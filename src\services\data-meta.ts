import { dataWrapper, requestUtil } from '@gwy/libs-web';
/**
 * 获取数据元列表
 */
export const getDatasourceList = () => dataWrapper(requestUtil.get('gwy-datasource/datasource/getDatasourceList'));

// 获取数元列表
export const getDataMetaList = () => dataWrapper(requestUtil.get('gwy-datasource/dataMeta/getDataMetaList'));

// 编辑数据单元标签副本
export const editTagPlan = (params) => dataWrapper(requestUtil.post('gwy-datasource/datasource/tag/plan/editTagPlan', params));
