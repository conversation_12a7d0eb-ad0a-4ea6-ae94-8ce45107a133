import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Select } from '@gwy/components-web';
import { useMemo } from 'react';
import { UseIn } from '../../const';

interface Iprops {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean; // 仅预览模式
  options?: any;
}
const AreaItem = (props: Iprops) => {
  const { useIn, tag, field, value, onChange, isPreview, options } = props;
  console.log('AreaItem---------', options, value, props);
  const [ops, initValue] = useMemo(() => {
    const { region_code, city_code, province_code, detail } = value?.[0] || {};
    const opt = options?.find((item) => {
      const { region_code: regionCode, city_code: cityCode, province_code: provinceCode, detail: dataDetail } = item?.data?.[0];
      if (regionCode === region_code && cityCode === city_code && provinceCode === province_code && dataDetail === detail) {
        return true;
      }
      return false;
    });

    const initValue = opt?.value || '';
    const ops = options?.map((opt) => ({
      lebel: opt?.value,
      value: opt?.value,
      data: opt.data,
    }));
    return [ops, initValue];
  }, [options, value]);
  return (
    <Select
      value={initValue}
      onChange={(e) => {
        onChange?.(options.find((option) => option.value === e)?.data);
      }}
      options={ops}
    />
  );
};

export default AreaItem;
