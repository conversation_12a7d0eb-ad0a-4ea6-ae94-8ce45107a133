import { NumberRangeType } from '@/const/metadata';
import { ResultType, ValueType } from '@/types/calc-field';

export const RESULT_TYPE_OPTIONS = [
  { label: '直接结果', value: ResultType.DIRECT_RESULT },
  { label: '阶梯模式', value: ResultType.CALCULATION_MODE },
];

export const VALUE_TYPE_OPTIONS = [
  { label: '固定值', value: ValueType.FIXED_VALUE },
  { label: '计算公式', value: ValueType.CALC_FORMULAS },
];

// 数值范围选项
export const SYMBOL_OPTIONS = [
  { label: '小于', value: NumberRangeType.lt },
  { label: '小于等于', value: NumberRangeType.lte },
  { label: '大于', value: NumberRangeType.gt },
  { label: '大于等于', value: NumberRangeType.gte },
  { label: '在范围内', value: NumberRangeType.between },
  { label: '在范围外', value: NumberRangeType.out },
];
