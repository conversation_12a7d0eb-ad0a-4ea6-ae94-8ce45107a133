import { AppDesignerContext } from '@/pages/app-designer/context';
import { useContext } from 'react';
import SectionHeader from '../../section-header';
import FieldsConfig from './fields-config';
import styles from './index.less';
import LayoutComponents from './layout-components';
import TagsConfig from './tags-config';

const ComponentsDesign = () => {
  const { groupFlag } = useContext(AppDesignerContext);

  return (
    <div className={styles.container}>
      <div>
        <SectionHeader title="布局组件" />
        <LayoutComponents />
      </div>
      <div>
        <SectionHeader title="标签库" />
        <TagsConfig />
      </div>
      {!groupFlag && (
        <div>
          {/* 字段库 */}
          <FieldsConfig />
        </div>
      )}
    </div>
  );
};

export default ComponentsDesign;
