import { Form, Input, Radio } from '@gwy/components-web';
import { useContext } from 'react';
import { AppType } from '../../../const';
import { AppDesignerContext } from '../../../context';
import { Default_Approve_Buttons, Default_Submit_Buttons } from '../../right-panel/form-design/button-options';
import SectionHeader from '../../section-header';
import styles from './index.less';
import MessageConfig from './message-config';
import SubFormConfig from './sub-form-config';

const BaseInfoConfig = () => {
  const {
    name,
    setName,
    formType,
    setFormType,
    appType,
    setAppType,
    wordReadonly,
    setWordReadonly,
    notifyConfig,
    setNotifyConfig,
    setButtonConfigs,
    setGroupFlag,
  } = useContext(AppDesignerContext);

  return (
    <div className={styles.container}>
      <SectionHeader title="表单信息" />
      <Form layout="vertical">
        <div>
          <div className={styles.sectionContent} style={{ paddingBottom: 0 }}>
            <Form.Item label="表单名称" required>
              <Input maxLength={20} placeholder="请输入表单名称" value={name} onChange={(e) => setName(e.target.value)} />
            </Form.Item>
            {/* <Form.Item hidden label="表单类型">
            <Radio.Group value={formType} onChange={(e) => setFormType(e.target.value)}>
              <Radio value={FormType.Normal}>普通表单</Radio>
            </Radio.Group>
          </Form.Item> */}
            <Form.Item label="表单类型" required>
              <Radio.Group
                value={appType}
                onChange={(e) => {
                  const appType = e.target.value;
                  setAppType(appType);
                  setNotifyConfig(null);
                  setButtonConfigs(appType === AppType.SUBMIT ? Default_Submit_Buttons : Default_Approve_Buttons);
                  if (appType === AppType.SUBMIT) {
                    setGroupFlag(false);
                  }
                }}
              >
                <Radio value={AppType.SUBMIT}>执行表单</Radio>
                <Radio value={AppType.Approve}>管理表单</Radio>
              </Radio.Group>
            </Form.Item>

            {/* 聚合表单 */}
            {appType === AppType.Approve && <SubFormConfig />}
          </div>
        </div>
      </Form>

      <div>
        <div className={styles.sectionContent} style={{ paddingTop: 0 }}>
          {appType === AppType.Approve && <MessageConfig value={notifyConfig} onChange={setNotifyConfig} />}
        </div>
      </div>
    </div>
  );
};

export default BaseInfoConfig;
