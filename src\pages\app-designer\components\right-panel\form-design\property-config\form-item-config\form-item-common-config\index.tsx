import { MetaTagType, MetaUnitType } from '@/const/metadata';
import { SelectedStateType } from '@/pages/app-designer/const';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Form, Input, Radio, Select, Switch } from '@gwy/components-web';
import { memo, useContext, useEffect } from 'react';
import DefaultValueSetting from './default-value-setting';
import styles from './index.less';

type Props = {
  tag?: DataUnitTagVO;
  field?: FormField;
};

const FormItemCommonConfig = memo(({ tag, field }: Props) => {
  const { selectedState } = useContext(AppDesignerContext);
  const form = Form.useFormInstance();

  const readonly = Form.useWatch('readonly', form);

  useEffect(() => {
    form.setFieldValue('readonly', field.readonly);
    form.setFieldValue('fieldName', field.fieldName);
    form.setFieldValue('placeholder', field.placeholder);
    form.setFieldValue('required', field.required);
    form.setFieldValue('widgetWith', field.widgetWith);
    form.setFieldValue('inputDefaultConfig', field.inputDefaultConfig);
  }, []);

  useEffect(() => {
    form.setFieldValue('readonly', field.readonly);
  }, [field.readonly]);

  return (
    <div className={styles.container}>
      {/* <div className={styles.header}>
        <div className={styles.title}>标签配置</div>
        <div className={styles.belong}>
          归属：{tag?.$$dataUnit?.categoryName || '通用'}-{tag?.$$dataUnit?.name}
        </div>
      </div> */}

      <Form.Item label="数据存储" name={['readonly']}>
        <Radio.Group
          disabled={tag?.$$dataUnit?.type === MetaUnitType.General || tag?.type === MetaTagType.System}
          onChange={() => {
            form.setFieldValue('required', undefined);
            form.setFieldValue('placeholder', undefined);
          }}
        >
          <Radio value={false}>输入</Radio>
          <Radio value={true}>输出</Radio>
        </Radio.Group>
      </Form.Item>

      {/* 默认数据 */}
      {!readonly && <DefaultValueSetting field={field} />}

      {!readonly && (
        <Form.Item label="必填校验" name="required" wrapperCol={{ style: { height: 0 } }}>
          <Switch style={{ position: 'absolute', top: -24, right: 0 }} />
        </Form.Item>
      )}
      <Form.Item
        label="标签别名"
        name="fieldName"
        tooltip="表单实际应用时看到的标签名称"
        rules={[{ validator: async (r, value) => (value && !value.trim() ? Promise.reject('禁止输入空白符') : Promise.resolve()) }]}
      >
        <Input placeholder="请输入" maxLength={20} showCount />
      </Form.Item>
      {!readonly && (
        <Form.Item label="输入提示" name="placeholder">
          <Input placeholder="请输入" maxLength={60} showCount />
        </Form.Item>
      )}
      {selectedState.type === SelectedStateType.baseGroupItem && (
        <Form.Item label="组件宽度" name="widgetWith">
          <Select
            allowClear={false}
            placeholder="请选择"
            options={[
              { label: '100%', value: '100%' },
              { label: '50%', value: '50%' },
            ]}
          />
        </Form.Item>
      )}
    </div>
  );
});

export default FormItemCommonConfig;
