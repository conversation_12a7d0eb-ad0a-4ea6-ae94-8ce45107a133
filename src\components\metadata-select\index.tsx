import { MetaDataVO } from '@/types/metadata';
import { Modal } from '@gwy/components-web';
import styles from './index.less';
import MetaDataList from './metadata-list';

type Props = {
  open?: boolean;
  onClose?: () => void;
  onSelect?: (item: MetaDataVO) => void;
};

const MetaDataSelect = ({ open, onClose, onSelect }: Props) => {
  return (
    <Modal size="small" title="数元库" footer={null} open={open} onCancel={onClose}>
      <div className={styles.container}>
        <MetaDataList onSelect={onSelect} />
      </div>
    </Modal>
  );
};

export default MetaDataSelect;
