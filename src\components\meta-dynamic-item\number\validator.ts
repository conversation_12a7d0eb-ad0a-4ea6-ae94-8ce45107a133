import { NumberRangeType, numberRangeTypeOptions } from '@/const/metadata';
import { MetaDataNumberDTO } from '@/types/metadata';

export const validator = (rule, value, config: Required<Pick<MetaDataNumberDTO, 'rangeType' | 'min' | 'max'>>) => {
  let { rangeType, min, max } = config;
  if (min && typeof min === 'string') {
    min = Number(min);
  }
  if (max && typeof max === 'string') {
    max = Number(max);
  }
  const val = Number(value);
  if (rangeType === NumberRangeType.unlimited || value === null || value === undefined) {
    return Promise.resolve();
  }
  if (typeof val !== 'number') {
    return Promise.reject('格式错误，请填写数值');
  }
  if (rangeType === NumberRangeType.lt || rangeType === NumberRangeType.lte) {
    if ((rangeType === NumberRangeType.lt && val < max) || (rangeType === NumberRangeType.lte && val <= max)) {
      return Promise.resolve();
    }
    return Promise.reject(`最大值需${numberRangeTypeOptions.find((n) => n.value === rangeType).label || ''}${max}`);
  }
  if (rangeType === NumberRangeType.gt || rangeType === NumberRangeType.gte) {
    if ((rangeType === NumberRangeType.gt && val > min) || (rangeType === NumberRangeType.gte && val >= min)) {
      return Promise.resolve();
    }
    return Promise.reject(`最小值需${numberRangeTypeOptions.find((n) => n.value === rangeType).label || ''}${min}`);
  }
  if (rangeType === NumberRangeType.between) {
    if (val >= min && val <= max) {
      return Promise.resolve();
    }
    return Promise.reject(`数值需在[${min}, ${max}]区间内`);
  }
  if (rangeType === NumberRangeType.out) {
    if (val < min || val > max) {
      return Promise.resolve();
    }
    return Promise.reject(`数值需在[${min}, ${max}]区间外`);
  }
  return Promise.resolve();
};
