.container {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  border: 1px solid #e5e6eb;
  border-radius: 2px;
  gap: 2px;
  cursor: pointer;
  background-color: #fff;

  &:hover {
    border-color: #4d7bf6;
  }

  &.active {
    border-color: #4d7bf6;
    background-color: #e8f3ff;
  }

  .name {
    flex-grow: 1;
    overflow: auto;
    font-size: 12px;
  }

  .right {
    display: flex;
    align-items: center;
    gap: 2px;
  }
}

.tagSource {
  flex-shrink: 0;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 16px;
  height: 16px;
  border-radius: 100%;
  font-size: 10px;
  color: #fff;
  background-color: #4d7bf6;
}
