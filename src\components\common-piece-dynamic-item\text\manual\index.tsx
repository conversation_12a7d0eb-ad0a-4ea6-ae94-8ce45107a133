import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Input } from '@gwy/components-web';
import { memo } from 'react';
import { UseIn } from '../../const';
import { WidgetType } from '../const';

const TextManualMeta = memo<{
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean;
}>(({ useIn, tag, field, value, onChange, isPreview }) => {
  const { placeholder } = field || {};
  const { widgetType } = field?.config || {};
  const { maxLength } = field?.config?.metaDataTextDTO || {};

  if (field?.readonly || isPreview) {
    if (useIn === UseIn.Designer) {
      return '回显数据';
    }
    const { value: tagValue, data: tagData } = value || {};
    return tagValue ?? '-';
  }

  return widgetType === WidgetType.SHORT ? (
    <Input
      maxLength={maxLength}
      placeholder={placeholder || '请输入'}
      value={value}
      onChange={(e) => {
        onChange(e.target.value);
      }}
      showCount
    />
  ) : (
    <Input.TextArea
      maxLength={maxLength}
      placeholder={placeholder || '请输入'}
      showCount
      value={value}
      onChange={(e) => {
        onChange(e.target.value);
      }}
    />
  );
});

export default TextManualMeta;
