import React, { useRef } from 'react';
import { useDrag } from 'react-dnd';

export enum DraggableType {
  Table = 'table',
  Tag = 'tag',
}

export enum DropResultName {
  Container = 'container',
  BaseGroup = 'baseGroup',
  CombineTag = 'combineTag',
  DataTable = 'data-table',
}

export type DropResult = {
  name: string;
  id?: string;
};

type Props<T = any> = {
  className?: string;
  type?: string;
  children?: React.ReactNode;
  item?: T;
  onDragEnd?: (item: T, dropResult: DropResult) => void;
};

const Draggable = <T extends any>({ className, type = 'tag', children, item, onDragEnd }: Props<T>) => {
  const onDragEndRef = useRef(onDragEnd);
  onDragEndRef.current = onDragEnd;

  const [{ isDragging }, drag] = useDrag(() => ({
    type,
    item,
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult<DropResult>();
      if (item && dropResult) {
        console.log(`You dropped ${item} into ${dropResult.name}!`);
        onDragEndRef.current?.(item, dropResult);
      }
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
      handlerId: monitor.getHandlerId(),
    }),
  }));

  return (
    <div ref={drag} className={className}>
      {children}
    </div>
  );
};

export default Draggable;
