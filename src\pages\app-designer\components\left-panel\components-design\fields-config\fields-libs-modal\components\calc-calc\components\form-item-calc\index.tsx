import { NumberRangeType } from '@/const/metadata';
import { CalcType, ValueType } from '@/types/calc-field';
import { CloseCircleFilled, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Form, InputNumber, Select, Space } from '@gwy/components-web';
import { useContext } from 'react';
import { FieldCreateContext } from '../../../field-create-modal/context';
import { SYMBOL_OPTIONS, VALUE_TYPE_OPTIONS } from '../form-item-condition/const';
import FormItemFormula from '../form-item-formula';
import styles from './index.less';

interface IProps {
  calculationModes: any[];
  disabled?: boolean;
  subPrefixName?: any[];
}

const FormItemCalc = (props: IProps) => {
  const { calculationModes, disabled, subPrefixName } = props;
  const { fieldType } = useContext(FieldCreateContext);
  return (
    <Form.List
      name={[...subPrefixName, 'calculationModes']}
      rules={[
        {
          validator: async (_, names) => {
            if (!names || names.length === 0) {
              return Promise.reject(new Error('请至少配置一个阶梯公式'));
            }
            return Promise.resolve();
          },
        },
      ]}
      initialValue={[{}]}
    >
      {(fields, { add, remove }) => (
        <>
          {fields.map(({ key, name }, idx) => {
            const { valueType, conditionFormulas, symbol, leftValue, rightValue } = calculationModes?.[name] || {};

            return (
              <div className={styles.item} key={key}>
                <div className={styles.itemContent}>
                  <Space>
                    <span style={{ verticalAlign: 10 }}>当</span>
                    <Form.Item name={[name, 'conditionFormulas', 'calcType']} initialValue={CalcType.FormulasCalc} hidden></Form.Item>
                    <Form.Item name={[name, 'conditionFormulas', 'fieldType']} initialValue={fieldType} hidden></Form.Item>
                    <Form.Item name={[name, 'conditionFormulas', 'formulasCalcs']} rules={[{ required: true, message: '请配置公式' }]}>
                      <FormItemFormula disabled={disabled} />
                    </Form.Item>
                    <Form.Item name={[name, 'symbol']}>
                      <Select disabled={disabled} style={{ width: 110 }} options={SYMBOL_OPTIONS} showSearch={false} allowClear={false} />
                    </Form.Item>
                    <Form.Item name={[name, 'leftValue']}>
                      <InputNumber disabled={disabled} style={{ width: 150 }} placeholder="请输入" />
                    </Form.Item>
                    {[NumberRangeType.between, NumberRangeType.out].includes(symbol as NumberRangeType) && (
                      <>
                        <span className={styles.separator}>-</span>
                        <Form.Item name={[name, 'rightValue']}>
                          <InputNumber disabled={disabled} style={{ width: 150 }} placeholder="请输入" />
                        </Form.Item>
                      </>
                    )}
                  </Space>
                  <br />
                  <Space>
                    <span style={{ verticalAlign: 10 }}>则结果= &nbsp;</span>
                    <Form.Item name={[name, 'valueType']} initialValue={ValueType.FIXED_VALUE}>
                      <Select disabled={disabled} style={{ width: 110 }} options={VALUE_TYPE_OPTIONS} showSearch={false} allowClear={false} />
                    </Form.Item>
                    {valueType === ValueType.FIXED_VALUE && (
                      <Form.Item name={[name, 'resultValue']}>
                        <InputNumber disabled={disabled} style={{ width: 150 }} placeholder="请输入" />
                      </Form.Item>
                    )}
                    {valueType === ValueType.CALC_FORMULAS && (
                      <>
                        <Form.Item name={[name, 'resultFormulas', 'calcType']} initialValue={CalcType.FormulasCalc} hidden></Form.Item>
                        <Form.Item name={[name, 'resultFormulas', 'fieldType']} initialValue={fieldType} hidden></Form.Item>
                        <Form.Item name={[name, 'resultFormulas', 'formulasCalcs']}>
                          <FormItemFormula disabled={disabled} />
                        </Form.Item>
                      </>
                    )}
                  </Space>
                </div>
                {fields.length > 1 && !disabled && (
                  <span
                    className={styles.deleteBtn}
                    onClick={() => {
                      remove(idx);
                    }}
                  >
                    <CloseCircleFilled style={{ fontSize: 16 }} />
                  </span>
                )}
              </div>
            );
          })}
          {!disabled && (
            <div className={styles.addBtnWrapper}>
              <Button color="primary" variant="link" onClick={() => add({})} icon={<PlusCircleOutlined style={{ fontSize: 16 }} />}>
                添加阶梯
              </Button>
            </div>
          )}
        </>
      )}
    </Form.List>
  );
};

export default FormItemCalc;
