import { buttonOptions, ButtonStyle } from '@/const/button-config';
import { FormButtonConfig } from '@/services/form-button';
import { Button, ButtonProps } from '@gwy/components-web';
import styles from './index.less';

type Props = {
  buttonConfigs?: FormButtonConfig[];
};

const ButtonConfigs = ({ buttonConfigs }: Props) => {
  return (
    <div className={styles.container}>
      {buttonConfigs?.length > 0 && (
        <div className={styles.buttonList}>
          {buttonConfigs?.map((btn, i) => {
            if (btn.buttonStyle) {
              const name = buttonOptions.find((b) => b.value === btn.operateType)?.label;
              let btnProps: ButtonProps = {};
              switch (btn.buttonStyle) {
                case ButtonStyle.Primary:
                  btnProps = { type: 'primary' };
                  break;
                case ButtonStyle.PrimaryGhost:
                  btnProps = { type: 'primary', ghost: true };
                  break;
                case ButtonStyle.Danger:
                  btnProps = { danger: true };
                  break;
              }
              return (
                <Button key={i} {...btnProps}>
                  {name}
                </Button>
              );
            }
          })}
        </div>
      )}

      {!buttonConfigs?.length && <div className={styles.empty}>点击该区域可配置表单按钮</div>}
    </div>
  );
};

export default ButtonConfigs;
