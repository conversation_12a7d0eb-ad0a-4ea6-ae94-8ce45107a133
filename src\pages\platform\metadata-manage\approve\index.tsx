import { useRoute } from '@/hooks';
import MetaDataEdit from '@/pages/platform/metadata-manage/components/metadata-edit-modal';
interface RouteState {
  record?: {
    bizParam?: string;
  };
  onClose?: () => void;
}
const ApproveMpdal = () => {
  const { state } = useRoute<RouteState>();
  const { record, onClose } = state || {};

  const { approveId } = JSON.parse(record.bizParam ?? '{}');

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <MetaDataEdit
        onCancel={() => onClose?.()}
        onOk={() => onClose?.()}
        operateType="approve"
        approveId={approveId}
        dataSourceType="NORMAL"
        editPermission
      />
    </div>
  );
};

export default ApproveMpdal;
