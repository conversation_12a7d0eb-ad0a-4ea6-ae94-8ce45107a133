import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { memo } from 'react';
import { UseIn } from '../const';
import ObjectItem from './object-select';

export type ObjectMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean;
  options?: any;
};

const ObjectMeta = memo(({ useIn, tag, field, value, onChange, isPreview, options }: ObjectMetaProps) => {
  if (field?.readonly || isPreview) {
    if (useIn === UseIn.Designer) {
      return '回显数据';
    }
    const { value: tagValue, data: tagData } = value || {};
    return tagData?.map((item) => item.value)?.join('、') ?? '-';
  }

  return <ObjectItem useIn={useIn} tag={tag} field={field} value={value} onChange={onChange} options={options} />;
});

export default ObjectMeta;
