import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Form } from '@gwy/components-web';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';
import CodeMeta from './index';
import { validator } from './validator';

export type CodeMetaFormProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  label?: React.ReactNode;
  prefix?: any[];
  isPreview?: boolean;
  options?: any;
};

const CodeMetaForm = memo(({ useIn, tag, field, label, prefix, isPreview, options }: CodeMetaFormProps) => {
  const { required, readonly, config } = field || {};
  const { metaDataCodeDTO } = config || {};

  const rules = useMemo(
    () =>
      !readonly && [UseIn.App].includes(useIn)
        ? [
            {
              validator: (rule, value) => validator(rule, value, metaDataCodeDTO, required),
            },
          ]
        : undefined,
    [required, metaDataCodeDTO, readonly],
  );

  return (
    <Form.Item label={label} name={[...(prefix || []), getMetaTagUniqueId(field), 'value']} rules={rules} required={required && !readonly}>
      <CodeMeta useIn={useIn} tag={tag} field={field} isPreview={isPreview} options={options} />
      {/* <Select placeholder="请选择" options={options.map(item => ({ value: item?.raw_value?.value, label: item?.raw_value?.value }))} onChange={e => {
                const value = options.find(item => item?.raw_value?.value === e);
                onchange(value);
            }}>
            </Select> */}
    </Form.Item>
  );
});

export default CodeMetaForm;
