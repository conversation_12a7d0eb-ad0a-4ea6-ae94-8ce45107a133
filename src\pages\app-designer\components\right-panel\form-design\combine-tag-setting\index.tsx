import { AppDesignerContext } from '@/pages/app-designer/context';
import { Form, Input } from 'antd';
import { useContext } from 'react';
import SectionHeader from '../../../section-header';
import styles from './index.less';

const CombineTagSetting = () => {
  const { combineTagConfig, setCombineTagConfig } = useContext(AppDesignerContext);

  return (
    <div>
      <div>
        <SectionHeader title="组合标签配置" />
        <div className={styles.sectionContent}>
          <Form layout="vertical">
            <Form.Item label={'标题名称'}>
              <Input
                placeholder="请输入"
                value={combineTagConfig?.combineTagName}
                onChange={(e) => {
                  setCombineTagConfig({
                    ...combineTagConfig,
                    combineTagName: e.target.value,
                  });
                }}
              />
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default CombineTagSetting;
