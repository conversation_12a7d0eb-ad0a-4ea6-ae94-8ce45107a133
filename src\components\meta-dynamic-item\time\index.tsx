import { CodeDateType, TimeRangeType } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import dayjs, { Dayjs } from 'dayjs';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';
import CustomDatePicker from './custom-date-picker';

export type TimeMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean;
};

export const getDisabledTime = (minDate: Dayjs, maxDate: Dayjs, dateForm: CodeDateType) => {
  if (![CodeDateType.date_hour, CodeDateType.date_hour_minute, CodeDateType.date_time, CodeDateType.time].includes(dateForm)) {
    return undefined;
  }
  const momentStart = minDate;
  const momentEnd = maxDate;
  const startHour = momentStart ? momentStart.hour() : 0;
  const startMinute = momentStart ? momentStart.minute() : 0;
  const startSecond = momentStart ? momentStart.second() : 0;
  const endHour = momentEnd ? momentEnd.hour() : 0;
  const endMinute = momentEnd ? momentEnd.minute() : 0;
  const endSecond = momentEnd ? momentEnd.second() : 0;
  const range = (start: number, length: number) => {
    return new Array(length).fill(0).map((_, index) => start + index);
  };
  return (date) => ({
    disabledHours: () => {
      const list = range(0, 24);
      let disabledHours: number[] = [];
      if (momentStart && momentStart.isSame(date, 'day')) {
        disabledHours = list.slice(0, startHour);
      }
      if (momentEnd && momentEnd.isSame(date, 'day')) {
        disabledHours.push(...list.slice(endHour));
      }
      return disabledHours;
    },
    disabledMinutes: (selectedHour) => {
      const list = range(0, 60);
      // 选择 年月日时 禁止 分的选择
      if ([CodeDateType.date_hour].includes(dateForm)) return list;

      let disabledMinutes: number[] = [];
      if (momentStart && momentStart.isSame(date, 'day') && selectedHour === startHour) {
        disabledMinutes = list.slice(0, startMinute);
      }
      if (momentEnd && momentEnd.isSame(date, 'day') && selectedHour === endHour) {
        disabledMinutes.push(...list.slice(endMinute));
      }
      return disabledMinutes;
    },
    disabledSeconds: (selectedHour, selectedMinute) => {
      const list = range(0, 60);
      // 选择 年月日时、年月日时分 禁止 秒的选择
      if ([CodeDateType.date_hour, CodeDateType.date_hour_minute].includes(dateForm)) return list;
      let disabledMinutes: number[] = [];
      if (momentStart && momentStart.isSame(date, 'day') && selectedHour === startHour && selectedMinute === startMinute) {
        disabledMinutes = list.slice(0, startSecond);
      }
      if (momentEnd && momentEnd.isSame(date, 'day') && selectedHour === endHour && selectedMinute === endMinute) {
        disabledMinutes.push(...list.slice(endSecond));
      }
      return disabledMinutes;
    },
  });
};

const TimeMeta = memo(({ useIn, tag, field, value, onChange, isPreview }: TimeMetaProps) => {
  const { placeholder, config } = field;
  const { tagMetaDataConfig } = tag;
  const { dateForm, metaDataDateTimeDTO } = config || {};
  const { start: metaStart, end: metaEnd } = tagMetaDataConfig.metaDataDateTimeDTO || {};

  const { rangeType, start, end } = metaDataDateTimeDTO || {};

  const minDate = useMemo(() => {
    const _metaStart = metaStart ? dayjs(metaStart) : undefined;
    const _start = start ? dayjs(start) : undefined;
    if (_metaStart && _start) {
      return _metaStart.isAfter(_start) ? _metaStart : _start;
    }
    return _start || _metaStart;
  }, [start, metaStart]);
  const maxDate = useMemo(() => {
    const _metaEnd = metaEnd ? dayjs(metaEnd) : undefined;
    const _end = end ? dayjs(end) : undefined;
    if (_metaEnd && _end) {
      return _metaEnd.isBefore(_end) ? _metaEnd : _end;
    }
    return _end || _metaEnd;
  }, [end, metaEnd]);

  const disabledTime = useMemo(() => getDisabledTime(minDate, maxDate, dateForm), [minDate, maxDate, dateForm]);

  if (field?.readonly || isPreview) {
    if (useIn === UseIn.Designer) {
      return '回显数据';
    }
    const { value: tagValue, data: tagData } = value || {};
    return tagValue ?? '-';
  }

  const render = () => {
    let options: {
      picker?: 'date' | 'month' | 'year';
      format?: string;
      showTime?: {
        format?: string;
      };
    } = {
      picker: 'date',
      format: 'YYYY-MM-DD HH:mm:ss',
      showTime: {
        format: 'HH:mm:ss',
      },
    };
    switch (dateForm) {
      case CodeDateType.year: {
        options = {
          picker: 'year',
        };
        break;
      }
      case CodeDateType.month:
      case CodeDateType.year_month: {
        options = {
          picker: 'month',
        };
        break;
      }
      case CodeDateType.day:
      case CodeDateType.date: {
        options = {
          picker: 'date',
        };
        break;
      }
      case CodeDateType.date_hour:
        options = {
          picker: 'date',
          format: 'YYYY-MM-DD HH',
          showTime: {
            format: 'HH',
          },
        };
        break;
      case CodeDateType.date_hour_minute:
        options = {
          picker: 'date',
          format: 'YYYY-MM-DD HH:mm',
          showTime: {
            format: 'HH:mm',
          },
        };
        break;
      case CodeDateType.date_time:
      case CodeDateType.time:
      default: {
        options = {
          picker: 'date',
          format: 'YYYY-MM-DD HH:mm:ss',
          showTime: {
            format: 'HH:mm:ss',
          },
        };
        break;
      }
    }

    return (
      <CustomDatePicker
        {...options}
        maxDate={maxDate}
        minDate={minDate}
        style={{ width: '100%' }}
        placeholder={placeholder || '请选择'}
        value={value}
        disabledTime={rangeType === TimeRangeType.unlimited ? undefined : disabledTime}
        onChange={onChange}
      />
    );
  };

  return render();
});

export default TimeMeta;
