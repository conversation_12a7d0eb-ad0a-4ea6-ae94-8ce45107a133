import { useBridge } from '@/hooks';
import { LayoutPage } from '@gwy/components-web';
import { useState } from 'react';
import SystemDataMetaList from './components/system-datameta-list';
import styles from './index.less';
// 普通数据元
export default () => {
  const [loading, setLoading] = useState(false);
  const bridge = useBridge();

  return (
    <LayoutPage
      footer={false}
      onCancel={() => {
        bridge.close();
      }}
      loading={loading}
    >
      <div className={styles.metadataContainer}>
        <SystemDataMetaList loading={loading} setLoading={setLoading} />
      </div>
    </LayoutPage>
  );
};
