import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Form } from '@gwy/components-web';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';
import FileMeta from './index';

export type FileMetaFormProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  label?: React.ReactNode;
  isPreview?: boolean;
};

const FileMetaForm = memo<FileMetaFormProps>(({ useIn, tag, field, label, isPreview }) => {
  const { required, readonly } = field || {};

  const rules = useMemo(
    () =>
      !readonly && [UseIn.App].includes(useIn)
        ? [
            {
              required: required,
              message: '请选择文件',
            },
          ]
        : undefined,
    [readonly, required, useIn],
  );

  return (
    <Form.Item label={label} name={[getMetaTagUniqueId(field), 'value']} rules={rules} required={required && !readonly}>
      <FileMeta useIn={useIn} tag={tag} field={field} isPreview={isPreview} />
    </Form.Item>
  );
});

export default FileMetaForm;
