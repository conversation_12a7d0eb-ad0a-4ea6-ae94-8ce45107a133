import { CDN_URL } from '@/const/common';
import { FileFormats } from '@/const/metadata';
import { MetadataManageTypes } from '@/types';
import { FormField, SourceType } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';

export const isCommonMeta = (item: any) => item?.property === MetadataManageTypes.DatasourcePropertyEnum.COMMON;

export const sortByTimeKey = (array, key, sort) => {
  // 创建副本避免修改原数组
  return array.slice().sort((a, b) => {
    // 转换为时间戳进行比较
    const val1 = new Date(a[key]).getTime();
    const val2 = new Date(b[key]).getTime();

    if (sort === 1) {
      // 升序排序（早的在前）
      return val1 - val2;
    } else if (sort === 2) {
      // 降序排序（新的在前）
      return val2 - val1;
    } else {
      return val1 - val2;
    }
  });
};

// 根据文件url获取文件图标
export const getFileIconByUrl = (url: string) => {
  const type = url.split('.').pop()?.toUpperCase();

  switch (type) {
    case FileFormats.ZIP:
    case FileFormats.RAR:
      return `${CDN_URL}/cg-gwy-platform/datasource/<EMAIL>`;
    case FileFormats.DOC:
    case FileFormats.DOCX:
      return `${CDN_URL}/cg-gwy-platform/datasource/<EMAIL>`;
    case FileFormats.XLS:
    case FileFormats.XLSX:
      return `${CDN_URL}/cg-gwy-platform/datasource/<EMAIL>`;
    case FileFormats.PDF:
      return `${CDN_URL}/cg-gwy-platform/datasource/<EMAIL>`;
    case FileFormats.PPT:
      return `${CDN_URL}/cg-gwy-platform/datasource/<EMAIL>`;
    default:
      return `${CDN_URL}/cg-gwy-platform/datasource/form-manage/file.png`;
  }
};

/**
 * 获取标签唯一标识
 *
 * 系统标签的tagId在不同数据单元下相同，需拼接数据单元id
 *
 * @param mainDataUnitId 主数据单元id
 */
export const getMetaTagUniqueId = (tag: DataUnitTagVO | FormField, mainDataUnitId?: number) => {
  if (tag.sourceType === SourceType.Field) {
    return (tag as FormField).fieldConfig?.fieldId;
  }
  return mainDataUnitId && tag.dataUnitId === mainDataUnitId ? tag.tagId : `${tag.dataUnitId}_${tag.tagId || tag.sourceId}`;
};

// 获取计算字段的显示名称
export const getCalcFieldDisplayName = (formField: FormField) => {
  return formField.fieldConfig?.formFieldConfig?.fieldOtherName || formField.fieldConfig?.fieldName;
};

// 获取表单字段的显示名称
export const getFormFieldDisplayName = (formField: FormField, tagsMap: Record<string, DataUnitTagVO>) => {
  if (formField.sourceType === SourceType.Field) {
    return getCalcFieldDisplayName(formField);
  }
  const uniqId = getMetaTagUniqueId(formField);
  const tag = tagsMap[uniqId];
  return formField.fieldName || tag?.name;
};
