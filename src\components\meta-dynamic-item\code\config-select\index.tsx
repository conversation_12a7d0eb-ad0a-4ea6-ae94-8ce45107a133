import CheckedFlag from '@/components/checked-flag';
import { codeDateTypeOptions, CodeNumberType, CustomCode, customCodeOptions } from '@/const/metadata';
import { MetaDataCodeConfigDTO } from '@/types/metadata';
import { DataUnitTagVO } from '@/types/tag';
import classNames from 'classnames';
import styles from './index.less';

type Props = {
  tag?: DataUnitTagVO;
  value?: MetaDataCodeConfigDTO[];
  onChange?: (value) => void;
};

const ConfigSelect = ({ tag, value, onChange }: Props) => {
  const { configList } = tag?.tagMetaDataConfig?.metaDataCodeDTO || {};

  const activeId = value?.[0]?.id;

  const renderElement = (element) => {
    switch (element.type) {
      case CustomCode.chinese:
      case CustomCode.symbol:
      case CustomCode.english_lower:
      case CustomCode.english_upper: {
        return (
          <>
            <span className={styles.elementLength}>位数：{element.maxLength}位</span>
            {element.text ? <span>{element.text}</span> : null}
          </>
        );
      }
      case CustomCode.number: {
        return (
          <>
            <span className={styles.elementLength}>位数：{element.maxLength}位</span>
            {element.numberForm ? (
              <span>形式：{element.numberForm === CodeNumberType.increment ? '自增' : '随机'}</span>
            ) : element.text ? (
              <span>{element.text}</span>
            ) : null}
          </>
        );
      }
      case CustomCode.date: {
        return <span className={styles.elementLength}>形式：{codeDateTypeOptions.find((item) => item.value === element.dateForm)?.label}</span>;
      }
      default: {
        return null;
      }
    }
  };

  return (
    <div className={styles.container}>
      {configList?.map((config, index) => {
        return (
          <div
            key={config.id}
            className={classNames(styles.configItem, {
              [styles.active]: activeId === config.id,
            })}
            onClick={() => {
              onChange?.(configList.filter((item) => item.id === config.id));
            }}
          >
            <div className={styles.groupHeader}>组{index + 1}</div>
            {config.elementList.map((element) => {
              return (
                <div key={element.id} className={styles.elementItem}>
                  <span className={styles.elementType}>{customCodeOptions.find((item) => item.value === element.type)?.label}：</span>
                  <span className={styles.elementValue}>{renderElement(element)}</span>
                </div>
              );
            })}

            {activeId === config.id && <CheckedFlag className={styles.checkedFlag} />}
          </div>
        );
      })}
    </div>
  );
};

export default ConfigSelect;
