import { MetadataManageTypes } from '@/types';
import { MenuOutlined, SwapOutlined } from '@ant-design/icons';
import { closestCenter, DndContext, DragEndEvent, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Popover } from '@gwy/components-web';
import { useRef, useState } from 'react';
import styles from './index.less';

interface SortableListProps {
  datasourceList: MetadataManageTypes.DataUnitVO[];
  onSubmit: (newList: MetadataManageTypes.DataUnitVO[]) => void;
  onReset: () => void;
  className?: string;
}

const SortableItem = ({ id, name }: { id: string | number; name: string }) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id });
  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: 'grab',
    userSelect: 'none',
    padding: '6px 12px',
    borderRadius: 4,
    background: '#fff',
    boxShadow: isDragging ? '0 0 0 2px #1677ff inset' : undefined,
  };
  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        {name}
        <MenuOutlined style={{ fontSize: 12 }} />
      </div>
    </div>
  );
};

const SortableList = ({ datasourceList, onSubmit, onReset, className }: SortableListProps) => {
  const [open, setOpen] = useState(false);
  const [items, setItems] = useState<MetadataManageTypes.DataUnitVO[]>([]);
  const originRef = useRef<MetadataManageTypes.DataUnitVO[]>([]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;
    setItems((prev) => {
      const oldIndex = prev.findIndex((item) => item._sortId === active.id);
      const newIndex = prev.findIndex((item) => item._sortId === over.id);
      return arrayMove(prev, oldIndex, newIndex);
    });
  };

  return (
    <Popover
      placement="bottom"
      trigger="click"
      open={open}
      onOpenChange={(val) => {
        setOpen(val);
        if (val) {
          setItems([...datasourceList]);
          originRef.current = [...datasourceList];
        }
      }}
      content={
        <div className={styles.container} onClick={(e) => e.stopPropagation()}>
          <div className={styles.content}>
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd} modifiers={[restrictToVerticalAxis]}>
              <SortableContext items={items.map((i) => i._sortId)} strategy={verticalListSortingStrategy}>
                {items.map((item) => (
                  <SortableItem key={item._sortId} id={item._sortId} name={item.name} />
                ))}
              </SortableContext>
            </DndContext>
          </div>
          <div className={styles.footer}>
            <Button
              size="middle"
              color="default"
              onClick={() => {
                onReset();
                setOpen(false);
              }}
            >
              重置
            </Button>
            <Button
              size="middle"
              type="primary"
              style={{ marginLeft: 8 }}
              onClick={() => {
                onSubmit(items);
                setOpen(false);
              }}
            >
              确定
            </Button>
          </div>
        </div>
      }
    >
      <div className={className ?? styles.sortItem} style={{ color: '#5881f5', cursor: 'pointer' }}>
        <SwapOutlined style={{ marginRight: 8, fontSize: 12 }} />
        自定义排序
      </div>
    </Popover>
  );
};

export default SortableList;
