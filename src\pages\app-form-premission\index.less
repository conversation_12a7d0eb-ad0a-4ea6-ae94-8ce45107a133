.layoutContent {
  padding: 0;
  overflow: hidden;

  .container {
    position: relative;
    height: 100%;
    overflow: hidden;

    .mainWrapper {
      width: 100%;
      height: 100%;
      overflow: hidden;
      display: flex;

      .leftWrapper {
        width: 270px;
        height: 100%;
        overflow: hidden;
        background: #f9fbff;
        border-right: 1px solid #e5e6eb;
        display: flex;
        flex-direction: column;
        flex-shrink: 0;

        .titleWrapper {
          font-weight: 600;
          padding: 10px 20px;
          display: flex;
          align-items: center;
          column-gap: 8px;
        }

        .btnWrapper {
          width: 100%;
          min-height: 70px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
          padding: 24px 20px;
          border-bottom: 1px solid #e5e6eb;

          .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 32px;
            background: #4d7bf6;
            border-radius: 2px;
            font-weight: 500;
            font-size: 14px;
            color: #fff;
            cursor: pointer;
          }
        }

        .menuFold {
          margin-right: 10px;
        }
      }

      .leftWrapperFold {
        width: 20px;
        position: relative;
        background-color: #fff;
      }

      .menuExpand {
        // text-align: left;
        transform: rotate(180deg);
        // transform: rotate(180deg);
        // transform: rotate(180deg);
        z-index: 1;
        position: absolute;
        width: 24px;
        top: 0;
        bottom: 0;
        left: 0;
        background: #fff;
        color: #1d2129;
        height: 24px;
        border-radius: 2px;
        text-align: center;
        line-height: 24px;
        // background: #f9fbff;
      }

      .rightWrapper {
        width: calc(100% - 270px);
        flex-grow: 1;
        height: 100%;
        background: #fff;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .tabsWrapper {
          padding: 8px 20px 0;

          :global {
            .ant-tabs-top > .ant-tabs-nav::before {
              border-bottom: none; // 去掉tab栏下面的横线
            }
          }
        }

        .contentWrapper {
          // flex: 1;
          overflow: hidden;
          height: 100%;
        }
      }
    }
  }
}

.container {
  display: flex;
  height: 100%;
  // padding: 0 20px;
  flex-direction: column;
  flex: 0;

  .segmented {
    // margin: 12px 0 16px 0;
    margin-bottom: 16px;
    padding: 0 20px;

    :global {
      .ant-segmented.ant-segmented-lg .ant-segmented-item-label {
        font-size: 14px;
        min-height: 42px;
        line-height: 42px;
      }

      .ant-segmented-item-selected {
        color: #4d7bf6;
      }
    }
  }

  .headerWrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    // margin-bottom: 6px;
    padding: 10px 16px;

    .leftBox {
      flex: 1;
      display: flex;
      align-items: center;

      .optBox {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .optBtn {
          width: 36px;
          margin-right: 10px;
          border: 1px solid #c9cdd4;
          border-radius: 2px;
          padding: 2px 0;
          display: flex;
          align-items: center;
          justify-content: space-evenly;

          .changeIcon {
            cursor: pointer;
            font-size: 18px;
            padding: 4px;
            color: #4e5969;
            transition: all 0.24s;
          }
        }
      }

      .changeType {
        width: 76px;
        margin-left: 20px;
        border: 1px solid #c9cdd4;
        border-radius: 2px;
        padding: 2px 0;
        display: flex;
        align-items: center;
        justify-content: space-evenly;

        .changeIcon {
          cursor: pointer;
          font-size: 18px;
          padding: 4px;
          color: #4e5969;
          transition: all 0.24s;
        }

        .activeIcon {
          color: #4d7bf6;
          background-color: #e8f3ff;
          border-radius: 4px;
        }

        &.w36 {
          width: 36px;
        }
      }
    }
  }
}

.content {
  position: relative;
  flex-grow: 1;
  overflow: hidden;
}

.footerBox {
  display: flex;
  padding: 8px 16px;
  flex-shrink: 0;
  align-items: center;
  justify-content: flex-end;
  column-gap: 15px;
  box-shadow: 0 -1px 6px 0 rgba(0, 0, 0, 10%);
  // margin: 0 -20px;
  :global {
    .ant-btn-primary {
      color: #fff;
      background: #4d7bf6;
    }

    .ant-btn-primary:hover,
    .ant-btn-primary:focus {
      color: #fff;
      background-color: #4d7bf6; /* 鼠标悬浮或聚焦时的背景颜色 */
      border-color: #4d7bf6; /* 鼠标悬浮或聚焦时的边框颜色 */
    }

    .ant-btn-primary[disabled],
    .ant-btn-primary[disabled]:hover,
    .ant-btn-primary[disabled]:focus,
    .ant-btn-primary[disabled]:active {
      color: rgba(0, 0, 0, 25%);
      border-color: #d9d9d9;
      background: #f5f5f5;
      text-shadow: none;
      box-shadow: none;
    }
  }
}

.viewButton {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  border: 1px solid #e5e6eb;
  justify-content: center;
  border-radius: 4px;

  &.active {
    border: 1px solid #4d7bf6;
  }

  img {
    width: 16px;
  }
}
