import { MetaUnitType, ObjectDTOType, objectDTOTypeOptions, ObjectRangeTypes } from '@/const/metadata';
import { metaDataAPI } from '@/services';
import { DataUnitTypes } from '@/types';
import { Form, FormInstance, Radio, Select } from '@gwy/components-web';
import { useContext, useEffect, useState } from 'react';
import { MetaDataContext } from '../../context';

export const ObjectDTOKey = 'metaDataObjectDTO';

type Props = {
  form?: FormInstance;
};

const ObjectConfig = ({ form }: Props) => {
  const { hasApproved, disabled } = useContext(MetaDataContext);

  const [relateDataUnits, setRelateDataUnits] = useState<DataUnitTypes.DataUnitBaseVO[]>([]);

  const generatedDataUnits = relateDataUnits.filter((item) => item.type === MetaUnitType.General);
  const normalDataUnits = relateDataUnits.filter((item) => item.type === MetaUnitType.Normal);

  const type = Form.useWatch([ObjectDTOKey, 'type'], form);
  const dataUnitId = Form.useWatch([ObjectDTOKey, 'dataUnitId'], form);
  const dataUnit = relateDataUnits.find((item) => item.dataUnitId === dataUnitId);

  const fetchRelateDataUnits = async () => {
    const data = await metaDataAPI.getRelateDataUnits();
    setRelateDataUnits(data);
  };

  useEffect(() => {
    fetchRelateDataUnits();
  }, []);

  return (
    <div>
      <Form.Item label="对象包含" name={[ObjectDTOKey, 'type']} rules={[{ required: true, message: '请选择' }]} initialValue={ObjectDTOType.GENERAL}>
        <Radio.Group
          disabled={hasApproved || disabled}
          options={objectDTOTypeOptions}
          onChange={() => {
            form.setFieldValue([ObjectDTOKey, 'dataUnitId'], undefined);
            form.setFieldValue([ObjectDTOKey, 'configList'], undefined);
            form.setFieldValue([ObjectDTOKey, 'generalType'], undefined);
          }}
        />
      </Form.Item>

      <Form.Item hidden name={[ObjectDTOKey, 'currentVersionId']} />
      <Form.Item name={[ObjectDTOKey, 'dataUnitId']} rules={[{ required: true, message: '请选择' }]}>
        <Select
          options={(type === ObjectDTOType.GENERAL ? generatedDataUnits : normalDataUnits).map((item) => ({
            label: `${item.name}（${item.identifierTagName}）`,
            value: item.dataUnitId,
          }))}
          onChange={(value) => {
            const dataUnit = relateDataUnits.find((item) => item.dataUnitId === value);
            form.setFieldValue([ObjectDTOKey, 'currentVersionId'], dataUnit?.currentVersionId);
            form.setFieldValue([ObjectDTOKey, 'generalType'], dataUnit?.generalType);
            form.setFieldValue([ObjectDTOKey, 'configList'], undefined);
          }}
        />
      </Form.Item>
      <Form.Item hidden name={[ObjectDTOKey, 'generalType']} />
      {type === ObjectDTOType.GENERAL && ObjectRangeTypes[dataUnit?.generalType] && (
        <>
          <Form.Item
            label={`配置${ObjectRangeTypes[dataUnit?.generalType]?.label}`}
            name={[ObjectDTOKey, 'configList']}
            rules={[{ required: true, message: '请选择' }]}
          >
            <Select mode="multiple" options={ObjectRangeTypes[dataUnit?.generalType]?.options} />
          </Form.Item>
        </>
      )}
    </div>
  );
};

export default ObjectConfig;
