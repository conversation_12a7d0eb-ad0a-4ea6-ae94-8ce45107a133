.container {
  :global {
    .ant-tabs {
      .ant-tabs-nav {
        &::before {
          display: none;
        }

        margin-bottom: 0;
        padding: 0;

        .ant-tabs-nav-wrap {
          .ant-tabs-nav-list {
            .ant-tabs-tab {
              background-color: #e5e6eb;
              color: #4e5969;
              border: 1px solid transparent;
              border-radius: 16px;
              padding: 2px 8px;

              &.ant-tabs-tab-active {
                background-color: #e8f3ff;
                color: #4d7bf6;
                border: 1px solid #4d7bf6;
              }
            }
          }
        }
      }

      .ant-tabs-nav-operations {
        display: none;
      }
    }
  }
}
