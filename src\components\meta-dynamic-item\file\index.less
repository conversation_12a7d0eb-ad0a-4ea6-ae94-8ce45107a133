.fileWrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .fileItem {
    display: flex;
    align-items: center;
    gap: 8px;

    img {
      width: 24px;
      height: 24px;
      object-fit: contain;
    }
  }
}

.uploadContainer {
  max-width: 100%;
}

:global {
  .ant-form-item.ant-form-item-has-error {
    :local {
      .uploadContainer {
        :global {
          .ant-btn {
            color: #ff4d4f !important;
            border-color: #ff4d4f !important;
          }
        }
      }
    }
  }
}
