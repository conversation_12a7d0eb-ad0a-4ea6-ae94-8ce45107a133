import { message, Modal } from '@gwy/components-web';
import { memo, useCallback, useEffect, useRef } from 'react';
import { TabType } from '../my-create-wrap';
import CustomAuth, { CustomAuthRef } from './custom-auth';
import styles from './index.less';

interface IProps {
  postId?: string;
  onCancle: () => void;
  onOk: () => void;
  type?: TabType;
}

const AuthDeptModal = memo<IProps>(({ postId, onCancle, onOk, type }) => {
  const localRef = useRef({
    posts: [],
    fields: [],
    storeMap: new Map(),
  });
  // const [orgList, setOrgList] = useState([]);

  const customAuthRef = useRef<CustomAuthRef>(null);

  useEffect(() => {
    localRef.current.storeMap.clear();
  }, []);

  // 岗位字段提交
  const handleSubmit = async () => {
    let tempArr = localRef.current.storeMap.values();
    // 将二维数组转为一维数组
    tempArr = Array.from(tempArr).reduce((acc, cur) => acc.concat(cur), []);
    console.log(tempArr, 'tempArr');

    // return;
    try {
      // const res = await dataSourceV3API.postFieldAuthSubmitApi({
      //   type: 1,
      //   postId,
      //   postFieldV2VOS: tempArr,
      // });
      message.success('保存成功');
      onOk();
    } catch (e) {
      console.log(e);
      message.error(`${e?.desc}`);
    }
  };

  const handleOk = async () => {
    // const { storeMap, posts, fields } = localRef.current;
    // // 对于批量授权需要
    // posts.forEach((postId) => {
    //   storeMap.set(
    //     postId,
    //     fields.map((ite) => ({ ...ite, currentPostId: postId })),
    //   );
    // });
    try {
      await customAuthRef.current?.handleSubmit();
      message.success('操作成功');
      onOk?.();
    } catch (err) {}
  };

  const onBatchChange = useCallback((posts, fields) => {
    localRef.current.posts = posts;
    localRef.current.fields = fields;
  }, []);

  return (
    <Modal
      open
      title="字段授权"
      onOk={handleOk}
      onCancel={onCancle}
      width={600}
      styles={{
        body: {
          padding: 0,
          height: 620 - 80,
        },
      }}
    >
      <div className={styles.authContainer}>
        {/* <BatchSetting onChange={onBatchChange} type={type} postId={postId} /> */}
        <CustomAuth ref={customAuthRef} type={type} postId={postId} />
      </div>
    </Modal>
  );
});

export default AuthDeptModal;
