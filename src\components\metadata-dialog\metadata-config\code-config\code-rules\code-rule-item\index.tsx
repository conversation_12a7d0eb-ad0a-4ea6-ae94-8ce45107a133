import { CodeDateType, codeDateTypeOptions, CodeNumberType, CustomCode, customCodeOptions } from '@/const/metadata';
import { genUuid } from '@/utils';
import { Form, FormInstance, Input, InputNumber, Select, Space } from '@gwy/components-web';
import { useMemo } from 'react';
import { CodeDTOKey } from '../..';
import NumberForm from './number-form';

type Props = {
  form?: FormInstance;
  prefix?: (string | number)[];
  fieldName?: number;
  disabled?: boolean;
  approvedHasItem?: boolean;
  isOrgMeta?: boolean;
};

const validator = async (rule, value, { type, maxLength }) => {
  if (maxLength && value) {
    let regExp;
    switch (type) {
      case CustomCode.chinese:
        regExp = new RegExp(`^[\\u4e00-\\u9fa5]{${maxLength}}$`);
        break;
      case CustomCode.symbol:
        regExp = new RegExp(`^[\\p{P}\\p{S}]{${maxLength}}$`, 'gu');
        break;
      case CustomCode.english_lower:
        regExp = new RegExp(`^[a-z]{${maxLength}}$`);
        break;
      case CustomCode.english_upper:
        regExp = new RegExp(`^[A-Z]{${maxLength}}$`);
        break;
      case CustomCode.number:
        regExp = new RegExp(`^[0-9]{${maxLength}}$`);
        break;
    }
    if (!regExp.test(value)) {
      return Promise.reject(`请输入${maxLength}位${customCodeOptions.find((item) => item.value === type)?.label || ''}`);
    }
  }
  return Promise.resolve();
};

const CodeRuleItem = ({ form, prefix, fieldName, disabled, approvedHasItem, isOrgMeta }: Props) => {
  const elementTypeList = Form.useWatch([CodeDTOKey, 'elementTypeList'], form);
  const elementTypeListOptions = customCodeOptions.filter((item) => elementTypeList?.includes(item.value));

  const type = Form.useWatch([...prefix, fieldName, 'type'], form);
  const numberForm = Form.useWatch([...prefix, fieldName, 'numberForm'], form);
  const maxLength = Form.useWatch([...prefix, fieldName, 'maxLength'], form);

  const codeOption = customCodeOptions.find((item) => item.value === type);

  const orgTimeOps = useMemo(() => codeDateTypeOptions.filter((item) => item.value !== CodeDateType.unlimited), []);
  // console.log(disabled, 'disabled------------')
  const renderItem = () => {
    switch (type) {
      case CustomCode.chinese:
      case CustomCode.symbol:
      case CustomCode.english_lower:
      case CustomCode.english_upper: {
        return (
          <Space>
            <Form.Item
              layout="horizontal"
              label="位数"
              name={[fieldName, 'maxLength']}
              rules={[{ required: true, message: '请输入' }]}
              initialValue={4}
            >
              <InputNumber disabled={disabled} style={{ width: 130 }} addonAfter="位" min={1} />
            </Form.Item>
            <Form.Item
              layout="horizontal"
              name={[fieldName, 'text']}
              rules={[{ validator: (rule, value) => validator(rule, value, { maxLength, type }) }]}
            >
              <Input disabled={disabled} placeholder={`请输入${maxLength}位${codeOption?.label}(选填)`} />
            </Form.Item>
          </Space>
        );
      }
      case CustomCode.number: {
        return (
          <div>
            <Form.Item layout="horizontal" label="选择形式" name={[fieldName, 'numberForm']}>
              <NumberForm disabled={disabled} />
            </Form.Item>
            <Space>
              <Form.Item
                layout="horizontal"
                label={`${codeOption?.label}位数`}
                name={[fieldName, 'maxLength']}
                rules={[{ required: true, message: '请输入' }]}
                initialValue={8}
              >
                <InputNumber disabled={disabled} style={{ width: 130 }} addonAfter="位" min={1} max={8} />
              </Form.Item>

              {[CodeNumberType.increment, CodeNumberType.random].includes(numberForm) ? (
                <Form.Item>
                  <span style={{ fontSize: 12, color: '#4E5969' }}>输入不可超过{maxLength}位限制</span>
                </Form.Item>
              ) : (
                <Form.Item
                  layout="horizontal"
                  name={[fieldName, 'text']}
                  rules={[{ validator: (rule, value) => validator(rule, value, { maxLength, type }) }]}
                >
                  <Input disabled={disabled} placeholder={`请输入${maxLength}位${codeOption?.label}(选填)`} />
                </Form.Item>
              )}
            </Space>
          </div>
        );
      }
      case CustomCode.date: {
        return (
          <Form.Item layout="horizontal" label="选择形式" name={[fieldName, 'dateForm']} rules={[{ required: true, message: '请选择时间格式' }]}>
            <Select disabled={disabled} options={orgTimeOps} />
          </Form.Item>
        );
      }
      default: {
        return null;
      }
    }
  };

  return (
    <div>
      <Form.Item hidden name={[fieldName, 'id']} initialValue={genUuid()} />

      <Form.Item layout="horizontal" name={[fieldName, 'type']} rules={[{ required: true, message: '请选择类型' }]}>
        <Select disabled={disabled} style={{ width: '50%' }} options={elementTypeListOptions} />
      </Form.Item>

      {renderItem()}
    </div>
  );
};

export default CodeRuleItem;
