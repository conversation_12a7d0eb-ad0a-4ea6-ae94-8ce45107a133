import { APPROVE_ENUM } from '@/const/datasource';

// 类型定义
interface VersionItem {
  version: number; // 版本号
  versionStr: string; // 版本字符串
  approveStatus: number; // 审核状态
}

export enum DatasourcePropertyEnum {
  NORMAL, // 普通数据单元
  COMMON, // 通用数据单元
}
export enum DatasourceCateTypeEnum {
  NORMAL, // 普通数据单元
  COMMON, // 通用数据单元
}
export type DatasourceVO = {
  id: string;
  datasourceName: string;
  description: string;
  isGroup: boolean;
  open: boolean;
  approveStatus: APPROVE_ENUM;
  version: number;
  versionStr: string;
  property: number;
  dataBelongs: number[];
  operateAuth: boolean;
  createdUserId: string;
  createdUserName: string;
  createdTime: string;
  updatedUserId: string;
  updatedUserName: string;
  updatedTime: string;
  uniqueTagId: string;
  uniqueTagName: string;
  tagNumber: number;
  versions: VersionItem[];
  iconColor: string;
  ifBlock: boolean;
  datasourceTagVOS?: any[];
  // 前端自定义的属性
  isDropdownOpen: boolean;
  versionsLoading: boolean;
  isVersionDropdownOpen: boolean;
};

export type DataUnitVO = {
  id: number | string; // 数据单元id
  name: string; // 数据单元名称
  enable: boolean; // 是否启用
  status: string; // 数据单元状态 STASH INVALID; PENDING_APPROVAL; APPROVED; REJECTED;
  createTime: string; // 创建时间
  createUser: string; // 创建人
  tagNum: number; // 标签数量
  currentVersion: string; // 当前版本
  currentVersionId: number | string; // 当前版本id
  versions: any[]; // 版本列表
  updateTime: string; // 更新时间
  dataUnitId?: number | string;
  // 父级数据单元id
  parentDataUnitId?: number | string;
  // 前端自定义的属性
  isDropdownOpen: boolean;
  versionsLoading: boolean;
  isVersionDropdownOpen: boolean;
  [key: string]: any;
};
export type DatasourceCate = {
  id: string;
  name: string;
  type: DatasourceCateTypeEnum;
  datasourceVOS: DatasourceVO[];
};
export type Tag = {
  tagId: string;
  tagName: string;
  tagDesc: string;
  createTime: string;
  updateTime: string;
};
export type Group = {
  groupId: string;
  groupName: string;
  groupDesc: string;
  isGroup: boolean;
  createTime: string;
  lastUpdateTime: string;
  datasourceList?: DatasourceVO[];
  datasourceIds: string[];
};
export type DatasourceDetail = DatasourceVO & {
  setRefreshSignal: () => void;
};
export function isGroup(item: Group | DatasourceVO): item is Group {
  return (item as Group).isGroup === true;
}
