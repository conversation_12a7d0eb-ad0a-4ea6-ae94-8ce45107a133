export enum TABS_LIST {
  create = 'create', // 我创建的
  auth = 'auth', // 被授权的
  company = 'company', // 其他岗位创建的(公司级表单)
  execute = 'execute', // 执行岗创建的
  otherCompany = 'otherCompany', // 其他公司表单
  blocRetake = 'blocRetake', // 公司收权
}

export enum TAB_TYPE {
  create = 0, // 创建的表单
  auth, // 被授权的表单
  company, // 公司级表单
  normal, // 普通应用表单
  companyApp, // 公司级应用表单
  group = 6, // 组合表单
  // allcanuseapp, // 全部可用表单内的应用
}
