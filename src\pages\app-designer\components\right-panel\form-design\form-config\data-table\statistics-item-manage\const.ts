/**
 * 函数类型
 */

export enum FUNCTION_TYPE {
  AVERAGE = 'AVERAGE',
  MAX = 'MAX',
  MIN = 'MIN',
  ROUND = 'ROUND',
  SUM = 'SUM',
  COUNT = 'COUNT',
  DISTINCT_COUNT = 'DISTINCT_COUNT',
  DAYS = 'DAYS_COUNT',
  CEIL = 'CEIL',
  FLOOR = 'FLOOR',
  MONTH = 'MONTH',
}

export const CALC_FIELD_FUNC_OPTIONS = [
  { label: 'SUM求和', value: FUNCTION_TYPE.SUM },
  { label: 'AVERAGE平均值	', value: FUNCTION_TYPE.AVERAGE },
  { label: 'MAX最大值	', value: FUNCTION_TYPE.MAX },
  { label: 'MIN最小值	', value: FUNCTION_TYPE.MIN },
  { label: 'COUNT计数	', value: FUNCTION_TYPE.COUNT },
  { label: 'DISTINCT_COUNT去重计算	', value: FUNCTION_TYPE.DISTINCT_COUNT },
];

export enum CalcSymbol {
  ADD = '+',
  SUBTRACT = '-',
  MULTIPLY = '*',
  DIVIDE = '/',
}

export const CalcList = [
  { label: '+', value: CalcSymbol.ADD },
  { label: '-', value: CalcSymbol.SUBTRACT },
  { label: '*', value: CalcSymbol.MULTIPLY },
  { label: '/', value: CalcSymbol.DIVIDE },
];
