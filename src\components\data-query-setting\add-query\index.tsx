import FilterGroup from '@/components/filter-group';
import { Form } from '@gwy/components-web';
import { useEffect } from 'react';

interface Iprops {
  additionalConditionGroups?: any;
  tags?: any;
  canRelateTags?: any;
  calcFields?: any;
  onChange?: (value: any) => void;
  conditionGroups?: any;
}
const AddQuery = (props: Iprops) => {
  const { additionalConditionGroups, tags, canRelateTags, calcFields, onChange, conditionGroups = [] } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    if (additionalConditionGroups) {
      onChange?.({
        additionalConditionGroups: {
          conditionGroups: additionalConditionGroups,
        },
      });
    }
  }, [additionalConditionGroups]);
  const onValuesChange = (changedFields, allFields) => {
    onChange?.(allFields);
  };

  return (
    <Form form={form} onValuesChange={onValuesChange}>
      <FilterGroup
        prefixNames={['additionalConditionGroups']}
        conditionGroups={additionalConditionGroups}
        tags={tags}
        canRelateTags={canRelateTags}
        calcFields={calcFields}
        orderStart={conditionGroups?.length}
      />
    </Form>
  );
};

export default AddQuery;
