import FieldCreateModal from '@/pages/app-designer/components/left-panel/components-design/fields-config/fields-libs-modal/components/field-create-modal';
import { EditOutlined } from '@ant-design/icons';
import { Button } from '@gwy/components-web';
import { useState } from 'react';

interface Props {
  postId?: string;
  configuredDataUnits?: any[];
  allDataUnitsWithTags?: any[];
  fieldList?: any[];
  value?: any;
  onChange?: (value?: any) => void;
}

const FieldCreateModalWrapper = ({ postId, configuredDataUnits, allDataUnitsWithTags, fieldList, value, onChange }: Props) => {
  const [fieldCreateModal, setFieldCreateModal] = useState<{
    open?: boolean;
  }>({
    open: false,
  });

  return (
    <>
      <Button
        type="link"
        icon={<EditOutlined />}
        style={{ padding: 0 }}
        onClick={() => {
          setFieldCreateModal({ open: true });
        }}
      >
        修改
      </Button>
      {fieldCreateModal.open && (
        <FieldCreateModal
          postId={postId}
          configuredDataUnits={configuredDataUnits}
          allDataUnitsWithTags={allDataUnitsWithTags}
          fieldList={fieldList}
          record={value}
          onCancel={() => setFieldCreateModal({ open: false })}
          onOk={(values) => {
            setFieldCreateModal({ open: false });
            onChange?.(values);
          }}
        />
      )}
    </>
  );
};

export default FieldCreateModalWrapper;
