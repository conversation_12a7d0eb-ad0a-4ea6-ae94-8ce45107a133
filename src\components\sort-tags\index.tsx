import { CloseCircleFilled, PlusCircleOutlined } from '@ant-design/icons';
import { Form, Select } from '@gwy/components-web';
import { useMemo } from 'react';
import { DATA_TYPE } from '../filter-group/filter-const';
import styles from './index.less';

type SortTagType = 'asc' | 'desc';

const getLabelByTagDataType = (type: SortTagType, tagDataType: string) => {
  switch (tagDataType) {
    case DATA_TYPE.TEXT.val:
      return type === 'asc' ? '从A到Z' : '从Z到A';
    case DATA_TYPE.NUMBER.val:
      return type === 'asc' ? '从小到大' : '从大到小';
    case DATA_TYPE.DATETIME.val:
      return type === 'asc' ? '从旧到新' : '从新到旧';
    default:
      return type === 'asc' ? '升序' : '降序';
  }
};

interface sortTagsProps {
  dataUnitId: number;
  tags: any[];
  disabled?: boolean;
}

const SortTags = (props: sortTagsProps) => {
  const { dataUnitId, tags, disabled = false } = props;
  const sortTags = Form.useWatch('sortTags');
  const tagOptions = useMemo(() => {
    // 过滤掉非 TEXT, NUMBER, DATETIME 类型的标签
    return tags
      .filter((tag) => [DATA_TYPE.TEXT.val, DATA_TYPE.NUMBER.val, DATA_TYPE.DATETIME.val].includes(tag.tagMetaDataConfig?.type))
      .map((tag) => ({
        label: tag.name,
        value: tag.tagId,
        // 将已选中的标签项设置为 disabled
        disabled: sortTags?.some((sortTag) => sortTag?.tagId === tag?.tagId),
      }));
  }, [tags, sortTags]);

  const getLabelByTag = (type: SortTagType, tagId: string) => {
    const tag = tags.find((tag) => tag.tagId === tagId);
    const { tagMetaDataConfig } = tag || {};
    const { type: tagDataType } = tagMetaDataConfig || {};
    return getLabelByTagDataType(type, tagDataType);
  };

  const getSortTagOptions = (tagId: string) => {
    return tagId
      ? [
          { label: getLabelByTag('asc', tagId), value: true },
          { label: getLabelByTag('desc', tagId), value: false },
        ]
      : null;
  };

  return (
    <Form.List name="sortTags">
      {(fields, { add, remove }) => (
        <div className={styles.sortTagsWrapper} style={{ marginTop: 12 }}>
          {fields.map((field, idx) => (
            <div key={idx}>
              <div className={styles.sortTagsItem}>
                <div className={styles.left}>
                  <Form.Item name={[field.name, 'dataUnitId']} hidden initialValue={dataUnitId}></Form.Item>
                  <Form.Item name={[field.name, 'tagId']} label="排序标签">
                    <Select
                      style={{ width: 168 }}
                      disabled={disabled}
                      options={tagOptions}
                      onChange={(value) => {
                        console.log(value);
                      }}
                    />
                  </Form.Item>
                  <Form.Item name={[field.name, 'asc']} label="排序方式">
                    <Select
                      style={{ width: 168 }}
                      disabled={disabled}
                      notFoundContent={<div style={{ color: '#999' }}>请先选择排序标签</div>}
                      options={getSortTagOptions(sortTags[field.name]?.tagId) as any}
                    />
                  </Form.Item>
                </div>
                <div className={styles.right}>
                  {fields.length > 1 && !disabled && (
                    <span
                      className={styles.deleteBtn}
                      onClick={() => {
                        remove(idx);
                      }}
                    >
                      <CloseCircleFilled style={{ fontSize: 16 }} />
                    </span>
                  )}
                </div>
              </div>
            </div>
          ))}
          {!disabled && (
            <div className={styles.addBtn} onClick={() => add()}>
              <PlusCircleOutlined style={{ fontSize: 16 }} />
              <span>添加排序规则</span>
            </div>
          )}
        </div>
      )}
    </Form.List>
  );
};

export default SortTags;
