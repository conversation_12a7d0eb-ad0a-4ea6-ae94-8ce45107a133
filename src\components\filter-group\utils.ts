import { DATA_TYPE, FILTER_VALUE_LIST, FILTER_VALUE_TYPE, NEW_RELATION_TYPE, NUMBER_FILTER_VALUE_LIST } from './filter-const';

export const getFilterValueOptionsByTag = (tag) => {
  const { type } = tag?.tagMetaDataConfig || {};

  if (type === DATA_TYPE.NUMBER.val) {
    // 数值类型的标签才有【字段变量】选项
    return NUMBER_FILTER_VALUE_LIST;
  }
  return FILTER_VALUE_LIST;
};
export const getFilterValueOptions = (tag, relation) => {
  // 当 relation 为 等于 时，才会有标签变量选项
  const options = getFilterValueOptionsByTag(tag);

  if (relation !== NEW_RELATION_TYPE.EQUAL.val) {
    return options.filter((n) => n.value !== FILTER_VALUE_TYPE.VARIABLE);
  }
  return options;
};

export const getMetaDataObjectConfigList = (tag) => {
  const { metaDataObjectDTO } = tag?.tagMetaDataConfig || {};
  const { configList } = metaDataObjectDTO || {};
  return configList;
};
