.container {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  gap: 2px;
}

.triangleUp,
.triangleDown {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-radius: 3px;
}

.triangleUp {
  border-bottom: 5px solid #ccc;
}

.triangleDown {
  border-top: 5px solid #ccc;
}

.initial {
  border-bottom-color: #ccc !important;
  border-top-color: #ccc !important;
}

.active {
  border-bottom-color: #2196f3 !important;
  border-top-color: #2196f3 !important;
}
