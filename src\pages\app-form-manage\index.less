.layoutContent {
  padding: 0;
  background: #fff;

  .header {
    width: calc(100% - 30px);
    height: 130px;
    background: linear-gradient(134deg, #f4fbff 0%, #fff 16%);
    box-shadow: 0 2px 4px 0 rgba(232, 232, 232, 25%);
    border-radius: 4px;
    border: 1px solid #e8f3ff;
    display: flex;
    margin: 10px 15px;
    padding: 10px 15px;
    gap: 20px;

    .headerLeft {
      flex: 1;

      .body {
        display: grid;
        // grid-template-columns: repeat(2, 1fr);
        grid-template-columns: repeat(4, 1fr);
        gap: 10px 40px;

        .main {
          display: flex;
          align-items: center;
          gap: 8px;
          white-space: nowrap;
          min-width: 0;

          img {
            // width: 20px;
            margin-right: 5px;
          }

          .content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex: 1;
            cursor: pointer;
            min-width: 0;

            .name {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-weight: 500;
              font-size: 14px;
              color: #4e5969;
              margin-right: 5px;
            }

            .time {
              font-weight: 400;
              font-size: 12px;
              color: #86909c;
              white-space: nowrap;
            }
          }
        }
      }

      .bodyEmpty {
        font-weight: 400;
        font-size: 12px;
        color: #c9cdd4;
        line-height: 14px;
        text-align: center;
      }
    }

    .dividerLine {
      height: calc(100% + 20px);
      top: -10px;
    }

    .headerRight {
      flex: 1;

      .body {
        display: flex;
        gap: 60px;

        .main {
          display: flex;
          align-items: center;
          gap: 10px;
          cursor: pointer;

          img {
            height: 44px;
          }

          .btnLabel {
            font-weight: bold;
            font-size: 14px;
            line-height: 20px;
            word-break: keep-all;
          }
        }
      }
    }

    .headerTitle {
      font-size: 16px;
      line-height: 19px;
      margin: 6px 0 20px;
      font-weight: bold;
      color: #4d7bf6;
      display: flex;

      .bdLeft {
        display: inline-block;
        width: 3px;
        height: 14px;
        margin-right: 7px;
        background: #4d7bf6;
      }
      // border-left: 3px solid #4d7bf6;
    }

    .btnLabel {
      display: inline-block;

      @media screen and (max-width: 1024px) {
        width: 30px;
      }
    }
  }

  .container {
    position: relative;
    height: calc(100% - 150px);
    overflow: hidden;

    .mainWrapper {
      width: 100%;
      height: 100%;
      overflow: hidden;
      display: flex;

      .leftWrapper {
        width: 270px;
        height: 100%;
        overflow: hidden;
        border-right: 1px solid #e5e6eb;
        display: flex;
        flex-direction: column;
        background: #f9fbff;
        flex-shrink: 0;

        .titleWrapper {
          font-weight: 600;
          padding: 10px 20px;
          display: flex;
          align-items: center;
          column-gap: 8px;
        }

        .btnWrapper {
          width: 100%;
          min-height: 70px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
          padding: 24px 20px;
          border-bottom: 1px solid #e5e6eb;

          .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 32px;
            background: #4d7bf6;
            border-radius: 2px;
            font-weight: 500;
            font-size: 14px;
            color: #fff;
            cursor: pointer;
          }
        }
      }

      .leftWrapperFold {
        width: 20px;
        position: relative;
        background: #fff;
      }

      .menuExpand {
        // text-align: left;
        transform: rotate(180deg);
        // transform: rotate(180deg);
        // transform: rotate(180deg);
        z-index: 1;
        position: absolute;
        width: 24px;
        top: 0;
        bottom: 0;
        left: 0;
        // background: #fff;
        color: #1d2129;
        height: 24px;
        border-radius: 2px;
        text-align: center;
        line-height: 24px;
        background: #f9fbff;
      }

      .rightWrapper {
        flex-grow: 1;
        // width: 100%;
        height: 100%;
        background: #fff;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .tabsWrapper {
          padding: 8px 20px 0;

          :global {
            .ant-tabs-top > .ant-tabs-nav::before {
              border-bottom: none; // 去掉tab栏下面的横线
            }
          }
        }

        .contentWrapper {
          flex: 1;
          overflow: hidden;
        }
      }
    }
  }
}
