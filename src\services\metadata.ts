import { MetaDataVO } from '@/types/metadata';
import { dataWrapper, requestUtil } from '@gwy/libs-web';

/**
 * 获取数元列表
 */
export const getMetadataList = (): Promise<MetaDataVO[]> => dataWrapper(requestUtil.get('gwy-datasource/api/meta_datas'));

/**
 * 获取数元详情
 */
export const getMetadataDetail = (id): Promise<MetaDataVO> => dataWrapper(requestUtil.get(`gwy-datasource/api/meta_datas/${id}`));

/**
 * 获取数元历史版本列表
 */
export const getMetadataHistoryList = (id): Promise<MetaDataVO[]> => dataWrapper(requestUtil.get(`gwy-datasource/api/meta_datas/${id}/versions`));

/**
 * 获取可关联的数据单元列表
 */
export const getRelateDataUnits = () => dataWrapper(requestUtil.get(`gwy-datasource/api/data_units/relates`));
