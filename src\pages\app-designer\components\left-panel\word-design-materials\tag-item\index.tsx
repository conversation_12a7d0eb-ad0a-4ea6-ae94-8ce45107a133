import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Tag, TextEllipsisTooltip, Tooltip } from '@gwy/components-web';
import classNames from 'classnames';
import styles from './index.less';

type Props = {
  tag?: DataUnitTagVO;
  field?: FormField;
};

const TagItem = ({ tag, field }: Props) => {
  return (
    <div
      className={classNames(styles.container, {
        // [styles.active]: active,
      })}
    >
      <span className={styles.name}>
        <TextEllipsisTooltip text={field?.fieldName || tag?.name} />
        {field?.fieldName && (
          <Tooltip title={`原标签名称：${tag?.name}`}>
            <InfoCircleOutlined style={{ marginLeft: 5, color: '#4D7BF6' }} />
          </Tooltip>
        )}
      </span>
      {!field.readonly ? (
        <Tag color="blue" style={{ margin: 0 }}>
          输入
        </Tag>
      ) : (
        <Tag color="orange" style={{ margin: 0 }}>
          输出
        </Tag>
      )}
    </div>
  );
};

export default TagItem;
