import { Col, Form, Row } from '@gwy/components-web';

interface Iprops {
  form: any;
  formAppDetail: any;
}
const BasicSetting = (props: Iprops) => {
  const { form, formAppDetail } = props;
  const getFormRuleRender = (formAppDetail) => {
    const mainRule = (formAppDetail.dataUnits || []).find((item) => item.mainDataUnit);
    const otherRules = (formAppDetail.dataUnits || []).filter((item) => !item.mainDataUnit);
    const otherRuleText = otherRules.map((item) => item.name).join('、');
    let text = mainRule?.name ? `${mainRule?.name}(主)` : '';
    if (otherRules.length > 0) {
      text += `、${otherRuleText}(副)`;
    }
    return <div>{text}</div>;
  };
  return (
    <>
      <div>
        <Form form={form}>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item label="表单类型">
                <span>普通表单</span>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="应用类型">
                <span>{formAppDetail.appType === 'SUBMIT' ? '发起表单' : '审核表单'}</span>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="数据单元">{getFormRuleRender(formAppDetail)}</Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    </>
  );
};

export default BasicSetting;
