import { SelectedStateType } from '@/pages/app-designer/const';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { ConfigProvider } from '@gwy/components-web';
import { useContext } from 'react';
import BaseGroupSetting from './base-group-setting';
import ButtonOptions from './button-options';
import CombineTagSetting from './combine-tag-setting';
import DataTableSetting from './data-table-setting';
import FormConfig from './form-config';
import styles from './index.less';
import PropertyConfig from './property-config';

const FormDesign = () => {
  const { selectedState, propertyConfigRef } = useContext(AppDesignerContext);

  return (
    <div className={styles.container}>
      <div className={styles.left}>
        <FormConfig />
      </div>

      <ConfigProvider theme={{ token: { fontSize: 12 } }}>
        <div className={styles.right}>
          {/* 基础分组设置 */}
          {selectedState.type === SelectedStateType.baseGroup && <BaseGroupSetting />}
          {/* 组合标签设置 */}
          {selectedState.type === SelectedStateType.combineTag && <CombineTagSetting />}
          {/* 数据表格设置 */}
          {selectedState.type === SelectedStateType.table && <DataTableSetting />}

          {/* 按钮设置 */}
          {selectedState.type === SelectedStateType.buttonList && <ButtonOptions />}

          {/* 标签属性设置 */}
          {[SelectedStateType.baseGroupItem, SelectedStateType.combineTagItem, SelectedStateType.tableItem].includes(selectedState.type) && (
            <PropertyConfig ref={propertyConfigRef} />
          )}
        </div>
      </ConfigProvider>
    </div>
  );
};

export default FormDesign;
