.container {
  padding: 8px;
  background-color: #f7f8fa;
  border: 1px dashed #c9cdd4;

  :global {
    .ant-form-item {
      flex-grow: 1;
    }

    .ant-form-item-row {
      flex-direction: row !important;
    }

    .ant-form-item-label {
      display: flex;
      align-items: center;
    }
  }
}

.itemBox {
  display: flex;
  justify-content: space-between;
  column-gap: 8px;

  .iconDel {
    align-self: flex-start;
    margin-top: 8px;
    font-size: 16px;
    color: #c9cdd4;
  }
}
