import { metaDataAPI } from '@/services';
import { MetaDataVO } from '@/types/metadata';
import { Timeline } from '@gwy/components-web';
import { useEffect, useState } from 'react';
import styles from './index.less';

type Props = {
  metaDataId: number;
};

const MetadataHistory = ({ metaDataId }: Props) => {
  const [historyList, setHistoryList] = useState<MetaDataVO[]>([]);

  const fetchHistory = async () => {
    const data = await metaDataAPI.getMetadataHistoryList(metaDataId);
    setHistoryList(data);
  };

  useEffect(() => {
    fetchHistory();
  }, []);

  return (
    <div className={styles.container}>
      <Timeline
        items={historyList.map((item) => ({
          children: (
            <div className={styles.item}>
              <span>版本号：</span>
              <span>{item.version}</span>
              <span style={{ marginLeft: '24px' }}>{item.createTime || '_'}</span>
            </div>
          ),
        }))}
      />
    </div>
  );
};

export default MetadataHistory;
