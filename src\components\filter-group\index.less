.filterGroup {
  padding: 0 4px;

  .ruleItem {
    :global {
      .ant-form-item {
        margin-bottom: 0;
      }
    }

    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    color: #1d2129;
    line-height: 22px;

    .ruleLabel {
      font-weight: 600;
    }

    .ruleValue {
      margin-left: 6px;
      font-weight: 400;
    }
  }

  .ruleTitle {
    font-weight: 600;
    color: #1d2129;
  }
}

.conditionInnerWrapper {
  position: relative;
  margin-top: 16px;
  padding-left: 30px;
  border-left: 1px solid #c9cdd4;

  &::before,
  &::after {
    content: '';
    position: absolute;
    left: 0;
    width: 15px;
    height: 1px;
    transform: translateX(-50%);
    background-color: #c9cdd4;
  }

  &::before {
    top: 0;
  }

  &::after {
    bottom: 0;
  }

  .logicText {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 36px;
    left: -1px;
    top: 50%;
    transform: translateY(-50%) translateX(-50%);
    color: #c9cdd4;
    font-size: 12px;
    font-weight: 400;
    background-color: #eef6ff;
  }
}

.itemWrapper {
  .itemContent {
    background: #fff;
    padding: 10px;

    .groupTop {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      .left {
        flex: 1;

        .groupTitle {
          font-weight: bold;
          color: #2a2f37;
        }
      }

      .right {
        display: flex;
        align-items: center;
        column-gap: 16px;
      }
    }
  }

  :global {
    :where(.css-dev-only-do-not-override-drsnig).ant-space-gap-col-small {
      column-gap: 15px;
    }
  }
}

.addBtn,
.addGroupBtn {
  color: #1677ff;

  span {
    margin-left: 5px;
  }

  :hover {
    cursor: pointer;
  }
}

.addGroupBtn {
  padding: 8px 0;
  text-align: center;
  border: 1px dashed #4d7bf6;
  border-radius: 2px;
}

.addBtnWrapper {
  width: 100%;
  text-align: center;
  background: #fff;
  height: 118px;
  line-height: 118px;
}

.deleteBtn {
  span {
    color: #c9cdd4;
    cursor: pointer;
  }

  &:hover {
    span {
      color: #aaa;
    }
  }
}
