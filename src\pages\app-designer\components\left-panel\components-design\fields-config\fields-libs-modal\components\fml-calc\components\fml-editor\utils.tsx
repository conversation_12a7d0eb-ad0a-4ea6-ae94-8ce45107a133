import { DATA_TYPE } from '@/components/filter-group/filter-const';
import { ExpressionType } from '@/types/calc-field';
import { CalculatorOutlined, EditOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { isEmpty } from 'lodash';
import { FUNC_OPTIONS } from './const';
import styles from './index.less';

/**
 * 渲染函数表达式
 */
export const renderFunc = (item, handleClick?, index?) => {
  if (item.expressionType === ExpressionType.FUNC) {
    const func = FUNC_OPTIONS.find((f) => f.value === item.refFunction?.functionType);
    const args = item.refFunction?.arguments || [];
    return (
      <>
        <span>{`${func?.label}( `}</span>
        {args.map((arg, i) => {
          // 如果是标签并且配置了筛选条件则高亮
          let isLightTag = false;
          if (arg.expressionType === ExpressionType.TAG) {
            if (!isEmpty(arg?.refTag?.conditionGroups)) {
              isLightTag = true;
            }
          }
          return (
            <>
              <span
                key={i}
                className={classNames({
                  [styles.lightTag]: isLightTag,
                  [styles.tag]: arg.expressionType === ExpressionType.TAG,
                  [styles.field]: arg.expressionType === ExpressionType.FUNC_CALC,
                })}
                onClick={() => {
                  handleClick &&
                    handleClick({
                      obj: arg,
                      tagPathname: [index, 'refFunction', 'arguments', i],
                    });
                }}
              >
                {arg.expressionType === ExpressionType.TAG && <EditOutlined />}
                {arg.expressionType === ExpressionType.FUNC_CALC && <CalculatorOutlined />}
                {arg.showValue}
              </span>
              {i < args.length - 1 ? ' , ' : ''}
            </>
          );
        })}
        <span>{' )'}</span>
      </>
    );
  }
  return '';
};

/**
 * 渲染公式
 */
export const renderFormula = (disabled, value?, handleClick?) => {
  return (value || []).map((item, i) => {
    // 如果是标签并且配置了筛选条件则高亮
    let isLightTag = false;
    if (item.expressionType === ExpressionType.TAG) {
      if (!isEmpty(item?.refTag?.conditionGroups)) {
        isLightTag = true;
      }
    }

    if (item.expressionType === ExpressionType.FUNC) {
      return <span key={i}>{renderFunc(item, handleClick, i)}</span>;
    }
    return (
      <span
        key={i}
        className={classNames({
          [styles.lightTag]: isLightTag,
          [styles.tag]: item.expressionType === ExpressionType.TAG,
          [styles.field]: item.expressionType === ExpressionType.FUNC_CALC,
        })}
        onClick={() => {
          if (disabled && !isLightTag) return;
          handleClick &&
            handleClick({
              obj: item,
              tagPathname: [i],
            });
        }}
      >
        {item.expressionType === ExpressionType.TAG && <EditOutlined />}
        {item.expressionType === ExpressionType.FUNC_CALC && <CalculatorOutlined />}
        {item.expressionType === ExpressionType.SYMBOL ? ` ${item.showValue} ` : item.showValue}
      </span>
    );
  });
};

/**
 * 函数的 showValue（纯文本类型）
 */
export const getFuncShowValue = (item) => {
  if (item.expressionType === ExpressionType.FUNC_CALC) {
    const func = FUNC_OPTIONS.find((f) => f.value === item.refFunction?.functionType);
    return `${func?.label}(${(item.refFunction?.arguments || []).map((c) => c.showValue).join(',')})`;
  }
  return '';
};

/**
 * 是否是数值类型、单位类型标签
 */
export const isNumberOrUnitTag = (tag) => {
  const { type } = tag?.tagMetaDataConfig || {};
  return [DATA_TYPE.NUMBER.val, DATA_TYPE.UNIT.val].includes(type);
};
