import { deleteCalc<PERSON>ield } from '@/services/calc-field';
import { CalcFieldVO, CalcType, FieldType } from '@/types/calc-field';
import { Post } from '@/types/post';
import { message, Modal, Tabs } from '@gwy/components-web';
import { memo, useRef, useState } from 'react';
import MyCreateWrap, { TabType } from './components/my-create-wrap';
import PreviewLibFields from './components/preview-lib-fields';
import styles from './index.less';

interface IProps {
  post: Post;
  configuredDataUnits: any[];
  allDataUnitsWithTags: any[];
  onOk?: (calcIds) => void;
  onCancel?: () => void;
  hideUse?: boolean;
}
export const calcTypeMap = {
  [CalcType.FormulasCalc]: '公式计算',
  [CalcType.Calculation]: '阶梯计算',
};

export const fieldTypeMap = {
  [FieldType.FormField]: '表单字段',
  [FieldType.IndependentField]: '独立字段',
};

const FieldsLibsModal = memo<IProps>(({ post, configuredDataUnits, allDataUnitsWithTags, onOk, onCancel, hideUse = false }) => {
  const myCreateWrapRef = useRef(null);
  const [activeKey, setActiveKey] = useState<string>('0');

  // 我创建的选择列表
  const [myCreaCheckteList, setMyCreateCheckList] = useState<any>([]);
  // 被授权的选择列表
  const [authorizedCheckList, setAuthorizedCheckList] = useState<any>([]);

  // 是否展示字段详情弹窗
  const [isPreview, setIsPreivew] = useState({
    field: {},
    open: false,
  });
  const handleDel = (field: CalcFieldVO, refresh = () => {}) => {
    Modal.confirm({
      centered: true,
      title: '是否确定删除该字段',
      onOk: async () => {
        try {
          await deleteCalcField(field.fieldId);
          message.success('删除成功');
          refresh && refresh();
        } catch (e) {}
      },
    });
  };

  const items = [
    {
      label: '我创建的',
      key: '0',
      children: (
        <MyCreateWrap
          type={TabType.create}
          postId={post.postId}
          ref={myCreateWrapRef}
          checkList={myCreaCheckteList}
          setCheckList={setMyCreateCheckList}
          canDelete
          setIsPreivew={setIsPreivew}
          handleDel={handleDel}
          configuredDataUnits={configuredDataUnits}
          allDataUnitsWithTags={allDataUnitsWithTags}
        />
      ),
    },
    {
      label: '被授权的',
      key: '1',
      children: (
        <MyCreateWrap
          type={TabType.auth}
          postId={post.postId}
          checkList={authorizedCheckList}
          setCheckList={setAuthorizedCheckList}
          setIsPreivew={setIsPreivew}
          handleDel={handleDel}
        />
      ),
    },
  ].filter((item) => !!item);

  return (
    <>
      <Modal
        open
        title="字段库"
        okText="使用"
        okButtonProps={{
          type: 'primary',
          disabled: ![...myCreaCheckteList, ...authorizedCheckList]?.length,
          hidden: hideUse,
        }}
        onOk={() => onOk([...myCreaCheckteList, ...authorizedCheckList])}
        onCancel={onCancel}
        width={760}
        styles={{
          body: {
            height: 680 - 80,
          },
        }}
      >
        <div className={styles.container}>
          <Tabs
            type="card"
            size="small"
            defaultActiveKey="0"
            items={items}
            activeKey={activeKey}
            onChange={(key) => {
              setActiveKey(key);
            }}
          />
        </div>
      </Modal>

      {isPreview?.open && (
        <PreviewLibFields
          onCancel={() => setIsPreivew((prev) => ({ ...prev, open: false }))}
          onOk={() => setIsPreivew((prev) => ({ ...prev, open: false }))}
          fieldItem={isPreview.field}
          post={post}
          configuredDataUnits={configuredDataUnits}
          allDataUnitsWithTags={allDataUnitsWithTags}
        />
      )}
    </>
  );
});

export default FieldsLibsModal;
