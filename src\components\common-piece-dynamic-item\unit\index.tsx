import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Select } from '@gwy/components-web';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';

export type UnitMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  styles?: React.CSSProperties;
  isPreview?: boolean;
  options?: any;
  label?: React.ReactNode;
};

const UnitMeta = memo<UnitMetaProps>(({ useIn, tag, field, value, onChange, styles, isPreview, options }) => {
  const { raw_value, raw_unit } = value || {};
  const { config, readonly, placeholder } = field || {};
  const { configList } = config?.metaDataUnitDTO || {};
  const onValueVhange = (data: { raw_value?; raw_unit? }) => {
    typeof onChange === 'function' &&
      onChange({
        raw_value,
        raw_unit,
        // ...data,
      });
  };
  console.log(options, value, 'options---- inunit');
  const [ops, initValue] = useMemo(() => {
    const opt = options?.find((opt) => opt?.data?.raw_value === raw_value && opt?.data?.raw_unit === raw_unit) || {};
    const initValue = opt?.value || '';
    const ops = options?.map((opt) => ({
      lebel: opt?.value,
      value: opt?.value,
      data: opt.data,
    }));
    return [ops, initValue];
  }, [options, value]);

  return (
    <Select
      value={initValue}
      onChange={(e) => {
        onChange?.(options.find((option) => option.value === e)?.data);
      }}
      options={ops}
    />
  );
});

export default UnitMeta;
