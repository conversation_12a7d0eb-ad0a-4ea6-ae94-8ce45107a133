import { CodeDTOKey } from '@/components/metadata-dialog/metadata-config/code-config';
import { CodeType, CommonCode, commonCodeOptions } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Checkbox, Form } from '@gwy/components-web';
import { useEffect } from 'react';
import ConfigSelect from './config-select';

type Props = {
  tag?: DataUnitTagVO;
  field?: FormField;
};

const CodePropertySetting = ({ tag, field }: Props) => {
  const { type, elementTypeList } = tag?.tagMetaDataConfig?.metaDataCodeDTO || {};

  const form = Form.useFormInstance();

  useEffect(() => {
    form.setFieldsValue(field.config);
  }, []);

  return (
    <div>
      <Form.Item hidden name={[CodeDTOKey, 'type']} />

      {type === CodeType.custom && (
        <Form.Item label="选择编码规则" name={[CodeDTOKey, 'configList']}>
          <ConfigSelect tag={tag} />
        </Form.Item>
      )}

      {type === CodeType.normal && (
        <Form.Item label="选择编码规则" name={[CodeDTOKey, 'elementTypeList']} rules={[{ required: true, message: '请选择' }]}>
          <Checkbox.Group options={commonCodeOptions.filter((item) => (elementTypeList as CommonCode[])?.includes(item.value))} />
        </Form.Item>
      )}
    </div>
  );
};

export default CodePropertySetting;
