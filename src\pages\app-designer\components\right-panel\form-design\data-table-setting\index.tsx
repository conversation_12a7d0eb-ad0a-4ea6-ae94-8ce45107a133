import { AppDesignerContext } from '@/pages/app-designer/context';
import { Form, Input } from 'antd';
import { useContext } from 'react';
import SectionHeader from '../../../section-header';
import DataUnitSelect from '../data-unit-select';
import StatisticsItemManage from '../form-config/data-table/statistics-item-manage';
import styles from './index.less';

const DataTableSetting = () => {
  const { tableList, setTableList, selectedState } = useContext(AppDesignerContext);
  const table = tableList?.find((t) => t.id === selectedState.tableId);

  if (!table) return null;

  return (
    <div>
      <div>
        <SectionHeader title="数据单元配置" />
        <div className={styles.sectionContent}>
          <DataUnitSelect type="table" />
        </div>
      </div>
      <div>
        <SectionHeader title="数据表格配置" />
        <div className={styles.sectionContent}>
          <Form layout="vertical">
            <Form.Item label={'标题名称'}>
              <Input
                placeholder="请输入"
                value={table.tableName}
                onChange={(e) => {
                  setTableList(
                    tableList.map((t) => {
                      if (t.id === table.id) {
                        return {
                          ...t,
                          tableName: e.target.value,
                        };
                      }
                      return t;
                    }),
                  );
                }}
              />
            </Form.Item>

            {/* 统计项 */}
            <StatisticsItemManage table={table} />
          </Form>
        </div>
      </div>
    </div>
  );
};

export default DataTableSetting;
