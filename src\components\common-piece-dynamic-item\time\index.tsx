import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Select } from 'antd';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';

export type CodeMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean; // 仅预览模式
  options?: any;
};

const TimeMeta = memo(({ useIn, tag, field, value, onChange, isPreview, options }: CodeMetaProps) => {
  console.log(options, value, 'options------------');
  // const { }
  const initValue = typeof value === 'string' ? value : value?.raw_value;
  console.log(initValue, value, 'initValue------------');
  // const [componetValue, setComponentValue] = useState(initValue)
  const renderOps = useMemo(() => {
    return (options || [])?.map((item) => {
      return {
        label: item.value,
        value: item.value,
      };
    });
  }, [options]);
  return (
    <Select
      value={initValue}
      options={renderOps}
      placeholder="请选择"
      onChange={(e) => {
        const sendvalue = options?.find((item) => item?.value === e);
        console.log(sendvalue, 'sendvalue------------');
        // setComponentValue(sendvalue?.value)
        onChange(sendvalue?.data);
      }}
      // onChange={(e) => {
      //     const value = options?.find((item) => item?.value === e);
      //     onChange?.(value);
      // }}
      //   onChange={(e) => {
      //     const value = options?.find((item) => item?.value === e);
      //     onChange?.(value?.data);
      //   }}
    />
  );
});

export default TimeMeta;
