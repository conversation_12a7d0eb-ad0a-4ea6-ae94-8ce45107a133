import { MetaUnitType } from '@/const/metadata';
import { datasourceAPI } from '@/services';
import { getDatasourceCategoryList } from '@/services/datasource';
import { DataUnitTypes } from '@/types';
import { Col, Form, Input, Row, Select } from '@gwy/components-web';
import { forwardRef, ForwardRefRenderFunction, useEffect, useImperativeHandle, useMemo, useState } from 'react';

type IRef = object;
enum OPERATE_TYPE {
  EDIT = 'edit',
  ADD = 'add',
  VIEW = 'view',
  APPROVE = 'approve',
}
type Iprops = {
  initialValue: any;
  dataSourceType: 'NORMAL' | 'GENERAL';
  operateType?: 'add' | 'edit' | 'approve' | 'view';
  onRelateDataUnitChange?: (dataUnit: DataUnitTypes.DataUnitBaseVO) => void;
  datasourceTypes?: any[];
};
type dataSourceOps = { label: string; value: string | number; key: string | number; [key: string]: any };
const BasicInfo: ForwardRefRenderFunction<IRef, Iprops> = (props: Iprops, ref) => {
  const { initialValue, dataSourceType, operateType, datasourceTypes, onRelateDataUnitChange } = props;
  const [form] = Form.useForm();
  const [dataSourceCateOps, setDataSourceOps] = useState<dataSourceOps[]>([]);

  const [generalDataUnits, setGeneralDataUnits] = useState<DataUnitTypes.DataUnitBaseVO[]>([]);
  const relateDataUnitId = Form.useWatch('dataUnitId', form);
  const parentDataUnitId = Form.useWatch('parentDataUnitId', form);
  const relateDataUnit = generalDataUnits.find((item) => item.dataUnitId === relateDataUnitId);
  useEffect(() => {
    onRelateDataUnitChange?.(relateDataUnit);
  }, [relateDataUnit]);

  const fetchRelateDataUnits = async () => {
    const params = {
      type: MetaUnitType.General,
    };
    const data = await datasourceAPI.getDatasourceList(params);
    setGeneralDataUnits(data);
  };
  useEffect(() => {
    fetchRelateDataUnits();
  }, []);

  useImperativeHandle(ref, () => ({
    getValues: async () => {
      const formData = form.getFieldsValue();
      try {
        const values = await form.validateFields();
        return {
          ...(initialValue || {}),
          ...(values || {}),
        };
      } catch (e: any) {
        const { name, categoryId } = formData;
        if (!name) {
          return Promise.reject('当前数据单元名称未填写，请填写后再次提交');
        }
        if (!categoryId) {
          return Promise.reject('未选择分类，请填写后再次提交');
        }
        const { errorFields } = e || {};
        if (errorFields?.length > 0 && Array.isArray(errorFields)) {
          return Promise.reject(errorFields[0].errors[0]);
        }
        return Promise.reject(e);
      }
    },
  }));
  const getDataSourceOps = async () => {
    const data = await getDatasourceCategoryList({
      type: dataSourceType,
    });
    setDataSourceOps(
      data.map((item) => ({
        label: item.categoryName,
        value: item.categoryId,
        key: item.categoryId,
      })),
    );
  };

  useEffect(() => {
    if (initialValue) {
      form.setFieldsValue(initialValue);
    }
    getDataSourceOps();
  }, [initialValue]);

  const datasourceTypeOptions = useMemo(
    () =>
      [{ label: '默认', value: null, currentVersionId: null }].concat(
        datasourceTypes.map((ite) => ({ label: ite.name, value: ite.dataUnitId, currentVersionId: ite.currentVersionId })),
      ),
    [datasourceTypes],
  );

  return (
    <div>
      <Form form={form} labelCol={{ style: { width: '110px' } }} wrapperCol={{ style: { width: 'calc(100% - 110px)' } }}>
        <Row gutter={24}>
          <Col span={12}>
            {operateType === OPERATE_TYPE.ADD ? (
              <Form.Item name="name" label="数据单元名称" rules={[{ required: true, message: '请输入数据元名称' }]}>
                <Input placeholder="请输入数据元名称" maxLength={20} />
              </Form.Item>
            ) : (
              <Form.Item label="数据单元名称">
                <span className="ant-form-text">{initialValue?.name}</span>
              </Form.Item>
            )}
          </Col>
          {operateType === OPERATE_TYPE.ADD ? (
            <Col span={12}>
              <Form.Item label="数据单元分组" name="categoryId" rules={[{ required: true, message: '请选择数据单元分组' }]}>
                <Select placeholder="请选择数据单元分组" options={dataSourceCateOps}></Select>
              </Form.Item>
            </Col>
          ) : (
            <Col span={12}>
              <Form.Item label="数据单元分组">
                <span className="ant-form-text">{initialValue?.categoryName}</span>
              </Form.Item>
            </Col>
          )}
          {operateType === OPERATE_TYPE.ADD ? (
            <Col span={12}>
              <Form.Item name="parentDataUnitId" label="数据单元类型" initialValue={null}>
                <Select
                  options={datasourceTypeOptions}
                  onChange={(v) => {
                    form.setFieldValue('parentDataUnitCurrentVersionId', datasourceTypeOptions.find((it) => it.value === v)?.currentVersionId);
                    if (v) {
                      form.setFieldValue('dataUnitId', undefined);
                    }
                  }}
                  disabled={!!relateDataUnitId}
                />
              </Form.Item>
              <Form.Item name="parentDataUnitCurrentVersionId" hidden />
            </Col>
          ) : (
            <Col span={12}>
              <Form.Item label="数据单元类型">-</Form.Item>
            </Col>
          )}
          {operateType === OPERATE_TYPE.ADD ? (
            <Col span={12}>
              <Form.Item name="dataUnitId" label="关联数据单元">
                <Select
                  options={generalDataUnits.map((item) => ({
                    label: item.name,
                    value: item.dataUnitId,
                  }))}
                  disabled={!!parentDataUnitId}
                  onChange={(v) => {
                    if (v) {
                      form.setFieldValue('parentDataUnitId', null);
                    }
                  }}
                />
              </Form.Item>
            </Col>
          ) : (
            <Col span={12}>
              <Form.Item label="关联数据单元">-</Form.Item>
            </Col>
          )}

          {/* <Col span={12}>
            <Form.Item name="allowCustomLabel" label="数据单元权限" valuePropName="checked" initialValue={false}>
              <Checkbox>不允许公司自定义标签</Checkbox>
            </Form.Item>
          </Col> */}
          <Col span={24}>
            {operateType === OPERATE_TYPE.ADD ? (
              <Form.Item label="数据单元描述" name="description">
                <Input.TextArea placeholder="请输入数据单元描述" rows={4} maxLength={300} />
              </Form.Item>
            ) : (
              <Form.Item label="数据单元描述" name="description">
                <span className="ant-form-text">{initialValue?.description}</span>
              </Form.Item>
            )}
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default forwardRef(BasicInfo);
