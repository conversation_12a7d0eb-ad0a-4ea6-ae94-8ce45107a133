import { datasourceDataAPI } from '@/services';
import { Modal, Table, TableColumnsType } from '@gwy/components-web';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import DataListWrapper from '../data-list-wrapper';
import styles from './index.less';

type Props = {
  title?: string;
  dataUnitId?: string;
  lightDataUnitId?: string;
  dataUnitVersionId?: number;
  onClose?: () => void;
};

const OrgListModal = ({ title, dataUnitId, lightDataUnitId, dataUnitVersionId, onClose }: Props) => {
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  const [data, setData] = useState([]);
  const [total, setTotal] = useState(data.length);

  const [orgId, setOrgId] = useState('');
  const org = data.find((item) => item.orgId === orgId);

  const fetchOrgList = useCallback(async () => {
    const { records, total } = await datasourceDataAPI.getDataStatisticsOrgPage(dataUnitId, {
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
    setData(records);
    setTotal(total);
  }, [dataUnitId, pagination]);

  useEffect(() => {
    fetchOrgList();
  }, [fetchOrgList]);

  const columns: TableColumnsType<any> = [
    {
      title: '序号',
      width: 85,
      dataIndex: 'idx',
      render: (value, record, index) => index + 1,
    },
    {
      title: '公司名称',
      width: 'auto',
      dataIndex: 'orgName',
    },
    {
      title: '数据总量',
      width: 'auto',
      dataIndex: 'dataNum',
    },
    {
      title: '最近存储时间',
      width: 'auto',
      dataIndex: 'lastTime',
      render: (value) => {
        return <div>{value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>;
      },
    },
    {
      title: '操作',
      width: 'auto',
      dataIndex: 'actions',
      render: (value, record) => (
        <a
          onClick={() => {
            setOrgId(record.orgId);
          }}
        >
          查看
        </a>
      ),
    },
  ];

  return (
    <Modal title={title} open footer={null} onCancel={onClose} size="large">
      <div className={styles.content}>
        {!orgId && (
          <Table
            bordered
            size="small"
            columns={columns}
            dataSource={data}
            scroll={{ x: 'max-content' }}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total,
              onChange: (page, pageSize) => {
                setPagination((pre) => ({ ...pre, current: page, pageSize }));
              },
            }}
          />
        )}

        {orgId && (
          <DataListWrapper
            dataUnitId={dataUnitId}
            lightDataUnitId={lightDataUnitId}
            dataUnitVersionId={dataUnitVersionId}
            orgId={orgId}
            orgName={org?.orgName}
            showBack
            onClickBack={() => setOrgId('')}
          />
        )}
      </div>
    </Modal>
  );
};

export default OrgListModal;
