import { InfoCircleOutlined } from '@ant-design/icons';
import { Collapse } from '@gwy/components-web';
import styles from '../../index.less';
import ApprovePendingInfo from '../approve-pending-info';
import BasicSetting from '../basic-setting';
import FormProperty from '../form-property';
import Formtrigger from '../form-trigger';
interface Iprops {
  form: any;
  formAppDetail: any;
}
const FormRule = (props: Iprops) => {
  // const [form] = Form.useForm();
  const { form, formAppDetail } = props;
  return (
    <div className={styles.collapseWrap}>
      <Collapse ghost defaultActiveKey={['$form_property_$', '$basic_setting_$', '$trigger_event_$', '$approve_setting_$']}>
        <Collapse.Panel header="表单属性" key="$form_property_$">
          <FormProperty form={form} formAppDetail={formAppDetail} />
        </Collapse.Panel>
        <Collapse.Panel header="基本配置" key="$basic_setting_$">
          <BasicSetting form={form} formAppDetail={formAppDetail} />
        </Collapse.Panel>
        <Collapse.Panel
          header={
            <>
              <span>触发条件</span>
              <span>
                <InfoCircleOutlined style={{ color: '#1677ff', margin: '0 10px' }} />
                当满足以下条件，可审批/查看筛选出来的表单
              </span>
            </>
          }
          key="$trigger_event_$"
        >
          <Formtrigger form={form} formAppDetail={formAppDetail} />
        </Collapse.Panel>
        <Collapse.Panel header="审核表单代办提醒" key="$approve_setting_$">
          <ApprovePendingInfo form={form} formAppDetail={formAppDetail} />
        </Collapse.Panel>
      </Collapse>
    </div>
  );
};

export default FormRule;
