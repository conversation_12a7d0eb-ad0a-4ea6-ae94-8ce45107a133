import { audioTypeList, FileSizeUnit, FileSizeUnitList } from '@/const/metadata';
import { MinusOutlined } from '@ant-design/icons';
import { Form, FormInstance, InputNumber, Radio, Select, Space } from '@gwy/components-web';
import { useContext } from 'react';
import { MetaDataContext } from '../../context';

export const AudioDTOKey = 'metaDataAudioDTO';

type Props = {
  form?: FormInstance;
};

const AudioConfig = ({ form }: Props) => {
  const { disabled, hasApproved } = useContext(MetaDataContext);

  const ifLimitFormat = Form.useWatch([AudioDTOKey, 'ifLimitFormat'], form);

  return (
    <div>
      <Form.Item label="音频格式" name={[AudioDTOKey, 'ifLimitFormat']} rules={[{ required: true, message: '请选择' }]} initialValue={false}>
        <Radio.Group
          disabled={hasApproved || disabled}
          options={[
            { label: '不限制', value: false },
            { label: '限制', value: true },
          ]}
        />
      </Form.Item>

      {ifLimitFormat && (
        <div>
          <Form.Item label="格式类型" name={[AudioDTOKey, 'formats']} rules={[{ required: true, message: '请选择' }]}>
            <Select placeholder="请选择" options={audioTypeList} mode="multiple"></Select>
          </Form.Item>
        </div>
      )}

      <Form.Item label="单音频容量大小" required style={{ marginBottom: 0 }}>
        <Space align="start">
          <Form.Item
            label=""
            name={[AudioDTOKey, 'sizeMin']}
            rules={[{ required: true, message: '请填写' }]}
            style={{ maxWidth: '100px' }}
            initialValue={0}
          >
            <InputNumber placeholder="请填写" min={0} step={1} max={1000} />
          </Form.Item>
          <Form.Item label="">
            <MinusOutlined />
          </Form.Item>

          <Form.Item
            initialValue={200}
            style={{ width: '100px' }}
            label=""
            name={[AudioDTOKey, 'sizeMax']}
            rules={[
              {
                validator(rule, value) {
                  const min = form.getFieldValue([AudioDTOKey, 'sizeMin']);
                  if (Number(value) <= Number(min) && value !== null) {
                    return Promise.reject('最大值必须大于最小值');
                  }
                  return Promise.resolve();
                },
              },
              { required: true, message: '请输入' },
            ]}
          >
            <InputNumber placeholder="请填写" min={0} step={1} max={1000} />
          </Form.Item>
          <Form.Item label="" name={[AudioDTOKey, 'sizeUnit']} rules={[{ required: true, message: '请选择' }]} initialValue={FileSizeUnit.MB}>
            <Select placeholder="请选择" options={FileSizeUnitList}></Select>
          </Form.Item>
        </Space>
      </Form.Item>
      <Form.Item label="音频数量" required>
        <Space>
          <Form.Item name={[AudioDTOKey, 'audioNum']} rules={[{ required: true, message: '请输入' }]} initialValue={1}>
            <InputNumber min={1} step={1} max={10} />
          </Form.Item>
        </Space>
      </Form.Item>
    </div>
  );
};

export default AudioConfig;
