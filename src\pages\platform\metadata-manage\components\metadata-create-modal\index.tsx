import { APPROVE_ENUM } from '@/const/datasource';
import { datasourceLightAPI } from '@/services';
import { addDatasourceVersion, cancelStashDataUnit, editDataUnit, getDataUnitVersionInfo, submitDatasourceApprove } from '@/services/datasource';
import { Button, message, Modal, Steps } from '@gwy/components-web';
import { isEmpty } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import AddTagConfig from './components/add-tag-config';
import AddTagConfigLight from './components/add-tag-config-light';
import BasicInfo from './components/basic-info';
import styles from './index.less';
type Iprops = {
  onCancel: () => void;
  onOk: () => void;
  refresh: () => void;
  categoryId: number | string;
  currentVersionId?: number | string;
  preVersionId?: number | string;
  dataSourceType: 'NORMAL' | 'GENERAL';
  operateType?: 'add' | 'edit' | 'approve' | 'view'; // 打开弹窗的场景类型
  addModalType?: 'add' | 'edit' | 'approve' | 'view';
  datasourceTypes?: any[];
};
const CreateDataSource = (props: Iprops) => {
  const { onCancel, onOk, refresh, categoryId, dataSourceType, currentVersionId, operateType, addModalType, datasourceTypes } = props;
  const [currentStep, setCurrentStep] = useState(0);

  // 关联数据单元
  const [relateDataUnit, setRelateDataUnit] = useState(null);

  const minStep = 0;
  const maxStep = 1;
  const step1Ref = useRef(null);
  const step2Ref = useRef(null);
  const step2LightRef = useRef(null);
  const [stepValues, setStepValues] = useState(
    new Array(maxStep + 1).fill(0).map((item, index) => ({
      step: index,
      initValue: null,
    })),
  );
  const stepVaueChange = (step, value) => {
    setStepValues((prev) => {
      return prev.map((item) => {
        if (item.step === step) {
          return {
            ...item,
            step,
            initValue: value,
          };
        }
        return item;
      });
    });
  };
  const prevStep = () => {
    switch (currentStep) {
      case 0:
        break;
      case 1: {
        const value = step2Ref.current?.getValues();
        stepVaueChange(currentStep, value);
        currentStep > minStep && setCurrentStep((prev) => prev - 1);
        break;
      }

      default:
        break;
    }
  };
  const nextStep = async () => {
    switch (currentStep) {
      case 0: {
        const value = await step1Ref.current?.getValues();
        stepVaueChange(currentStep, value);
        currentStep < maxStep && setCurrentStep((prev) => prev + 1);

        break;
      }
      case 1:
        break;
      default:
        break;
    }
  };

  const getDetail = async (currentVersionId) => {
    const data = await getDataUnitVersionInfo(currentVersionId);
    stepVaueChange(0, data);
  };

  const geneModal = (title, onOk) => {
    return Modal.confirm({
      centered: true,
      title,
      onOk,
    });
  };
  const sameTagNameCheck = (arr) => {
    const nameMap = new Map();
    for (let i = 0; i < arr.length; i++) {
      const itemName = arr[i].name;

      if (nameMap.has(itemName)) {
        // 找到重复项，返回之前的索引和当前索引
        return { index1: nameMap.get(itemName), index2: i };
      }

      nameMap.set(itemName, i);
    }
    return null;
  };

  const checkValuesFn = (allData) => {
    let title = '';
    let { tags } = allData;
    tags = (tags || []).filter((tag) => !tag?.deletedFlag);
    let emptyIndexs = [];
    (tags || []).forEach((tag, index) => {
      if (!tag?.name || isEmpty(tag?.tagMetaDataConfig)) {
        emptyIndexs.push(`第${index + 1}项`);
      }
    });
    if (emptyIndexs.length > 0) {
      return (title = `${emptyIndexs.join('、')}标签属性不完整，请填写完整后再提交`);
    }
    const sameName = sameTagNameCheck(tags || []);
    if (sameName) {
      return `当前数据单元标签存在名称重复，请重新修改后再提交`;
    }
    return title;
  };

  useEffect(() => {
    if (currentVersionId) {
      getDetail(currentVersionId);
    }
  }, [currentVersionId]);

  const handleSubmitData = async () => {
    let allValues = {};
    const step1RefValues = await step1Ref.current?.getValues();
    const step2RefValues = step2Ref.current?.getValues();
    allValues = {
      ...step1RefValues,
      tags: (step2RefValues?.configTags || []).filter((t) => !t.parentFlag),
    };

    return allValues;
  };
  const saveData = async () => {
    try {
      const allValues = await handleSubmitData();
      const title = checkValuesFn(allValues);
      if (title) {
        return message.error(title);
      }
      const res = currentVersionId
        ? await editDataUnit(currentVersionId, allValues)
        : await addDatasourceVersion({
            ...allValues,
          });
      if (res) {
        message.success('保存成功');
        onCancel && onCancel();
        refresh && refresh();
      }
    } catch (error: any) {
      if (typeof error === 'string') {
        return message.error(`${error}`);
      }
      // if (error?.desc) {
      //   return message.error(`${error.desc}`);
      // }
    }
  };
  const sbumitData = async () => {
    try {
      const allValues = await handleSubmitData();
      const title = checkValuesFn(allValues);
      if (title) {
        return message.error(title);
      }
      geneModal('确认提交该数据单元吗', async () => {
        try {
          const approveId = currentVersionId
            ? await editDataUnit(currentVersionId, allValues)
            : await addDatasourceVersion({
                ...allValues,
              });
          if (approveId) {
            await submitDatasourceApprove(approveId);
            message.success('提交成功');
            onCancel && onCancel();
            refresh && refresh();
          }
        } catch (error: any) {
          // debugger;
          // if (error?.desc) {
          //   return message.error(error.desc);
          // }
        }
      });
    } catch (error) {
      console.log(error, 'error---');
      return message.error(error);
    }
  };

  // 轻量数据单元-提交
  const sbumitDataLight = async () => {
    try {
      let allValues = {};
      const step1RefValues = await step1Ref.current?.getValues();
      const step2LightRefValues = step2LightRef.current?.getValues();
      allValues = {
        ...step1RefValues,
        relateTagIds: step2LightRefValues?.relateTagIds || [],
      };
      geneModal('确认提交该数据单元吗', async () => {
        try {
          await datasourceLightAPI.addDataUnit(allValues);
          message.success('提交成功');
          onCancel && onCancel();
          refresh && refresh();
        } catch (error: any) {
          if (error?.desc) {
            return message.error(error.desc);
          }
        }
      });
    } catch (error) {
      return message.error(error);
    }
  };

  const cancleTempSave = () => {
    cancelStashDataUnit(currentVersionId).then(() => {
      message.success('取消成功');
      refresh && refresh();
      onOk && onOk();
    });
  };

  const renderModalBtn = () => {
    const basicInfo = stepValues[0]?.initValue;
    console.log('basicInfo', basicInfo);
    const ifSaveStatus = basicInfo?.status === APPROVE_ENUM.STASH;
    const ifApproving = basicInfo?.status === APPROVE_ENUM.ING;
    console.log(basicInfo?.status, 'basicInfo?.status');
    return (
      <div>
        <Button onClick={onCancel} style={{ marginRight: '8px' }}>
          取消
        </Button>
        {currentStep > minStep && currentStep <= maxStep && (
          <Button onClick={prevStep} style={{ marginRight: '8px' }}>
            上一步
          </Button>
        )}
        {!ifApproving && !relateDataUnit && (
          <Button onClick={saveData} color="primary" variant="outlined" style={{ marginRight: '8px' }}>
            暂存
          </Button>
        )}

        {ifSaveStatus && (
          <Button onClick={cancleTempSave} type="primary" ghost style={{ marginRight: '8px' }}>
            取消暂存
          </Button>
        )}
        {currentStep >= minStep && currentStep < maxStep && (
          <Button onClick={nextStep} type="primary">
            下一步
          </Button>
        )}
        {currentStep === maxStep && !ifApproving && (
          <Button
            type="primary"
            onClick={() => {
              if (relateDataUnit) {
                sbumitDataLight();
              } else {
                sbumitData();
              }
            }}
          >
            提交
          </Button>
        )}
      </div>
    );
  };
  return (
    <div className={styles.createModal}>
      <Modal title="新增数据单元" open width={currentStep === 0 ? 700 : 980} onCancel={onCancel} onOk={onOk} footer={() => renderModalBtn()}>
        <div
          style={{
            height: currentStep === 0 ? 400 : 600,
          }}
        >
          <div className={styles.stepBox}>
            <Steps current={currentStep}>
              <Steps.Step title="基本信息" />
              <Steps.Step title="标签配置" />
            </Steps>
          </div>
          <div>
            <div
              style={{
                display: currentStep !== 0 && 'none',
                marginTop: 18,
              }}
            >
              <BasicInfo
                ref={step1Ref}
                initialValue={stepValues[0]?.initValue}
                dataSourceType={dataSourceType}
                operateType={addModalType || 'add'}
                onRelateDataUnitChange={(dataUnit) => {
                  setRelateDataUnit(dataUnit);
                }}
                datasourceTypes={datasourceTypes}
              />
            </div>
            <div
              style={{
                display: currentStep !== 1 && 'none',
              }}
            >
              {!relateDataUnit && (
                <AddTagConfig
                  ref={step2Ref}
                  operateType={addModalType || 'add'}
                  currentVersionId={currentVersionId}
                  parentDataUnitCurrentVersionId={stepValues[0].initValue?.parentDataUnitCurrentVersionId}
                  fromPage="addModal"
                  currentStep={currentStep}
                />
              )}

              {relateDataUnit && <AddTagConfigLight ref={step2LightRef} operateType={'edit'} currentVersionId={relateDataUnit.currentVersionId} />}
            </div>
            {/* {currentStep === 1 && } */}
            {/* {currentStep === 2 && <AddTagConfig ref={step2Ref} initialValue={stepValues[0]?.initValue} operateType="add" />} */}
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CreateDataSource;
