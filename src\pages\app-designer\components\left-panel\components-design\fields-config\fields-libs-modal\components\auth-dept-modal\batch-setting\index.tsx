import { getFieldAuthPostList } from '@/services/calc-field';
import { Avatar, Checkbox, Empty, Input, Tree } from '@gwy/components-web';
import { cloneDeep, isEmpty } from 'lodash';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import FieldItem from '../../field-item';
import { TabType } from '../../my-create-wrap';
import styles from './index.less';

interface IProps {
  postId?: string;
  onChange: (posts: any[], fields: any[]) => void;
  type?: TabType;
}

const BatchSetting = memo<IProps>(({ postId, onChange, type }) => {
  const [orgList, setOrgList] = useState([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [checkAll, setCheckAll] = useState(false);
  const [indeterminate, setIndeterminate] = useState(false);
  const [calcList, setCalcList] = useState([]);
  const [calcListChecked, setCalcListChecked] = useState([]);

  const localRef = useRef({
    checkedPosts: [],
  });

  // 处理检索
  const handleDepSearch = (value) => {
    setSearchKeyword(value);
  };

  const handleList = useMemo(() => {
    if (!searchKeyword) {
      return orgList;
    }

    const memoData = cloneDeep(orgList);
    let searchList = [];
    const loopSearchData = (originData, prevLevel = {}) => {
      originData = originData
        ?.map((firstLevelItem) => {
          if (
            (firstLevelItem?.orgName || '').includes(searchKeyword) ||
            (firstLevelItem?.deptName || '').includes(searchKeyword) ||
            (firstLevelItem?.postName || '').includes(searchKeyword) ||
            (firstLevelItem?.userName || '').includes(searchKeyword)
          ) {
            return {
              ...firstLevelItem,
              hidden: false,
            };
          }

          const prev = Object.keys(prevLevel).length > 0 ? prevLevel : firstLevelItem;
          const prevHasInclude =
            (prev?.orgName || '').includes(searchKeyword) ||
            (prev?.deptName || '').includes(searchKeyword) ||
            (prev?.postName || '').includes(searchKeyword) ||
            (prev?.userName || '').includes(searchKeyword);
          if (!prevHasInclude && (!isEmpty(firstLevelItem?.depts) || !isEmpty(firstLevelItem?.posts))) {
            firstLevelItem.depts = loopSearchData(firstLevelItem.depts, firstLevelItem);
            firstLevelItem.posts = loopSearchData(firstLevelItem.posts, firstLevelItem);
            const everyHidden = firstLevelItem.depts?.every((item) => item.hidden) && firstLevelItem.posts?.every((item) => item.hidden);
            return {
              ...firstLevelItem,
              hidden: everyHidden,
            };
          }
          return {
            ...firstLevelItem,
            hidden: true,
          };
        })
        .filter(Boolean);

      return originData;
    };
    searchList = loopSearchData(memoData);

    return searchList;
  }, [searchKeyword, orgList]);

  useEffect(() => {
    (async () => {
      try {
        const params = {
          postId,
          type,
        };
        const [calcList, org] = await Promise.all([Promise.resolve([]), getFieldAuthPostList(postId)]);
        setCalcList(calcList || []);
        // 默认全选
        setCalcListChecked(calcList.map((item) => item.id));
        setCheckAll(!!calcList.length);
        setOrgList([org]);
      } catch (e) {}
    })();
  }, [postId]);

  const handleCheckAll = (e) => {
    const { checked } = e.target;
    const checkedList = checked ? calcList.map((item) => item.id) : [];
    setCalcListChecked(checkedList);
    setIndeterminate(false);
    setCheckAll(checked);
    onChange(
      localRef.current.checkedPosts,
      calcList.map((ite) => ({ ...ite, selected: checkedList.includes(ite.id) })),
    );
  };

  const handleCheck = (checkList) => {
    setCalcListChecked(checkList);
    setIndeterminate(checkList.length && checkList.length !== calcList.length);
    checkList.length && checkList.length === calcList.length && setCheckAll(true);
    onChange(
      localRef.current.checkedPosts,
      calcList.map((ite) => ({ ...ite, selected: checkList.includes(ite.id) })),
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.rightContainer}>
        <Checkbox checked={checkAll} indeterminate={indeterminate} onChange={handleCheckAll} style={{ marginBottom: 8 }}>
          全选
        </Checkbox>
        <Checkbox.Group value={calcListChecked} onChange={handleCheck} style={{ width: '100%' }}>
          <div className={styles.fieldList}>
            {calcList.map((field, i) => (
              <div key={i} className={styles.field}>
                <FieldItem field={field} />
              </div>
            ))}
          </div>
        </Checkbox.Group>
      </div>

      <div className={styles.leftContainer}>
        <div className={styles.search}>
          <Input.Search
            placeholder="搜索"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleDepSearch((e.target as HTMLInputElement).value);
              }
            }}
            allowClear
          />
        </div>

        <div className={styles.postList}>
          {isEmpty(handleList) ? (
            <Empty />
          ) : (
            <>
              <Tree
                style={{ padding: '10px 0' }}
                defaultExpandAll
                onCheck={(list) => {
                  const posts = ((list || []) as any[]).filter((i) => !handleList.some((h) => h.departmentId === i));
                  localRef.current.checkedPosts = posts;
                  onChange(
                    posts,
                    calcList.map((ite) => ({ ...ite, selected: calcListChecked.includes(ite.id) })),
                  );
                }}
                checkable
              >
                {handleList.map((org) => {
                  const renderDepts = (depts) => {
                    return depts?.map((dept) => (
                      <Tree.TreeNode
                        style={{
                          fontWeight: 'bold',
                          display: dept?.hidden ? 'none' : 'inline-flex',
                        }}
                        key={org.deptId}
                        title={`${dept.deptName}（${dept.posts?.length || 0}）`}
                      >
                        {dept.posts?.map((post) => (
                          <Tree.TreeNode
                            key={post.postId}
                            className={styles.postItemBox}
                            style={{
                              display: post?.hidden ? 'none' : 'inline-flex',
                            }}
                            title={
                              <div className={styles.postItem}>
                                <Avatar name={post.userName} src={post.portraitUrl} size={40} sex={post.sex} />
                                <div className={styles.detail}>
                                  <div className={styles.userName}>{post.userName || '-'}</div>
                                  <div className={styles.postName}>{post.postName || '-'}</div>
                                </div>
                              </div>
                            }
                          />
                        ))}
                        {dept.depts?.length > 0 && renderDepts(dept.depts)}
                      </Tree.TreeNode>
                    ));
                  };

                  return (
                    <Tree.TreeNode
                      style={{ fontWeight: 'bold', display: org?.hidden ? 'none' : 'inline-flex' }}
                      key={org.orgId}
                      title={`${org.orgName}（${org.depts?.length || 0}）`}
                    >
                      {renderDepts(org.depts)}
                    </Tree.TreeNode>
                  );
                })}
              </Tree>
            </>
          )}
        </div>
      </div>
    </div>
  );
});

export default BatchSetting;
