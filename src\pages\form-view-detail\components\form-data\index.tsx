import { DATA_TYPE } from '@/components/filter-group/filter-const';
import { NumberRangeType, numberRangeTypeOptions, TimeRangeType } from '@/const/metadata';
import { Collapse, Empty, Modal, Table, Tag } from '@gwy/components-web';
import dayjs from 'dayjs';
import { useMemo, useState } from 'react';
import FilterItemPreview from '../filter-item-view';
interface Iprops {
  formAppDetail: any;
}
const FormData = (props: Iprops) => {
  const { formAppDetail } = props;
  const [openModal, setOpenModal] = useState({
    open: false,
    data: null,
  });

  const [tagList, fieldList] = useMemo(() => {
    // const tagList = (formAppDetail.fields || []).filter((item) => item.sourceType === 'tag');
    const tagList = formAppDetail?.tags || [];
    // const fieldList = (formAppDetail.fields || []).filter((item) => item.sourceType === 'combine_field');
    const fieldList = formAppDetail?.fields || [];
    return [tagList, fieldList];
  }, [formAppDetail]);

  const formatDateTime = (dateTimeStr) => {
    if (dateTimeStr === null || dateTimeStr === '') {
      return '';
    }
    const dateTime = dayjs(dateTimeStr);
    let formatStr = 'YYYY年MM月DD日';

    // 当年时间，不展示年份
    if (dateTime.year() === dayjs().year()) {
      formatStr = 'MM月DD日';
    }

    return dateTime.format(formatStr);
  };

  const getRenderTagConfig = (config) => {
    const {} = config || {};
    const getControlText = () => {
      let text = null;
      const { type } = config || {};
      switch (type) {
        case DATA_TYPE.TEXT.val: {
          const { metaDataTextVO, widgetTypeDesc } = config || {};
          text = widgetTypeDesc ? widgetTypeDesc : '选择器';
          break;
        }
        case DATA_TYPE.TEXTENUM.val:
          text = '选择器';
          break;
        case DATA_TYPE.NUMBER.val:
          text = '数字输入框';
          break;
        case DATA_TYPE.DATETIME.val:
          text = '日期选择器';
          break;
        case DATA_TYPE.UNIT.val:
          text = '数字输入框 + 选择器';
          break;
        case DATA_TYPE.CODE.val:
          text = '输入框';
          break;
        case DATA_TYPE.ORG.val:
        case DATA_TYPE.DEPT.val:
        case DATA_TYPE.POST.val:
        case DATA_TYPE.USER.val:
        case DATA_TYPE.OBJECT.val:
          text = '选择器';
          break;
      }
      return text ? <span>控件选择：{text}</span> : null;
    };
    const getCheckText = () => {
      let text = null;
      const { type } = config || {};
      switch (type) {
        case DATA_TYPE.TEXT.val: {
          const { metaDataTextVO, multipleChoice } = config || {};
          const { maxLength, enumList } = metaDataTextVO;
          if (maxLength) {
            text = `最大长度：${maxLength}`;
            break;
          }
          if (enumList?.length) {
            text = `从枚举选项中选择`;
            if (multipleChoice) {
              text += `，可多选`;
            }
          }
          break;
        }
        case DATA_TYPE.NUMBER.val: {
          const { decimalPlaces, metaDataNumberVO } = config || {};
          const { min, max, rangeType } = metaDataNumberVO;
          if (min) {
            text = `${numberRangeTypeOptions.find((n) => n.value === rangeType).label}${min}`;
          }
          if (max) {
            text = `${numberRangeTypeOptions.find((n) => n.value === rangeType).label}${max}`;
          }
          if (min !== null && max !== null) {
            if (rangeType === NumberRangeType.between) {
              text = `在${min}和${max}之间`;
            }
            if (rangeType === 'OUT') {
              text = `在${min}和${max}之外`;
            }
          }
          if (rangeType === NumberRangeType.unlimited) {
            text = `不做限制`;
          }
          const decText = decimalPlaces === 0 ? `，保留整数位数` : `，保留小数点后${decimalPlaces}位`;
          text += decText;
          break;
        }

        case DATA_TYPE.DATETIME.val: {
          const { metaDataDateTimeVO } = config || {};
          const { start, end, rangeType, rangeTypeDesc } = metaDataDateTimeVO;
          const startText = start ? formatDateTime(start || '') : '';
          const endText = end ? formatDateTime(end || '') : '';
          if (startText || endText) {
            text = `${rangeTypeDesc}${startText || endText}`;
          }
          if (rangeType === TimeRangeType.between && startText && endText) {
            text = `在${startText} - ${endText}之间`;
          }
          break;
        }
        case DATA_TYPE.UNIT.val: {
          const { metaDataUnitVO } = config || {};
          const { rangeType, rangeTypeDesc, typeDesc, unitDesc, min, max } = metaDataUnitVO;
          if (rangeType === NumberRangeType.unlimited) {
            text = `${typeDesc || ''}${rangeTypeDesc}`;
            break;
          }
          if (min !== null) {
            text = `${rangeTypeDesc}${min}${unitDesc}`;
          }
          if (max !== null) {
            text = `${rangeTypeDesc} ${max}${unitDesc}`;
          }
          if (min !== null && max !== null) {
            text = `最小值${min}${unitDesc}，最大值${max}${unitDesc}`;
            if (rangeType === 'BETWEEN') {
              text = `在${min}${unitDesc}和${max}${unitDesc}之间`;
            }
            if (rangeType === 'OUT') {
              text = `在${min}${unitDesc}和${max}${unitDesc}之外`;
            }
          }
          break;
        }
        case DATA_TYPE.CODE.val: {
          const { metaDataCodeVO } = config || {};
          const { elementTypeDescList, configList } = metaDataCodeVO || {};
          text = `暂未配置编码组`;
          if (elementTypeDescList) {
            text = `从常用编码组：${elementTypeDescList.join('、')}中选择`;
          }
          if (configList) {
            text = `从自定义编码组中选择`;
          }
          break;
        }
        case DATA_TYPE.ORG.val:
        case DATA_TYPE.DEPT.val:
        case DATA_TYPE.POST.val:
        case DATA_TYPE.USER.val:
        case DATA_TYPE.OBJECT.val: {
          const { metaDataObjectVO, multipleChoice } = config || {};
          const { configDescList } = metaDataObjectVO || {};
          text = '选择器';
          if (configDescList) {
            text = `选取范围：${configDescList.join('、')}`;
          }
          if (multipleChoice) {
            text += '可多选';
          }
          break;
        }
      }
      return text ? <span style={{ marginLeft: '20px' }}>输入校验：{text}</span> : null;
    };
    const getFilterText = () => {
      return null;
    };
    const controlText = getControlText();
    const checkText = getCheckText();
    const filterText = getFilterText();
    return (
      <div>
        {controlText}
        {checkText}
        {filterText}
      </div>
    );
  };

  const renderTagList = useMemo(() => {
    const columns = [
      {
        title: '字段名称',
        dataIndex: 'sourceName',
        key: 'sourceName',
        width: 120,
      },
      {
        title: '类型',
        dataIndex: 'readonly',
        key: 'readonly',
        render: (value) => {
          const tagColor = value ? 'orange' : 'geekblue';
          const text = value ? '输出' : '输入';
          return <Tag color={tagColor}>{text}</Tag>;
        },
        width: 80,
      },
      {
        title: '标签别名',
        dataIndex: 'fieldName',
        key: 'fieldName',
        render: (value, record) => {
          return <>{value || '_'}</>;
        },
        width: 120,
      },
      {
        title: '规则设置',
        dataIndex: 'config',
        key: 'config',
        render: (value, record) => {
          const { config } = record || {};
          return <div>{getRenderTagConfig(config)}</div>;
        },
      },
    ];
    return <Table columns={columns} dataSource={tagList} pagination={false} rowKey="id" scroll={{ y: 300 }} />;
  }, [tagList]);

  const renderFieldList = useMemo(() => {
    const columns = [
      {
        title: '字段名称',
        dataIndex: 'sourceName',
        key: 'sourceName',
        width: 120,
      },

      {
        title: '类型',
        dataIndex: 'sourceType',
        key: 'sourceType',
        render: (text, record) => {
          return <span>组合</span>;
        },
        width: 80,
      },
      {
        title: '规则设置',
        dataIndex: 'conbineConfig',
        key: 'conbineConfig',
        render: (text, record) => {
          const { formCombineFieldItems } = record || {};
          const renderText = formCombineFieldItems
            .map((item) => item.tagName)
            .map((item, index) => (
              <span style={{ color: '#4c7bf6' }} key={index}>
                {index !== 0 && <span style={{ margin: '0 10px' }}>+</span>}
                <span>【{item}】</span>
              </span>
            ));
          return (
            <div
              onClick={() => {
                setOpenModal({
                  open: true,
                  data: record,
                });
              }}
            >
              {renderText}
            </div>
          );
        },
      },
    ];
    return <Table columns={columns} dataSource={fieldList} pagination={false} rowKey="id" scroll={{ y: 300 }} />;
  }, [fieldList]);
  return (
    <>
      <Collapse defaultActiveKey={['$form_tag_$', '$basic_fieled_$']} ghost>
        <Collapse.Panel header="标签" key="$form_tag_$">
          {tagList.length > 0 ? renderTagList : <Empty description="暂无数据" />}
        </Collapse.Panel>
        <Collapse.Panel header="字段" key="$basic_fieled_$">
          {fieldList.length > 0 ? renderFieldList : <Empty description="暂无数据" />}
        </Collapse.Panel>
      </Collapse>
      {openModal.open && (
        <Modal
          title="字段名称"
          open
          onCancel={() => {
            setOpenModal({ open: false, data: null });
          }}
          footer={null}
        >
          {(openModal?.data?.formCombineFieldItems || []).map((tag, index) => (
            <div key={index}>
              <div
                style={{
                  fontWeight: 'bold',
                  marginBottom: '12px',
                }}
              >{`${tag?.dataUnitName}—${tag?.tagName}`}</div>
              <FilterItemPreview conditionGroups={tag?.conditionGroups} theme="white" />
            </div>
          ))}
        </Modal>
      )}
    </>
  );
};

export default FormData;
