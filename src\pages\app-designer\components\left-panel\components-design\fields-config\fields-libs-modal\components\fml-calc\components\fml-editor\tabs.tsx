import TagIOType from '@/components/tag-io-type';
import { ExpressionType, FieldType } from '@/types/calc-field';
import { CaretRightOutlined } from '@ant-design/icons';
import { Collapse, Empty, Input, message, Select, Tabs, TextEllipsisTooltip } from '@gwy/components-web';
import classNames from 'classnames';
import { isArray, isEmpty } from 'lodash';
import { useMemo, useState } from 'react';
import { FIELDS_TYPE_OPTIONS } from './const';
import styles from './index.less';
import { isNumberOrUnitTag } from './utils';

enum TABS_KEY {
  TAG = 'TAG',
  FIELD = 'FIELD',
}
enum SEARCH_TYPE {
  TAG = '1',
  DATAUNIT = '2',
}
const tabs = [
  {
    label: <div className={styles.tabsLabel}>标签</div>,
    key: TABS_KEY.TAG,
  },
  {
    label: <div className={styles.tabsLabel}>字段</div>,
    key: TABS_KEY.FIELD,
  },
];

const FormulaTabs = ({ allDataUnitsWithTags, isNumOrUnitOnly, fields, onSelectTag, disabled = false, fieldType }) => {
  // 标签筛选
  const [search, setSearch] = useState('');
  // 标签搜索类型
  const [searchType, setSearchType] = useState(SEARCH_TYPE.TAG);

  const dataUnitFilter = useMemo(() => {
    if (allDataUnitsWithTags && allDataUnitsWithTags.length) {
      if (search && searchType === SEARCH_TYPE.TAG) {
        return (
          allDataUnitsWithTags
            .filter((dataUnit: any) => {
              // 检查该dataUnit的tagList中是否有tagName包含search的标签
              const hasMatchingTag = dataUnit.tagList?.some((tag: any) => tag.name?.toLowerCase().includes(search.toLowerCase()));
              return hasMatchingTag;
            })
            .map((dataUnit: any) => ({
              ...dataUnit,
              // 只保留tagName包含search的标签
              tagList: dataUnit.tagList?.filter((tag: any) => tag.name?.toLowerCase().includes(search.toLowerCase())) || [],
            })) || []
        );
      }
      if (search && searchType === SEARCH_TYPE.DATAUNIT) {
        return allDataUnitsWithTags.filter((n: any) => n.name?.includes(search)) || [];
      }
      return allDataUnitsWithTags;
    }
    return [];
  }, [allDataUnitsWithTags, search, searchType]);

  const filterFields = useMemo(() => {
    if (isEmpty(fields)) return [];
    let _fields = fields;

    // 如果当前配置的字段类型为独立字段，则只显示独立字段
    if (fieldType === FieldType.IndependentField) {
      _fields = fields.filter((n) => n.fieldType === FieldType.IndependentField);
    }

    let searchArr: Array<{ groupId: number; groupName: string; list: any[]; type: ExpressionType }> = (_fields || [])
      .reduce((total, cur) => {
        if (cur.groupId) {
          const index = total.findIndex((n) => n.groupId === cur.groupId);
          if (~index) {
            total[index].list.push(cur);
          } else {
            total.push({
              groupId: cur.groupId,
              groupName: cur.groupName,
              list: [cur],
              type: ExpressionType.FUNC_CALC,
            });
          }
        } else {
          total.push({
            groupId: null,
            groupName: '',
            list: [cur],
            type: ExpressionType.FUNC_CALC,
          });
        }
        return total;
      }, [])
      .sort((item) => (item.groupId === null ? 1 : -1));

    if (!search) {
      return searchArr;
    } else {
      searchArr = searchArr
        .map((item) => ({
          ...item,
          list: item.list.filter((field) => field.fieldName?.includes(search)),
        }))
        .filter((item) => item.list && item.list.length);
      return searchArr;
    }
  }, [fields, search, fieldType]);

  const renderTags = (dataUnit: any) => {
    return (dataUnit.tagList || []).map((n) => {
      return (
        <div
          className={classNames(styles.tagBox)}
          style={{
            width: '100%',
            pointerEvents: disabled ? 'none' : 'all',
          }}
          key={n.tagId}
          onClick={(e) => {
            e.stopPropagation();
            if (isNumOrUnitOnly) {
              if (!isNumberOrUnitTag(n)) {
                message.warning('请选择数值类型、单位类型标签');
                return;
              }
            }
            onSelectTag({
              showValue: n.name,
              value: n.tagId,
              expressionType: ExpressionType.TAG,
              refTag: {
                sourceId: n.tagId,
                code: n.code,
                type: n.tagMetaDataConfig?.type,
                name: n.name,
                dataUnitId: n.dataUnitId,
                dataUnitName: dataUnit.name,
              },
            });
          }}
        >
          <TextEllipsisTooltip text={n.name} />
          <div>
            <TagIOType type={n.dataSave} />
          </div>
        </div>
      );
    });
  };

  const renderFields = (calc, type) => (
    <div
      className={classNames(styles.tagBox)}
      style={{
        width: '100%',
        pointerEvents: disabled ? 'none' : 'all',
      }}
      key={calc.fieldId}
      onClick={(e) => {
        e.stopPropagation();
        onSelectTag({
          showValue: calc.fieldName,
          value: calc.fieldId,
          expressionType: type,
          refFieldId: calc.fieldId,
        });
      }}
    >
      <TextEllipsisTooltip text={calc.fieldName} style={{ maxWidth: '80%' }} />
    </div>
  );

  const renderTabs = () => {
    return tabs.slice(0, 2).map((item) => {
      let children = <></>;
      switch (item.key) {
        case TABS_KEY.TAG:
          children = (
            <div className={styles.tabBox}>
              <div style={{ display: 'flex' }}>
                <Select
                  value={searchType}
                  style={{ width: 100 }}
                  disabled={disabled}
                  onChange={(e) => {
                    setSearchType(e);
                    setSearch('');
                  }}
                  options={[
                    {
                      value: SEARCH_TYPE.TAG,
                      label: '标签',
                    },
                    {
                      value: SEARCH_TYPE.DATAUNIT,
                      label: '数据单元',
                    },
                  ]}
                />
                <Input.Search placeholder="关键字" allowClear value={search} disabled={disabled} onChange={(e) => setSearch(e.target.value)} />
              </div>

              {isArray(dataUnitFilter) && dataUnitFilter.length ? (
                <div className={styles.tags}>
                  <div className={styles.tag}>
                    <Collapse
                      key="tag"
                      className={styles.collapse}
                      defaultActiveKey={dataUnitFilter.map((item) => item.tagId)}
                      expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
                      ghost
                    >
                      {dataUnitFilter.map((dataUnit) => (
                        <Collapse.Panel key={dataUnit.dataUnitId} header={<div className={styles.ellipseHeader}>{dataUnit.name}</div>}>
                          {renderTags(dataUnit)}
                        </Collapse.Panel>
                      ))}
                    </Collapse>
                  </div>
                </div>
              ) : (
                <Empty style={{ height: 'calc(100% - 36px)' }} description={<span style={{ textAlign: 'center', fontSize: 12 }}>暂无数据</span>} />
              )}
            </div>
          );
          break;
        case TABS_KEY.FIELD:
          children = (
            <div className={styles.tabBox}>
              <Input.Search placeholder="关键字" allowClear value={search} onChange={(e) => setSearch(e.target.value)} />
              {isArray(filterFields) && filterFields.length ? (
                <div className={styles.filterFieldsBox}>
                  {(filterFields || []).map((n) => {
                    const label = FIELDS_TYPE_OPTIONS[n.type]?.label;

                    if (n?.groupId) {
                      return (
                        <Collapse
                          key="field"
                          ghost
                          expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
                          className={styles.collapseFields}
                          defaultActiveKey={[1]}
                        >
                          <Collapse.Panel key={1} header={<TextEllipsisTooltip text={n.groupName} />}>
                            {n.list.map((item) => renderFields(item, n.type))}
                          </Collapse.Panel>
                        </Collapse>
                      );
                    }
                    const field = n.list[0];
                    return renderFields(field, n.type);
                  })}
                </div>
              ) : (
                <Empty style={{ height: 'calc(100% - 36px)' }} description={<span style={{ textAlign: 'center', fontSize: 12 }}>暂无数据</span>} />
              )}
            </div>
          );
          break;
        default:
          break;
      }
      return { ...item, children };
    });
  };

  return (
    <div
      className={styles.tagsBox}
      style={{
        flex: 2,
        pointerEvents: 'all',
      }}
    >
      <Tabs
        size="small"
        centered
        onChange={() => {
          setSearch('');
        }}
        items={renderTabs()}
        tabBarStyle={{ marginBottom: 0 }}
        style={{ height: '100%', color: '#1d2129' }}
      />
    </div>
  );
};

export default FormulaTabs;
