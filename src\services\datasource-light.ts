import { dataWrapper, requestUtil } from '@gwy/libs-web';

/**
 * 创建轻量数据单元
 */
export const addDataUnit = (params) => dataWrapper(requestUtil.post('gwy-datasource/api/light_data_units', params));

/**
 * 编辑轻量数据单元
 */
export const updateDataUnit = (id, params) => dataWrapper(requestUtil.put(`gwy-datasource/api/light_data_units/${id}`, params));

/**
 * 获取轻量数据单元信息
 */
export const getDataUnitInfo = (id) => dataWrapper(requestUtil.get(`gwy-datasource/api/light_data_units/${id}`));

/**
 * 轻量数据单元的标签列表
 */
export const getTagList = (
  id,
  params?: {
    systemFlag?: boolean; // 是否包含系统属性标签
  },
) => dataWrapper(requestUtil.get(`gwy-datasource/api/light_data_units/${id}/tags`, { params }));
