import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Select } from '@gwy/components-web';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';
import { ValueFormat } from './const';

export type ObjectItemProps = {
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  useIn?: UseIn;
  valueFormat?: ValueFormat;
  options?: any;
  label?: React.ReactNode;
};
type ValueItem = {
  data?: string;
  _id?: string;
  value?: string;
  original_data_id?: string;
};

const ObjectItem = memo<ObjectItemProps>(({ tag, field, value, onChange, options, useIn = UseIn.App, valueFormat = ValueFormat.ObjectArray }) => {
  const multipleChoice = field?.config?.multipleChoice;
  const _value = useMemo(() => {
    if (multipleChoice) {
      if (valueFormat === ValueFormat.ObjectArray) {
        return (value as ValueItem[])?.map((item) => item._id);
      }
      if (valueFormat === ValueFormat.ValueArray) {
        return value;
      }
      // 多选情况下，valueFormat 为 Object、Value 时，不合理，所以不处理
      return value;
    }

    if (valueFormat === ValueFormat.ObjectArray) {
      return (value as ValueItem[])?.[0]?._id;
    }
    if (valueFormat === ValueFormat.ValueArray) {
      return value?.[0];
    }
    if (valueFormat === ValueFormat.Object) {
      return (value as ValueItem)?._id;
    }

    return value;
  }, [multipleChoice, value, valueFormat]);

  const [ops, initValue] = useMemo(() => {
    const ops = options?.map((item) => {
      let opsValue = item?.value;
      if (multipleChoice) {
        if (valueFormat === ValueFormat.ObjectArray) {
          // return (value as ValueItem[])?.map((item) => item._id);
          opsValue = item?.data?.map((item) => item._id)?.join(',');
        }
        if (valueFormat === ValueFormat.ValueArray) {
          opsValue = item?.data?.join(',');
        }
        // 多选情况下，valueFormat 为 Object、Value 时，不合理，所以不处理
        opsValue = value;
      }

      if (valueFormat === ValueFormat.ObjectArray) {
        opsValue = item?.data?.[0]?._id;
      }
      if (valueFormat === ValueFormat.ValueArray) {
        opsValue = item?.data?.[0];
      }
      if (valueFormat === ValueFormat.Object) {
        opsValue = item?.data?._id;
      }
      return {
        label: item?.value,
        value: opsValue,
        data: item?.data,
      };
    });
    let initValue = '';
    if (Array.isArray(_value)) {
      if (typeof _value[0] === 'object') {
        initValue = _value.map((item) => item?._id)?.join(',');
      }
      initValue = _value?.join(',');
    } else {
      initValue = _value;
    }
    console.log(initValue, ops, options, value, _value, 'object-select------------');
    return [ops, initValue];
  }, [_value, options, multipleChoice]);

  return (
    <Select
      value={initValue}
      onChange={(e) => {
        onChange?.(options.find((option) => option.value === e)?.data);
      }}
      options={ops}
    />
  );
});

export default ObjectItem;
