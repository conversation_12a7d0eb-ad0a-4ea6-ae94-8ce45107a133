import { metaDataGroupList, metaDataListOptions, MetaDataType } from '@/const/metadata';
import { metaDataAPI } from '@/services';
import { MetaDataVO } from '@/types/metadata';
import { Collapse, CollapseProps, Input } from '@gwy/components-web';
import { groupBy, map } from 'lodash-es';
import { CSSProperties, useEffect, useState } from 'react';
import styles from './index.less';

type Props = {
  onSelect?: (item: MetaDataVO) => void;
  searchWrapperStyle?: CSSProperties;
};

const MetaDataList = ({ onSelect, searchWrapperStyle }: Props) => {
  const [searchValue, setSearchValue] = useState('');
  const [metaDataList, setMetaDataList] = useState<MetaDataVO[]>([]);

  const datametaListFiltered = metaDataList.filter((item) => item.name.toLowerCase().includes(searchValue.toLowerCase()));

  const items: CollapseProps['items'] = map(groupBy(datametaListFiltered, 'type'), (groupMetadataList, type) => {
    const group = metaDataGroupList.find((item) => item.value === type);
    return {
      key: type,
      label: (
        <div>
          <span>{group.label}</span>
          <span style={{ fontSize: 12, color: '#86909C', marginLeft: 10 }}>{group.desc}</span>
        </div>
      ),
      children: (
        <div className={styles.group}>
          {groupMetadataList.map((item) => (
            <div key={item.dataType} className={styles.item} onClick={() => onSelect?.(item)}>
              <span className={styles.icon}>{item.icon}</span>
              <span className={styles.label}>{item.name}</span>
            </div>
          ))}
        </div>
      ),
    };
  });

  const fetchMetaDataList = async () => {
    let data = await metaDataAPI.getMetadataList();
    if (data) {
      data = data.filter((item) => ![MetaDataType.Picture, MetaDataType.Audio, MetaDataType.Video].includes(item.dataType));
      for (const metaData of data) {
        Object.assign(metaData, { icon: metaDataListOptions.find((item) => item.dataType === metaData.dataType)?.icon });
      }
      setMetaDataList(data);
    }
  };

  useEffect(() => {
    fetchMetaDataList();
  }, []);

  if (!metaDataList.length) return null;

  return (
    <div className={styles.container}>
      <div style={searchWrapperStyle}>
        <Input.Search value={searchValue} onChange={(e) => setSearchValue(e.target.value)} placeholder="搜索数元名称" />
      </div>
      <div>
        <Collapse defaultActiveKey={items.map((item) => item.key as number)} items={items} />
      </div>
    </div>
  );
};

export default MetaDataList;
