import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Form } from '@gwy/components-web';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';
import AddressMeta from './index';

export type CodeMetaFormProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  label?: React.ReactNode;
  prefix?: any[];
  isPreview?: boolean;
  options?: any;
};

const AddressMetaForm = memo(({ useIn, tag, field, label, prefix, isPreview, options }: CodeMetaFormProps) => {
  const { required, readonly, config } = field || {};
  const { metaDataAddressDTO } = config || {};

  const rules = useMemo(
    () =>
      !readonly && [UseIn.App].includes(useIn)
        ? [
            {
              required: required,
              message: '请填写必填项',
            },
          ]
        : undefined,
    [required, readonly, useIn],
  );

  return (
    <Form.Item label={label} name={[...(prefix || []), getMetaTagUniqueId(field), 'value']} rules={rules} required={required && !readonly}>
      <AddressMeta useIn={useIn} tag={tag} field={field} isPreview={isPreview} options={options} />
    </Form.Item>
  );
});

export default AddressMetaForm;
