import { MetaDataContext } from '@/components/metadata-dialog/context';
import { genUuid } from '@/utils';
import { DeleteOutlined, MenuOutlined, MinusCircleOutlined, PlusCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { DndContext } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Checkbox, Collapse, Form, FormInstance, Tooltip } from '@gwy/components-web';
import classNames from 'classnames';
import { memo, useContext, useEffect, useState } from 'react';
import { CodeDTOKey } from '..';
import CodeRuleItem from './code-rule-item';
import styles from './index.less';

export const GROUP_LIST_KEY = 'configList';
export const RULE_LIST_KEY = 'elementList';

const SortItem = memo<{
  groupDisabled;
  groupField;
  field;
  index;
  style?;
  remove;
  form;
  allowDiscount?: boolean;
  goodsList?: any[];
  allowEdit?: boolean;
  allowEditDiscount?: boolean;
  checked?: boolean;
  approvedHasItem?: boolean;
  isOrgMeta?: boolean;
  onChecked?: (checked: boolean) => void;
}>(({ groupDisabled, groupField, field: ruleField, index, style, remove: ruleRemove, form, approvedHasItem, isOrgMeta }) => {
  console.log(groupDisabled, 'groupDisabled-----');
  const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, transition, isDragging } = useSortable({
    id: ruleField.key,
  });

  if (!form) return;

  return (
    <div
      ref={setNodeRef}
      style={{
        ...(style || {}),
        transform: CSS.Translate.toString(transform),
        transition,
        ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
      }}
      {...attributes}
    >
      <div key={ruleField.key} className={styles.ruleItem}>
        <CodeRuleItem
          form={form}
          prefix={[CodeDTOKey, GROUP_LIST_KEY, groupField.name, RULE_LIST_KEY]}
          fieldName={ruleField.name}
          disabled={groupDisabled}
          approvedHasItem={approvedHasItem}
          isOrgMeta={isOrgMeta}
        />
        <Form.Item name={[CodeDTOKey, GROUP_LIST_KEY, groupField.name, RULE_LIST_KEY, 'id']} hidden />
        {!groupDisabled && (
          <MinusCircleOutlined style={{ color: '#F53F3F' }} className={styles.ruleDelete} onClick={() => ruleRemove(ruleField.name)} />
        )}
        {!groupDisabled && <MenuOutlined ref={setActivatorNodeRef} className={styles.ruleDrag} {...listeners} />}
      </div>
    </div>
  );
});

type Props = {
  form?: FormInstance;
};

const CodeRules = ({ form }: Props) => {
  const { metaDataConfigApproved, disabled, isOrgMeta } = useContext(MetaDataContext);

  const [activeKey, setActiveKey] = useState([]);

  useEffect(() => {
    const groupList = form.getFieldValue([CodeDTOKey, GROUP_LIST_KEY]) || [];
    setActiveKey(groupList?.map((item) => item.id));
  }, []);

  const { allowOrgAddConfig } = metaDataConfigApproved?.[CodeDTOKey] || {};

  const addDisabled = isOrgMeta ? !allowOrgAddConfig && disabled : disabled;

  return (
    <div className={styles.container}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Form.Item label="配置编码格式" style={{ marginBottom: 0 }} wrapperCol={{ style: { display: 'none' } }} />
        {!isOrgMeta && (
          <Form.Item name={[CodeDTOKey, 'allowOrgAddConfig']} initialValue={false} valuePropName="checked" style={{ marginBottom: 0 }}>
            <Checkbox disabled={disabled || allowOrgAddConfig}>
              允许公司添加自定义编码
              <Tooltip title="设置开启后，无法再关闭此功能">
                <QuestionCircleOutlined />
              </Tooltip>
            </Checkbox>
          </Form.Item>
        )}
      </div>

      <Form.Item shouldUpdate noStyle>
        {() => {
          return (
            <Form.List name={[CodeDTOKey, GROUP_LIST_KEY]}>
              {(groupFields, { add: groupAdd, remove: groupRemove }) => {
                return (
                  <div>
                    <div className={styles.groupList}>
                      <Collapse activeKey={activeKey} onChange={(keys) => setActiveKey(keys)}>
                        {groupFields.map((groupField, groupIndex) => {
                          const group = form.getFieldValue([CodeDTOKey, GROUP_LIST_KEY, groupField.name]);
                          const approvedHasItem = !!metaDataConfigApproved?.[CodeDTOKey]?.[GROUP_LIST_KEY]?.some((item) => item.id === group.id);
                          // 无数元配置可添加，但已添加的不能更改
                          const groupDisabled = isOrgMeta ? (disabled && !allowOrgAddConfig) || approvedHasItem : disabled || approvedHasItem;
                          return (
                            <Collapse.Panel
                              key={group.id}
                              style={{ marginBottom: 12 }}
                              header={
                                <div className={styles.groupHeader}>
                                  <span className={styles.groupHeaderTitle}>组{groupIndex + 1}</span>
                                  <span className={styles.groupHeaderDesc}>移动组成元素可实现编码前后顺序变化</span>
                                  {!groupDisabled && (
                                    <DeleteOutlined
                                      style={{ color: '#4E5969' }}
                                      className={styles.groupDelete}
                                      onClick={() => groupRemove(groupField.name)}
                                    />
                                  )}
                                </div>
                              }
                            >
                              <div key={groupField.key} className={styles.groupItem}>
                                <Form.Item hidden name={[groupField.name, 'id']} />

                                <Form.List name={[groupField.name, RULE_LIST_KEY]}>
                                  {(ruleFields, { add: ruleAdd, remove: ruleRemove, move }) => {
                                    return (
                                      <div>
                                        <div className={styles.ruleList}>
                                          <DndContext
                                            modifiers={[restrictToVerticalAxis]}
                                            onDragEnd={({ active, over }) => {
                                              if (active.id !== over?.id) {
                                                const activeIndex = ruleFields.findIndex((f) => f.key === active?.id);
                                                const overIndex = ruleFields.findIndex((f) => f.key === over?.id);
                                                move(activeIndex, overIndex);
                                              }
                                            }}
                                          >
                                            <SortableContext items={ruleFields.map((f) => f.key)} strategy={verticalListSortingStrategy}>
                                              {ruleFields.map((field, index) => {
                                                return (
                                                  <SortItem
                                                    remove={ruleRemove}
                                                    key={field.key}
                                                    field={field}
                                                    index={index}
                                                    form={form}
                                                    groupField={groupField}
                                                    groupDisabled={!!groupDisabled}
                                                    approvedHasItem={approvedHasItem}
                                                    isOrgMeta={isOrgMeta}
                                                  />
                                                );
                                              })}
                                            </SortableContext>
                                          </DndContext>
                                        </div>

                                        {!groupDisabled && (
                                          <Button
                                            style={{ padding: 0 }}
                                            type="link"
                                            icon={<PlusCircleOutlined />}
                                            disabled={addDisabled}
                                            onClick={() =>
                                              ruleAdd({
                                                id: genUuid(),
                                              })
                                            }
                                          >
                                            添加编码元素
                                          </Button>
                                        )}
                                      </div>
                                    );
                                  }}
                                </Form.List>
                              </div>
                            </Collapse.Panel>
                          );
                        })}
                      </Collapse>
                    </div>

                    <div
                      className={classNames(styles.addGroupBtnWrapper, {
                        [styles.emptyList]: groupFields.length === 0,
                      })}
                    >
                      <Button
                        style={{ padding: 0 }}
                        type="link"
                        icon={<PlusCircleOutlined />}
                        disabled={addDisabled}
                        onClick={() => {
                          const id = genUuid();
                          groupAdd({
                            id,
                            [RULE_LIST_KEY]: [
                              {
                                id: genUuid(),
                              },
                            ],
                          });
                          setActiveKey((pre) => [...pre, id]);
                        }}
                      >
                        添加编码组
                      </Button>
                    </div>
                  </div>
                );
              }}
            </Form.List>
          );
        }}
      </Form.Item>
    </div>
  );
};

export default CodeRules;
