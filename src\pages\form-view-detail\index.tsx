import { useBridge, useRoute } from '@/hooks';
import { getFormViewInfo } from '@/services/form-manage';
import { Post } from '@/types/post';
import { genOpenMicroApp } from '@/utils';
import { Button, Form, LayoutPage, Tabs } from '@gwy/components-web';
import { get, pick } from 'lodash-es';
import { useEffect, useState } from 'react';
import { openAppApply } from '../app-apply';
import { openAppDesigner } from '../app-designer';
import FormData from './components/form-data';
import FormRule from './components/form-rule';
import styles from './index.less';

type IRouteState = {
  formVersionId?: number;
  selectedPost?: Post;
};
export const openFormViewDetail = genOpenMicroApp<IRouteState>('form-view-detail');

const { TabPane } = Tabs;
const FormViewDetail = () => {
  const bridge = useBridge();
  const route = useRoute<IRouteState>();
  const { formVersionId: defaultFormVersionId, selectedPost } = route.state || {};

  const [form] = Form.useForm();
  const formDetailTabs = [
    {
      key: '$form_rule_',
      label: '表单规则',
      value: '$form_rule_',
    },
    {
      key: '$form_data_',
      label: '表单数据',
      value: '$form_data_',
    },
  ];
  const [activeKey, setActiveKey] = useState(formDetailTabs[0]?.key);
  const [formAppDetail, setFormAppDetail] = useState<any>({});
  const findDetail = async (id) => {
    const data = await getFormViewInfo(id);
    setFormAppDetail(data);
  };
  useEffect(() => {
    defaultFormVersionId && findDetail(defaultFormVersionId);
  }, [defaultFormVersionId]);
  return (
    <LayoutPage
      footer={
        <>
          <Button onClick={() => bridge.close()}>取消</Button>
          <Button
            ghost
            type="primary"
            // 有配置数据单元才可预览
            disabled={!get(formAppDetail, ['dataUnits', 0, 'dataUnitId'])}
            onClick={() => {
              openAppApply(bridge, {
                state: {
                  isPreview: true,
                  app: pick(formAppDetail, 'appType', 'formVersionId', 'formId', 'name'),
                  post: selectedPost,
                },
              });
            }}
          >
            预览
          </Button>
          <Button
            type="primary"
            onClick={() => {
              openAppDesigner(bridge, {
                hideInMenu: true,
                state: {
                  ...((route as any).state || {}),
                  post: selectedPost,
                },
              });
              bridge.close();
            }}
          >
            配置
          </Button>
        </>
      }
      header={{ title: formAppDetail?.name || '' }}
      contentClassName={styles.layoutContent}
      onCancel={() => bridge.close()}
    >
      <div className={styles.container}>
        <Tabs activeKey={activeKey} onChange={(key) => setActiveKey(key)} type="card" size="small">
          <TabPane key="$form_rule_" tab="表单规则">
            <div>
              <FormRule formAppDetail={formAppDetail} form={form} />
            </div>
          </TabPane>
          <TabPane key="$form_data_" tab="表单数据">
            <div>
              <FormData formAppDetail={formAppDetail} />
            </div>
          </TabPane>
        </Tabs>
      </div>
    </LayoutPage>
  );
};

export default FormViewDetail;
