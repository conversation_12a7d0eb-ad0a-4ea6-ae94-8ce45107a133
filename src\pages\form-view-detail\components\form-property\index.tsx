import { Col, Form, Row } from '@gwy/components-web';

interface Iprops {
  form: any;
  formAppDetail: any;
}
const FormProperty = (props: Iprops) => {
  const { form, formAppDetail } = props;
  return (
    <>
      <div>
        <Form form={form}>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item label="创建人">
                <span>{formAppDetail.createUser}</span>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="创建时间">
                <span>{formAppDetail.createTime}</span>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="变更人">
                <span>{formAppDetail.updateUser}</span>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="变更时间">
                <span>{formAppDetail.updateTime}</span>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    </>
  );
};

export default FormProperty;
