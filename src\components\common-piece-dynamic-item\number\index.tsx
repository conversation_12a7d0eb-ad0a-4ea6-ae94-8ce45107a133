import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Select } from '@gwy/components-web';
import { memo } from 'react';
import { UseIn } from '../const';

export type NumberMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean;
  options?: any;
};

const NumberMeta = memo(({ useIn, tag, field, value, onChange, isPreview, options }: NumberMetaProps) => {
  const { placeholder } = field;
  const { decimalPlaces: precision, metaDataNumberDTO } = field?.config || {};
  const { rangeType, min, max } = metaDataNumberDTO || {};

  return (
    <Select
      value={value}
      options={options?.map((option) => ({
        label: option?.value,
        value: option?.value,
      }))}
      onChange={(e) => {
        onChange(options.find((option) => option?.value === e));
      }}
    />
  );
});

export default NumberMeta;
