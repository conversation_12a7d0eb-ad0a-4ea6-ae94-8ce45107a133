import { DatePicker, DatePickerProps } from '@gwy/components-web';
import dayjs from 'dayjs';

export type TimeValue = {
  raw_format?: string;
  raw_value?: string;
  std_format?: string;
  std_value?: string;
};

type Props = {
  value?: TimeValue;
  onChange?: (date: TimeValue) => void;
} & DatePickerProps;

const CustomDatePicker = ({ value, onChange, ...rest }: Props) => {
  return (
    <DatePicker
      value={value ? dayjs(value.raw_value) : undefined}
      onChange={(date, dateStr) => {
        onChange?.(
          dateStr
            ? {
                raw_value: dateStr as string,
              }
            : undefined,
        );
      }}
      {...rest}
    />
  );
};

export default CustomDatePicker;
