// import Empty from '@/components/empty';
import AddTagConfig from '@/pages/platform/metadata-manage/components/metadata-create-modal/components/add-tag-config';
import { Tabs } from '@gwy/components-web';
import { forwardRef, ForwardRefRenderFunction, useImperativeHandle, useRef, useState } from 'react';
import EditBasicInfo from '../edit-basicinfo';
import styles from './index.less';

type Iprops = {
  operateType: 'add' | 'edit' | 'approve' | 'view';
  dataSourceType: 'NORMAL' | 'GENERAL';
  editData: any;
  compareData?: any;
};
type IRef = any;
const MetaDataContent: ForwardRefRenderFunction<IRef, Iprops> = (props, ref) => {
  const { operateType, dataSourceType, editData, compareData } = props;
  const [activeKey, setActiveKey] = useState<string>('1');
  const tabBasicRef = useRef(null);
  const tabTagConfRef = useRef(null);
  const tabTagConfLightRef = useRef(null);

  useImperativeHandle(ref, () => ({
    getValues: async () => {
      let tagsLightValues = {};
      let tagsValues = {};
      const basicValues = await tabBasicRef.current?.getValues();
      if (tabTagConfLightRef.current) {
        tagsLightValues = await tabTagConfLightRef.current?.getValues();
      }
      if (tabTagConfRef.current) {
        tagsValues = await tabTagConfRef.current?.getValues();
      }

      return {
        basicValues,
        tagsValues,
        tagsLightValues,
      };
    },
  }));

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Tabs
          type="card"
          size="small"
          activeKey={activeKey}
          onChange={(key) => setActiveKey(key)}
          items={[
            { key: '1', label: '基础信息' },
            { key: '2', label: '标签配置' },
          ].filter(Boolean)}
        />
      </div>
      <div className={styles.content}>
        <div
          style={{
            display: activeKey !== '1' ? 'none' : '',
          }}
        >
          <EditBasicInfo ref={tabBasicRef} operateType={operateType} dataSourceType={dataSourceType} editData={editData} compareData={compareData} />
        </div>
        <div
          style={{
            display: activeKey !== '2' ? 'none' : '',
            height: '100%',
          }}
        >
          <AddTagConfig
            ref={tabTagConfRef}
            initialValue={[]}
            operateType={operateType}
            currentVersionId={editData?.dataUnitVersionId}
            preVersionId={editData?.preVersionId}
          />
        </div>
      </div>
    </div>
  );
};

export default forwardRef(MetaDataContent);
