import { getFormLayoutCol } from '@/utils';
import { CloseCircleFilled, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Form, Input } from '@gwy/components-web';
import { memo, useEffect } from 'react';
import styles from './index.less';

const formLayouts = getFormLayoutCol(60);
const InputEnumConfig = memo<{
  value?: string[];
  onChange?: (value: string[]) => void;
}>(({ value, onChange }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    const groups = form.getFieldValue('groups');
    if (value && groups) {
      if (String(value) !== String(groups)) {
        form.setFieldValue('groups', value);
      }
    } else {
      form.setFieldValue('groups', value);
    }
  }, [value]);

  return (
    <div className={styles.container}>
      <Form
        layout="horizontal"
        form={form}
        onValuesChange={(changedValues, values) => {
          onChange?.(values.groups);
        }}
        {...formLayouts}
      >
        <Form.List name="groups">
          {(fields, { add, remove }) => {
            return (
              <>
                {fields.map((field, index) => (
                  <div key={field.key} className={styles.itemBox}>
                    <Form.Item name={field.name} label={`选项${index + 1}:`}>
                      <Input maxLength={30} placeholder="请输入选项" />
                    </Form.Item>
                    <CloseCircleFilled onClick={() => remove(field.name)} className={styles.iconDel} />
                  </div>
                ))}
                <Button icon={<PlusCircleOutlined />} onClick={() => add()} type="link" size="small">
                  添加选项
                </Button>
              </>
            );
          }}
        </Form.List>
      </Form>
    </div>
  );
});

export default InputEnumConfig;
