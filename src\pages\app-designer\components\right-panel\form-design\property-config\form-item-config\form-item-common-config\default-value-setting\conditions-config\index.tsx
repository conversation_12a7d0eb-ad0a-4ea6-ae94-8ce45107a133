import DataQuerySetting from '@/components/data-query-setting';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { OrgDataUnitTagVO } from '@/types/org-data-unit';
import { EditOutlined } from '@ant-design/icons';
import { Button } from '@gwy/components-web';
import { useContext, useState } from 'react';

type Props = {
  tag?: OrgDataUnitTagVO;
  value?: any;
  onChange?: (value: any) => void;
};

const ConditionsConfig = ({ tag, value, onChange }: Props) => {
  const { allConfiguredDataUnitsWithTags, calcFields } = useContext(AppDesignerContext);

  const [openFilter, setOpenFilter] = useState({
    open: false,
    dataUnit: null,
  });

  // 当前数据单元的标签，变量可关联的标签
  const tags =
    allConfiguredDataUnitsWithTags
      ?.find((d) => d.dataUnitId === tag?.dataUnitId)
      ?.tagList?.map((tag) => {
        return {
          ...tag,
          belongDataUnitId: tag.dataUnitId,
          belongDataUnitName: tag.$$dataUnit.name,
        };
      }) || [];

  return (
    <>
      <Button
        size="small"
        type="link"
        icon={<EditOutlined />}
        style={{ padding: 0 }}
        onClick={() =>
          setOpenFilter((prev) => ({
            ...prev,
            open: true,
            dataUnit: value || {},
          }))
        }
      >
        {value?.conditionGroups?.length > 0 ? `${value?.conditionGroups?.length}个条件` : '数据查询规则'}
      </Button>

      {openFilter.open && (
        <DataQuerySetting
          dataUnitName={tag?.$$dataUnit?.name}
          dataUnitId={tag?.dataUnitId}
          conditionGroups={openFilter.dataUnit?.conditionGroups}
          extConfig={openFilter.dataUnit?.extConfig}
          tags={tags}
          canRelateTags={tags}
          calcFields={calcFields}
          onOk={(values) => {
            setOpenFilter({
              open: false,
              dataUnit: null,
            });
            onChange?.(values);
          }}
          onCancel={() => {
            setOpenFilter({
              open: false,
              dataUnit: null,
            });
          }}
        />
      )}
    </>
  );
};

export default ConditionsConfig;
