import { FormField, SourceType } from '@/types/form-field';
import { Location } from '@/types/statistic-item';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Select } from '@gwy/components-web';

type Props = {
  options?: any[];
  formFields?: FormField[];
  value?: Location;
  onChange?: (value: Location) => void;
};

const LocationSelect = ({ formFields = [], options = [], value, onChange }: Props) => {
  return (
    <Select
      style={{ width: 140 }}
      placeholder="请选择"
      allowClear={false}
      showSearch
      optionFilterProp="label"
      options={options}
      value={value ? (value.sourceType === SourceType.Field ? value.sourceId : `${value.dataUnitId}_${value.sourceId}`) : undefined}
      onChange={(v) => {
        if (!v) {
          onChange?.(undefined);
          return;
        }
        const formField = formFields?.find((f) => getMetaTagUniqueId(f) === v);
        onChange?.({
          sourceType: formField.sourceType,
          dataUnitId: formField.sourceType === SourceType.Field ? undefined : formField.dataUnitId,
          sourceId: formField.sourceType === SourceType.Field ? formField.fieldConfig.fieldId : formField.sourceId,
        });
      }}
    />
  );
};

export default LocationSelect;
