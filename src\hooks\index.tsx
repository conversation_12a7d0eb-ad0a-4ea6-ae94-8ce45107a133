import { BridgeProps, useBridge as useBridgeHook, useGlobalState as useGlobalStateHook, useRoute as useRouteHook } from '@gwy/libs-web';
import { useModel } from '@umijs/max';

export const useBridge = (opts?: BridgeProps) => useBridgeHook(useModel, opts);
export function useRoute<T>() {
  return useRouteHook<T>(useModel) || {};
}
export const useGlobalState = () => useGlobalStateHook(useModel) || {};
