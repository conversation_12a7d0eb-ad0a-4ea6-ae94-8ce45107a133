import { dataWrapper, requestUtil } from '@gwy/libs-web';

/**
 * 获取公司分类列表
 */

export const getOrgCategoryLists = (params) => dataWrapper(requestUtil.get(`gwy-datasource/api/org_data_units/categoryList`, { params }));

/**
 * 获取公司数据单元列表
 */
export const getOrgUnitLists = (params) =>
  dataWrapper(
    requestUtil.get(`gwy-datasource/api/org_data_units`, {
      params,
    }),
  );

/**
 * 获取公司数据单元历史版本列表
 */
export const getOrgHistoryVersionLists = (id) => dataWrapper(requestUtil.get(`gwy-datasource/api/org_data_units/${id}/versions`));

/**
 * 获取公司数据单元特定版本的详细信息
 */

export const getOrgUnitHistoryDetail = (id) => dataWrapper(requestUtil.get(`gwy-datasource/api/org_data_units/${id}`));

/**
 * 获取公司数据单元特定版本的标签列表
 */

export const getOrgTagLists = (id, params) =>
  dataWrapper(
    requestUtil.get(`gwy-datasource/api/org_data_units/${id}/tags`, {
      params,
    }),
  );

/**
 * 获取复数公司数据单元的标签列表
 */

export const getMutiOrgTagLists = (params) => dataWrapper(requestUtil.post(`gwy-datasource/api/org_data_units/dataUnitTags`, params));

/**
 * 公司数据单元标签配置添加
 */

export const addOrgTagConfig = (params) => dataWrapper(requestUtil.post(`gwy-datasource/api/org_data_units/tagConfigAdd`, params));

/**
 * 获取表单暂存列表数据
 */

export const getOrgDataUnitStashList = (params) => dataWrapper(requestUtil.post(`gwy-datasource-data/api/form_datas/getDraftList`, params));

/**
 * 获取审批的表单数据
 */

export const getOrgDataUnitApproveData = (formDataDraftId, tableId?: string) =>
  dataWrapper(
    requestUtil.get(`gwy-datasource-data/api/form_datas/${formDataDraftId}/getDraftDetail`, {
      params: { tableId },
    }),
  );

/**
 * 暂存数据---同意
 */

export const submitDataAgree = (formDataDraftId, params) =>
  dataWrapper(requestUtil.put(`gwy-datasource-data/api/form_datas/${formDataDraftId}/agreeDraft`, params));

/**
 * 暂存数据---拒绝
 */
export const submitDataReject = (formDataDraftId, params) =>
  dataWrapper(requestUtil.put(`gwy-datasource-data/api/form_datas/${formDataDraftId}/rejectDraft`, params));

/**
 * 暂存数据---退回
 */

export const submitDataBack = (formDataDraftId, params) =>
  dataWrapper(requestUtil.put(`gwy-datasource-data/api/form_datas/${formDataDraftId}/returnDraft`, params));
