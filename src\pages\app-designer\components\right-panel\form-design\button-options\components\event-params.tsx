import { EditOutlined } from '@ant-design/icons';
import { Button, Checkbox, Form, Modal, TreeSelect } from '@gwy/components-web';
import { useEffect, useMemo, useState } from 'react';
import { EventParamsMap, ExecutionEventType } from '../constants';
import styles from './index.less';

interface IProps {
  value?: any;
  onChange?: (value: any) => void;
  item?: { key?: ExecutionEventType; label?: string; disabled?: boolean };
  preName?: number;
  name?: number;
}

const EventParams = ({ value, onChange, item, preName, name }: IProps) => {
  const [form] = Form.useForm();

  const [open, setOpen] = useState(false);
  const entryPostTypes = Form.useWatch(['entryPostTypes'], form);

  const prePath = useMemo(() => {
    return ['buttonList', preName, 'events', name];
  }, [preName, name]);

  useEffect(() => {
    if (!open) return;
    form.setFieldsValue(value);
  }, [open]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onChange(values);
      setOpen(false);
    } catch (e) {}
  };

  const handelCancel = () => {
    setOpen(false);
  };

  const renderEventParams = () => {
    return (
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }} labelAlign="left">
        <div className={styles.title}>需满足以下参数必填</div>
        <Form.Item
          style={{ marginBottom: 0 }}
          name="entryPostTypes"
          initialValue={[]}
          rules={[
            {
              required: true,
              message: '请选择',
              validator: async (rule, value) => {
                if (![ExecutionEventType.ENTRY_POST].includes(item?.key) || (value && value.length > 0)) {
                  return Promise.resolve();
                }
                throw new Error();
              },
            },
          ]}
          hidden={![ExecutionEventType.ENTRY_POST].includes(item?.key)}
        >
          <Checkbox.Group
            style={{ marginBottom: 16 }}
            onChange={() => {
              form.setFieldValue('params', []);
            }}
          >
            <Checkbox value={1}>选择已有岗位</Checkbox>
            <Checkbox value={2}>自动生成岗位</Checkbox>
          </Checkbox.Group>
        </Form.Item>
        {(EventParamsMap[item?.key] || []).map((param, index) => {
          // const hidden =
          //   [ExecutionEventType.ENTRY_POST, ExecutionEventType.INVITE_POST].includes(item?.key) &&
          //   !entryPostTypes?.includes(1) &&
          //   param?.type === TAG_DATA_TYPE.ORG_POST.val;
          let hidden = false;
          return (
            <div key={index} className={styles.eventParamItem} style={{ marginBottom: hidden ? 0 : 16 }}>
              <Form.Item style={{ marginBottom: 0 }} name={['params', index]} rules={[{ required: !hidden, message: '请选择标签' }]} hidden={hidden}>
                <TreeSelect
                  style={{ width: 250 }}
                  styles={{
                    popup: {
                      root: { maxHeight: 400, overflow: 'auto' },
                    },
                  }}
                  placeholder="请选择"
                  treeData={[]}
                  allowClear
                  showSearch
                  treeDefaultExpandAll
                />
              </Form.Item>
              {!hidden && (
                <>
                  <span className={styles.midLabel}>传参至</span>
                  <span>{param.label}</span>
                </>
              )}
            </div>
          );
        })}
      </Form>
    );
  };

  const renderModalContent = () => {
    return renderEventParams();
  };

  return (
    <>
      <Button block icon={<EditOutlined />} onClick={() => setOpen(true)}>
        {item?.label}
      </Button>
      <Modal
        destroyOnHidden
        title={item?.label}
        open={open}
        onOk={handleOk}
        onCancel={handelCancel}
        width={500}
        styles={{
          body: {
            padding: 0,
            height: 400,
          },
        }}
      >
        <div className={styles.content}>{renderModalContent()}</div>
      </Modal>
    </>
  );
};

export default EventParams;
