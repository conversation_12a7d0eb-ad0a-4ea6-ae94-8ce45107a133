import { dataWrapper, requestUtil } from '@gwy/libs-web';

/**
 * 获取表单列表
 */
export const getFormLists = (params) => dataWrapper(requestUtil.post('gwy-datasource/api/forms/list', params));

/**
 * 获取表单应用列表
 */

export const getFormApplyLists = (params) => dataWrapper(requestUtil.post('gwy-datasource/api/forms/app_list', params));

/**
 * 获取表单权限列表
 */
export const getFormPermissionLists = (params) => dataWrapper(requestUtil.post('gwy-datasource/api/forms/auth_list', params));

/**
 * 表单启用
 */

export const updateFormApplyOpen = (id, params) => dataWrapper(requestUtil.put(`gwy-datasource/api/form_versions/${id}/enable`, params));

/**
 * 表单禁用
 */
export const updateFormApplyDisable = (id, params) => dataWrapper(requestUtil.put(`gwy-datasource/api/form_versions/${id}/disable`, params));

/**
 * 表单删除
 */

export const deleteFormApply = (id, postId) => dataWrapper(requestUtil.delete(`gwy-datasource/api/form_versions/${id}?postId=${postId}`));

/**
 * 获取最近使用的表单
 */

export const getRecentUsedForm = (params) =>
  dataWrapper(
    requestUtil.get(`gwy-datasource/api/forms/use/lastList`, {
      params,
    }),
  );

/**
 * 提交最近使用的表单信息
 */

export const updateRecentUsedForm = (params) => dataWrapper(requestUtil.post(`gwy-datasource/api/forms/use`, params));

/**
 * 表单查看信息
 */

export const getFormViewInfo = (id) => dataWrapper(requestUtil.get(`gwy-datasource/api/form_versions/${id}/view`));

/**
 * 表单排序
 */

export const updateFormSort = (params) => dataWrapper(requestUtil.put(`gwy-datasource/api/forms/sort/update`, params));

/**
 * 表单置顶
 */

export const updateFormTop = (params) => dataWrapper(requestUtil.put(`gwy-datasource/api/forms/sort/top_position`, params));

/**
 * 表单取消置顶
 */

export const updateFormCancelTop = (params) => dataWrapper(requestUtil.put(`gwy-datasource/api/forms/sort/cancel_top_position`, params));
