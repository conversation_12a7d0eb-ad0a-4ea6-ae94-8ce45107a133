import { Col, Form, Row } from '@gwy/components-web';

interface Iprops {
  form: any;
  formAppDetail: any;
}
const ApprovePendingInfo = (props: Iprops) => {
  const { form, formAppDetail } = props;
  const { notifyConfig } = formAppDetail || {};
  return (
    <>
      <div>
        <Form form={form}>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item label="消息提醒">
                <span>{notifyConfig?.notifyEnabled ? '开启' : '关闭'}</span>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="消息类型">
                {notifyConfig?.notifyTypeDesc} {notifyConfig?.intervalStr ? `(${notifyConfig?.intervalStr})` : ''}
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>
    </>
  );
};

export default ApprovePendingInfo;
