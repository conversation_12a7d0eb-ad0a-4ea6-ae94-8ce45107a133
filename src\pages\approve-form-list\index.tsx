import { useBridge } from '@/hooks';
import { ProTable } from '@ant-design/pro-components';
import { Button, LayoutPage } from '@gwy/components-web';
import { useCallback, useEffect, useMemo, useState } from 'react';
import styles from './index.less';

const ApproveFormList = () => {
  const bridge = useBridge();
  const [dataSource, setDataSource] = useState(null);
  const [searchParams, setSearchParams] = useState(null);
  const [readIds, setReadIds] = useState([]);
  const tags = [
    {
      tagVersionId: 2437,
      preVersionId: 2433,
      tagId: 1199,
      name: '标签1',
      metaDataId: 5,
      tagMetaDataConfig: {
        type: 'UNIT',
        metaDataId: 5,
        metaDataUnitDTO: {
          type: 'WEIGHT',
          configList: ['TON', 'KILOGRAM', 'GRAM', 'JIN'],
          rangeType: 'UNLIMITED',
          min: null,
          max: null,
          unit: 'KILOGRAM',
        },
      },
      identifierFlag: false,
      enable: true,
      operateType: null,
      createTime: '2025-06-13 16:26:01',
      updateTime: '2025-06-13 16:26:10',
      bizCreateTime: '2025-06-13 16:24:53',
      bizUpdateTime: null,
      deletedFlag: false,
    },
    {
      tagVersionId: 2438,
      preVersionId: 2434,
      tagId: 1198,
      name: '标签2',
      metaDataId: 2,
      tagMetaDataConfig: {
        type: 'TEXT',
        metaDataId: 2,
        metaDataTextDTO: {
          inputType: 'MANUAL',
          allowedCharacters: ['CHINESE', 'ENGLISH_LOWERCASE', 'ENGLISH_UPPERCASE', 'NUMBER'],
          maxLength: 2000,
          enumList: null,
        },
      },
      identifierFlag: true,
      enable: true,
      operateType: null,
      createTime: '2025-06-13 16:26:01',
      updateTime: '2025-06-13 16:26:10',
      bizCreateTime: '2025-06-13 16:24:14',
      bizUpdateTime: '2025-06-13 16:26:10',
      deletedFlag: false,
    },
    {
      tagVersionId: 2439,
      preVersionId: 2435,
      tagId: 875,
      name: '标签3',
      metaDataId: 2,
      tagMetaDataConfig: {
        type: 'TEXT',
        metaDataId: 2,
        metaDataTextDTO: {
          inputType: 'MANUAL',
          allowedCharacters: ['CHINESE', 'ENGLISH_LOWERCASE', 'ENGLISH_UPPERCASE', 'NUMBER'],
          maxLength: 2000,
          enumList: null,
        },
      },
      identifierFlag: false,
      enable: true,
      operateType: null,
      createTime: '2025-06-13 16:26:01',
      updateTime: '2025-06-13 16:26:10',
      bizCreateTime: '2025-06-12 16:53:08',
      bizUpdateTime: null,
      deletedFlag: false,
    },
    {
      tagVersionId: 2440,
      preVersionId: 2436,
      tagId: 876,
      name: '标签4',
      metaDataId: 1,
      tagMetaDataConfig: {
        type: 'NUMBER',
        metaDataId: 1,
        metaDataNumberDTO: {
          rangeType: 'UNLIMITED',
          min: null,
          max: null,
        },
      },
      identifierFlag: false,
      enable: true,
      operateType: null,
      createTime: '2025-06-13 16:26:01',
      updateTime: '2025-06-13 16:26:10',
      bizCreateTime: '2025-06-12 16:53:08',
      bizUpdateTime: null,
      deletedFlag: false,
    },
  ];

  useEffect(() => {
    const ids = window.localStorage.getItem(`readed_app1`);
    ids && setReadIds(ids.split(','));
  }, []);

  const getDataSource = useCallback(async () => {
    const mockData = [
      {
        _id: 'mock_id_1',
        '1199': '张三',
        '1198': '男',
        '875': '产品经理A',
        '876': '2022-01-01',
      },
      {
        _id: 'mock_id_2',
        '1199': '张四',
        '1198': '女',
        '875': '产品是大非',
        '876': '2022-08-01',
      },
      {
        _id: 'mock_id_3',
        '1199': '李三',
        '1198': '男',
        '875': '经理A',
        '876': '2022-09-01',
      },
      {
        _id: 'mock_id_4',
        '1199': '大概违法',
        '1198': '男',
        '875': '产品A',
        '876': '2022-01-09',
      },
    ];
    const dataSourceObj = {
      total: mockData.length + 1,
      list: mockData,
    };
    setDataSource(dataSourceObj);
  }, []);

  useEffect(() => {
    getDataSource();
  }, []);

  const pagination = useMemo(
    () => ({
      total: dataSource?.total,
      pageSize: searchParams?.pageSize,
      current: searchParams?.current,
      showSizeChanger: true,
    }),
    [dataSource?.total, searchParams],
  );

  const handleViewClick = (item) => {
    console.log(item, 'item');
    setReadIds((pre) => {
      const newIds = [...pre, item._id];
      window.localStorage.setItem(`readed_app1`, newIds.join(','));
      return newIds;
    });
  };

  const getColumns = useMemo(() => {
    const cols = (tags || [])?.map((item) => {
      const { name, tagId } = item;
      const column: any = {
        dataIndex: tagId?.toString(),
        title: name,
        width: 150,
        render: (v, r, k) => {
          if (typeof v === 'string' || typeof v === 'number') {
            if (!v) return '-';
            return v;
          }
          return <span>-</span>;
        },
      };
      return column;
    });

    cols.unshift({
      dataIndex: 'index',
      title: '序号',
      width: 85,
      align: 'left',
      render: (v, r, k) => {
        if (r.parent_id) return null;
        const dataSourceItem = dataSource.list[k];
        const unRead = readIds && !readIds.includes(r._id);
        return (
          <span className={styles.seq}>
            {k + 1}
            {unRead && <span className={styles.unRead} />}
            {/* {dataSourceItem?.main_split_data_is === true && <TagCustomIcon text="主" isSmall className={styles.icon} />} */}
            {/* {dataSourceItem?.main_split_data_is === false && (
                <TagCustomIcon text="子" isSmall className={styles.icon} />
              )} */}
          </span>
        );
      },
    });
    cols.push({
      title: '操作',
      dataIndex: 'operate',
      render: (v, r) => {
        const btn = (
          <Button type="link" onClick={() => handleViewClick(r)}>
            查看
          </Button>
        );
        return btn;
      },
      align: 'center',
      fixed: 'right',
      width: 80,
    });
    return cols;
  }, [dataSource, tags]);
  return (
    <LayoutPage footer={false} header={{ title: '表单管理' }} contentClassName={styles.layoutContent} onCancel={() => bridge.close()}>
      <div>
        <ProTable
          dataSource={dataSource?.list || []}
          columns={getColumns}
          pagination={pagination}
          bordered
          search={false}
          options={{
            setting: true,
            reload: false,
            density: false,
          }}
        />
      </div>
    </LayoutPage>
  );
};

export default ApproveFormList;
