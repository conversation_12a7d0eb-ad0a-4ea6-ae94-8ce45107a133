import { AddressType, addressTypeOptions, RangeType, rangeTypeOptions } from '@/const/metadata';
import { commonAPI } from '@/services';
import { maxTagPlaceholder } from '@/utils/ui-util';
import { Cascader, Form, FormInstance, Radio } from '@gwy/components-web';
import { useContext, useEffect, useState } from 'react';
import { MetaDataContext } from '../../context';

export const AddressDTOKey = 'metaDataAddressDTO';

type Props = {
  form?: FormInstance;
};

const AddressConfig = ({ form }: Props) => {
  const { disabled, hasApproved } = useContext(MetaDataContext);

  const rangeType = Form.useWatch([AddressDTOKey, 'rangeType'], form);

  const [areaTree, setAreaTree] = useState([]);

  const fetchAreaTree = async () => {
    const data = await commonAPI.getAreaTree();
    const loopTree = (list) => (list || []).map((li) => ({ ...li, label: li.name, value: li.code, children: loopTree(li.children) }));
    setAreaTree(loopTree(data));
  };

  useEffect(() => {
    fetchAreaTree();
  }, []);

  return (
    <div>
      <Form.Item label="地址类型" name={[AddressDTOKey, 'type']} rules={[{ required: true, message: '请选择' }]} initialValue={AddressType.area}>
        <Radio.Group disabled={hasApproved || disabled} options={addressTypeOptions} />
      </Form.Item>

      <Form.Item
        label="地址范围"
        name={[AddressDTOKey, 'rangeType']}
        rules={[{ required: true, message: '请选择' }]}
        initialValue={RangeType.unlimited}
      >
        <Radio.Group disabled={hasApproved || disabled} options={rangeTypeOptions} />
      </Form.Item>
      {rangeType !== RangeType.unlimited && (
        <Form.Item name={[AddressDTOKey, 'codeList']} rules={[{ required: true, message: '请选择' }]}>
          <Cascader style={{ width: '100%' }} options={areaTree} multiple maxTagCount="responsive" maxTagPlaceholder={maxTagPlaceholder} />
        </Form.Item>
      )}
    </div>
  );
};

export default AddressConfig;
