import type { DragEndEvent } from '@dnd-kit/core';
import { closestCenter, DndContext, PointerSensor, useSensor } from '@dnd-kit/core';
import { arrayMove, horizontalListSortingStrategy, SortableContext, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Input, Tabs } from '@gwy/components-web';
import type { TabsProps } from 'antd';
import type { Tab } from 'rc-tabs/lib/interface';
import React, { useEffect, useState } from 'react';

interface DraggableTabPaneProps extends React.HTMLAttributes<HTMLDivElement> {
  'data-node-key': string;
}

const DraggableTabNode: React.FC<Readonly<DraggableTabPaneProps>> = ({ className, ...props }) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: props['data-node-key'],
  });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    cursor: 'move',
  };

  return React.cloneElement(props.children as React.ReactElement<any>, {
    ref: setNodeRef,
    style,
    ...attributes,
    ...listeners,
  });
};
// type AntdTab extends CompatibilityProps{
//     Omit<Tab, 'destroyInactiveTabPane'> & CompatibilityProps
// }
interface CompatibilityProps {
  /** @deprecated Please use `destroyOnHidden` instead */
  destroyInactiveTabPane?: boolean;
  /**
   * @since 5.25.0
   */
  destroyOnHidden?: boolean;
}
export interface Dashboard extends CompatibilityProps, Tab {
  dashboardName: string;
  dashboardId: string | number;
  label: string;
  key: any;
  [key: string]: any;
}
type Props = {
  disabled?: boolean;
  tabs: Dashboard[];
  activeKey: string;
  canAdd?: boolean;
  onDashboardSwitch: (dashboardId: string) => void;
  onChangeDashboardName: (dashboardId: any, dashboardName: string, tabItem?: Dashboard) => void;
  onAddDashboard: () => void;
  onTabsChange?: (tabs: any[]) => void;
};

const DragTabs = ({ disabled, tabs, activeKey, onDashboardSwitch, onChangeDashboardName, onAddDashboard, canAdd, onTabsChange }: Props) => {
  const [items, setItems] = useState<NonNullable<TabsProps['items']>>(tabs);
  const tabsLen = (tabs || []).length;
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    setItems(
      tabs.map((item) => ({
        ...item,
        key: item.key?.toString(),
      })),
    );
  }, [tabs]);

  const sensor = useSensor(PointerSensor, { activationConstraint: { distance: 10 } });

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      const activeIndex = tabs.findIndex((i) => i.key === active?.id);
      const overIndex = tabs.findIndex((i) => i.key === over?.id);
      //   console.log(activeIndex, overIndex, active, 'activeIndex, overIndex');
      const newTabs = arrayMove(tabs, activeIndex, overIndex);
      onTabsChange?.(newTabs);
    }
  };

  return (
    <Tabs
      items={items}
      type="editable-card"
      size="small"
      activeKey={activeKey}
      onTabClick={(key) => {
        onDashboardSwitch(key);
      }}
      onEdit={() => {
        onAddDashboard();
      }}
      hideAdd={!canAdd}
      renderTabBar={(tabBarProps, DefaultTabBar) => (
        <DndContext sensors={[sensor]} onDragEnd={onDragEnd} collisionDetection={closestCenter}>
          <SortableContext items={items.map((i) => i.key)} strategy={horizontalListSortingStrategy}>
            <DefaultTabBar {...tabBarProps}>
              {(node) => {
                // console.log(node, 'node-----');
                const tabItem = tabs.find((i) => i.key === node.key);
                // const inputValue = tabItem?.dashboardName || '';
                return (
                  <>
                    {isEdit && activeKey === tabItem.key ? (
                      <Input
                        autoFocus
                        size="small"
                        type="text"
                        maxLength={10}
                        // className={styles.titleInput}
                        defaultValue={tabItem.dashboardName}
                        onBlur={(e) => {
                          const dashboardId = tabItem.dashboardId === 'tempdashboard' ? undefined : tabItem.dashboardId;

                          setIsEdit(false);
                          onChangeDashboardName(dashboardId, e.target.value, tabItem);
                        }}
                      />
                    ) : (
                      <DraggableTabNode {...(node as React.ReactElement<DraggableTabPaneProps>).props} key={node.key}>
                        <div
                          onDoubleClick={(e) => {
                            if (disabled || !canAdd || tabItem.disabled) {
                              return;
                            }
                            e.stopPropagation();
                            // console.log(node, 'nodeinput');
                            setIsEdit(true);
                          }}
                        >
                          {node}
                        </div>
                        {/* {node} */}
                      </DraggableTabNode>
                    )}
                  </>
                );
              }}
            </DefaultTabBar>
          </SortableContext>
        </DndContext>
      )}
    />
  );
};

export default DragTabs;
