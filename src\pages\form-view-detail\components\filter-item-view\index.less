.filterWrapper {
  .filterContainer {
    &:last-of-type {
      padding-bottom: 1px;
    }

    padding: 12px 16px 0;

    &.blueTheme {
      background: #f1f8ff;
      border: 1px dashed #c9cdd4;
    }

    &.whiteTheme {
      background: #fff;
    }

    .ellipsis {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      word-break: break-all;
    }

    .filterLabel {
      font-weight: bold;
      margin-bottom: 12px;
    }

    .filterItem {
      // height: 32px;
      // line-height: 32px;
      display: flex;
      align-items: center;
      column-gap: 16px;
      margin-bottom: 12px;

      .commomWrap {
        .ellipsis();

        min-width: 60px;
        width: fit-content;
        background: #fff;
        padding: 4px 8px;
        border: 1px solid #ede6e6;

        &.tagWrap {
          min-width: 120px;
          max-width: 160px;
        }
      }

      .valueWrap {
        background: #fff;
        .ellipsis();

        flex-grow: 1;
        min-width: 300px;
        // max-width: 500px;
        padding: 4px 8px;
        border: 1px solid #ede6e6;
      }
    }

    // .groupLogic {
    //     height: 32px;
    //     line-height: 32px;
    // }
  }
}
