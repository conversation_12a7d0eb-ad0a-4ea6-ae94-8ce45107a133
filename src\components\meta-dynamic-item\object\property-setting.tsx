import { AppDesignerContext } from '@/pages/app-designer/context';
import { getVersionTagList } from '@/services/datasource';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Form, Radio } from '@gwy/components-web';
import { useContext, useEffect, useState } from 'react';
import FilterGroupWrapper from './filter-group-wrapper';

type Props = {
  tag: DataUnitTagVO;
  field: FormField;
  [property: string]: any;
};

const ObjectPropertySetting = ({ tag, field }: Props) => {
  const { calcFields } = useContext(AppDesignerContext);
  const form = Form.useFormInstance();

  const [tags, setTags] = useState([]);

  const fetchTags = async () => {
    const data = await getVersionTagList(tag.tagMetaDataConfig?.metaDataObjectDTO?.currentVersionId);
    const tags = (data || []).map((item) => ({ ...item, belongDataUnitId: tag.dataUnitId, belongDataUnitName: tag.dataUnitName }));
    setTags(tags);
  };

  useEffect(() => {
    fetchTags();
  }, []);

  useEffect(() => {
    form.setFieldsValue(field.config);
  }, []);

  return (
    <div>
      <Form.Item label="选项类型" name={['multipleChoice']}>
        <Radio.Group>
          <Radio value={true}>多选</Radio>
          <Radio value={false}>单选</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item layout="horizontal" label="选项范围" name={['conditionGroups']}>
        <FilterGroupWrapper tags={tags} canRelateTags={tags} calcFields={calcFields} />
      </Form.Item>
    </div>
  );
};

export default ObjectPropertySetting;
