.container {
  height: 100%;

  :global {
    .ant-collapse {
      border: none;
      background-color: transparent;

      .ant-collapse-item {
        border-bottom: none;

        .ant-collapse-header {
          padding: 0;
          margin: 10px 0;
          align-items: center;
        }

        .ant-collapse-content {
          background-color: transparent;
          border-top: none;

          .ant-collapse-content-box {
            padding: 0;
          }
        }
      }
    }
  }
}

.group {
  display: flex;
  flex-wrap: wrap;
  gap: 14px;

  .item {
    width: 66px;
    height: 66px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 2px;
    border: 1px solid #e5e6eb;
    border-radius: 3px;
    background-color: #fff;
    cursor: pointer;

    &:hover {
      border: 1px solid #4d7bf6;
    }

    .icon {
      display: flex;
      width: 30px;
      height: 30px;
      justify-content: center;
      align-items: center;
      border-radius: 6px;
      background-color: #e8f3ff;
      color: #4d7bf6;
      font-size: 20px;
    }

    .label {
      font-size: 12px;
    }
  }
}
