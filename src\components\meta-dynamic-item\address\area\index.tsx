import { AddressType, RangeType } from '@/const/metadata';
import { commonAPI } from '@/services';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { maxTagPlaceholder } from '@/utils/ui-util';
import { Cascader, Input } from '@gwy/components-web';
import { isEmpty } from 'lodash-es';
import { memo, useEffect, useMemo, useState } from 'react';
import { AddressRange } from '../const';

type IValue = {
  region_code?: string;
  city_code?: string;
  province_code?: string;
  region_name?: string;
  city_name?: string;
  province_name?: string;
  detail?: string;
};

export const addressRangeCount = {
  [AddressRange.province]: 1,
  [AddressRange.city]: 2,
  [AddressRange.region]: 3,
};
export const loopTreeRange = (tree, range) =>
  (tree || []).map((li) => ({ ...li, label: li.name, value: li.code, children: range === 1 ? [] : loopTreeRange(li.children, range - 1) }));

export const treeFilter = (tree: any[], filterOptions: string[], filterType: RangeType) => {
  const cascaderOptions = [[], [], []];
  filterOptions.forEach((cascader) => {
    const [provinceCode, cityCode, regionCode] = cascader;
    if (provinceCode) {
      cascaderOptions[0].push({
        code: provinceCode,
        isLeaf: !cityCode,
      });
    }
    if (cityCode) {
      cascaderOptions[1].push({
        code: cityCode,
        isLeaf: !regionCode,
      });
    }
    if (regionCode) {
      cascaderOptions[2].push({
        code: regionCode,
        isLeaf: true,
      });
    }
  });

  const loopTree = (list, index) => {
    const results = [];
    const filterOptions = cascaderOptions[index];
    list.forEach((item) => {
      const { isLeaf, code } = filterOptions.find((opt) => opt.code === item.code) || {};
      if ((code && filterType === RangeType.contain) || (!code && filterType === RangeType.exclude)) {
        results.push({
          ...item,
          children: isLeaf ? item.children : Array.isArray(item.children) ? loopTree(item.children, index + 1) : item.children,
        });
      }
    });
    return results;
  };

  return loopTree(tree, 0);
};
const AreaItem = memo<{
  tag?: DataUnitTagVO;
  field?: FormField;
  propsStyles?: React.CSSProperties;
  value?: IValue[];
  onChange?: (val?: IValue[]) => void;
}>(({ value, onChange, tag, field, propsStyles }) => {
  const [areaTree, setAreaTree] = useState([]);
  const { metaDataAddressDTO } = tag.tagMetaDataConfig || {};
  const { codeList, rangeType, type } = metaDataAddressDTO || {};

  const { config, placeholder } = field || {};
  const { addressRange } = config || {};

  const [cascaderValue, detail] = useMemo(() => {
    const { region_code, city_code, province_code, detail } = value?.[0] || {};
    const codes = [];
    if (province_code) {
      codes.push(province_code);
    }
    if (city_code) {
      codes.push(city_code);
    }
    if (region_code) {
      codes.push(region_code);
    }
    return [codes, detail];
  }, [value]);

  const filterTree = useMemo(() => {
    if (Array.isArray(areaTree) && areaTree.length > 0) {
      const rangeTree = loopTreeRange(areaTree, addressRangeCount[addressRange]);
      let filterTree = rangeTree;
      if (rangeType !== RangeType.unlimited) {
        filterTree = treeFilter(filterTree, codeList, rangeType);
      }
      return filterTree;
    }
    return [];
  }, [areaTree, addressRange, rangeType, codeList]);

  const handleChange = (val, isCascader = false) => {
    let newValue = { ...(value?.[0] || {}) };
    if (isCascader) {
      if (Array.isArray(val) && val.length > 0) {
        const [province, city, region] = val;
        newValue.province_code = province.value;
        newValue.province_name = province.label;
        newValue.city_code = city?.value;
        newValue.city_name = city?.label;
        newValue.region_code = region?.value;
        newValue.region_name = region?.label;
      } else {
        newValue = detail ? { detail } : undefined;
      }
    } else {
      if (val) {
        newValue.detail = val;
      } else {
        delete newValue.detail;
        if (isEmpty(newValue)) {
          newValue = undefined;
        }
      }
    }
    return onChange([newValue]);
  };

  useEffect(() => {
    (async () => {
      const data = await commonAPI.getAreaTree();
      setAreaTree(data || []);
      setInitialized(true);
    })();
  }, []);

  // 可选层级改变时，清空默认值
  const [initialized, setInitialized] = useState(false);
  useEffect(() => {
    if (!initialized) {
      return;
    }
    handleChange([], true);
  }, [addressRange]);

  return (
    <div style={{ display: 'flex', alignItems: 'center', columnGap: 8, rowGap: 8, flexWrap: 'wrap' }}>
      <Cascader
        value={cascaderValue}
        onChange={(vals, opts) => handleChange(opts, true)}
        style={{ flexGrow: 1, minWidth: 180, ...(propsStyles || {}) }}
        options={filterTree}
        placeholder={placeholder || '请选择'}
        maxTagCount="responsive"
        maxTagPlaceholder={maxTagPlaceholder}
      />
      {AddressType.detail === type && (
        <Input value={detail} onChange={(e) => handleChange(e.target.value, false)} style={{ flexGrow: 1, minWidth: 180, ...(propsStyles || {}) }} />
      )}
    </div>
  );
});

export default AreaItem;
