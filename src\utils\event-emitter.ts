import EventEmitter from 'events';

export enum EventName {
  // 动态入参关联标签值改变
  dynamicParamChange = 'dynamicParamChangeMessage',
}

const eventEmitter = new EventEmitter();

// 触发--动态入参关联标签值改变
export function dispatchDynamicParamChange(params: { dataUnitId?: number; tagId?: number; value?: any; callback?: (val?: any) => void }) {
  eventEmitter.emit(EventName.dynamicParamChange, params);
}
export default eventEmitter;
