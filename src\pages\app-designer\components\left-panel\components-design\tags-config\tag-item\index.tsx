import { MetaTagType } from '@/const/metadata';
import { AppDesignerContext } from '@/pages/app-designer/context';
import MetaDataEdit from '@/pages/platform/metadata-manage/components/metadata-edit-modal';
import { openModalType, openType } from '@/pages/system-datameta-manage/components/system-datameta-list';
import { OrgDataUnitTagVO } from '@/types/org-data-unit';
import { POST_PROPERTY } from '@/types/post';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Button, Modal, TextEllipsisTooltip } from '@gwy/components-web';
import classNames from 'classnames';
import { useContext, useState } from 'react';
import styles from './index.less';

type Props = {
  tag?: OrgDataUnitTagVO;
  active?: boolean;
};

const TagItem = ({ tag, active }: Props) => {
  const { post, fetchAllConfiguredDataUnitsWithTags } = useContext(AppDesignerContext);

  const [openModal, setOpenModal] = useState<openModalType>({
    open: true,
    type: openType.close,
    data: null,
  });

  return (
    <div
      className={classNames(styles.container, {
        [styles.active]: active,
      })}
    >
      <span className={styles.name}>
        <TextEllipsisTooltip text={tag?.name} />
      </span>

      <span className={styles.right}>
        {tag.noMetaDataCodeConfigFlag && (
          <InfoCircleOutlined
            style={{ color: '#FF7D00' }}
            onClick={() => {
              const modal = Modal.confirm({
                title: '温馨提示',
                content: (
                  <div>
                    当前标签没有可选编码规则，需法人/管理员去数据单元管理中设置{' '}
                    {[POST_PROPERTY.TOP, POST_PROPERTY.ADMIN1, POST_PROPERTY.ADMIN2].includes(post.property) && (
                      <Button
                        type="link"
                        onClick={() => {
                          let checkType = openType.viewDataSource;
                          const item = tag.$$dataUnit;
                          setOpenModal({
                            open: true,
                            type: checkType,
                            data: {
                              currentVersionId: item.dataUnitVersionId,
                              addModalType: 'add',
                              unitData: item,
                            },
                          });

                          modal.destroy();
                        }}
                      >
                        去设置
                      </Button>
                    )}
                  </div>
                ),
              });
            }}
          />
        )}
        {tag.type === MetaTagType.System && <span className={styles.tagSource}>系</span>}
      </span>

      {openModal.open && [openType.editDatasource, openType.viewDataSource].includes(openModal.type) && (
        <MetaDataEdit
          operateType={openModal.type}
          onCancel={() => setOpenModal((prev) => ({ ...prev, open: false, type: openType.close }))}
          onOk={() => setOpenModal((prev) => ({ ...prev, open: false, type: openType.close }))}
          refresh={() => {}}
          {...openModal.data}
          dataSourceType={tag.$$dataUnit?.type}
          formPage="system-datameta-manage"
          incompleteTag={tag}
          onCompleteTag={() => {
            setOpenModal({ open: false, type: openType.close, data: null });
            fetchAllConfiguredDataUnitsWithTags();
          }}
        />
      )}
    </div>
  );
};

export default TagItem;
