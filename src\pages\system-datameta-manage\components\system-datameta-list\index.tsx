import { APPROVE_ENUM, APPROVE_TYPE, OPEN_TYPE } from '@/const/datasource';
import { useBridge, useGlobalState } from '@/hooks';
import MetaDataEdit from '@/pages/platform/metadata-manage/components/metadata-edit-modal';
import { orgAPI } from '@/services';
import { getOrgCategoryLists, getOrgUnitLists } from '@/services/system-datameta';
import { MetadataManageTypes } from '@/types';
import { formatDateTime, metadataUtils } from '@/utils';
import { CaretDownOutlined, ExclamationCircleOutlined, InfoCircleOutlined, LeftOutlined, LoadingOutlined } from '@ant-design/icons';
import { Button, Dropdown, Empty, Input, MenuProps, Select, Space, Tabs, TextEllipsisTooltip, Tooltip } from '@gwy/components-web';
import classNames from 'classnames';
import { uniqueId } from 'lodash-es';
import { useEffect, useState } from 'react';
import styles from './index.less';
export type openModalType = {
  type: openType;
  open: boolean;
  data: any;
};
export enum openType {
  close = 'close',
  addDatasource = 'add',
  editDatasource = 'edit',
  viewDataSource = 'view',
  approveDataSource = 'approve',
}
export enum DataSourceTypes {
  GENERAL = 'GENERAL',
  NORMAL = 'NORMAL',
}

interface Iprops {
  onCancle?: () => void;
  onBack?: () => void;
  loading: boolean;
  setLoading: (loading: boolean) => void;
}

enum PostPropertyEnums {
  EXECUTE = 'EXECUTE', // 执行岗
  MANAGE_EXECUTE = 'MANAGE_EXECUTE', // 管理执行岗
  MANAGE = 'MANAGE', // 管理岗
  TOP = 'TOP', // 顶级岗位（法人岗）
  ADMIN1 = 'ADMIN1', // 岗位管理员1
  ADMIN2 = 'ADMIN2', // 岗位管理员2
  CHIEF_SUPERVISOR = 'CHIEF_SUPERVISOR', // 监事长岗
  SUPERVISOR_MEMBER = 'SUPERVISOR_MEMBER', // 监事岗
  BOARD_CHAIRMAN = 'BOARD_CHAIRMAN', // 董事长岗
  BOARD_MEMBER = 'BOARD_MEMBER', // 董事岗
}

enum DeptPropertyenums {
  TOP = 'TOP', // 顶级部门
  BOARD_OF_DIRECTORS = 'BOARD_OF_DIRECTORS', // 董事部门
  SUPERVISORY = 'SUPERVISORY', // 监事部门
  NORMAL_OPERATION = 'NORMAL_OPERATION', // 普通运营部门
  DEPUTY_OPERATION = 'DEPUTY_OPERATION', // 副级运营部门
  BRANCH = 'BRANCH', // 分公司部门
  AFFILIATED_ORGANIZATION = 'AFFILIATED_ORGANIZATION', // 附建组织部门
  BLOC_MANAGEMENT = 'BLOC_MANAGEMENT', // 集团管理机构
}
const SystemDataMetaList = (props: Iprops) => {
  const [classifyTabs, setClassifyTabs] = useState([
    {
      categoryId: null,
      categoryName: '全部',
      label: '全部',
      key: null,
    },
  ]);
  const bridge = useBridge();
  const [currentClassify, setCurrentClassify] = useState(null);
  const [datasourceList, setDatasourceList] = useState<MetadataManageTypes.DataUnitVO[]>(null);
  const [openModal, setOpenModal] = useState<openModalType>({
    open: true,
    type: openType.close,
    data: null,
  });
  const [dropOpen, setDropOpen] = useState({
    tempUnitId: null,
    open: false,
  });
  const [searchName, setSearchName] = useState('');

  const leftTabDatas = [
    {
      label: '通用数据单元',
      key: DataSourceTypes.GENERAL,
    },
    {
      label: '普通数据单元',
      key: DataSourceTypes.NORMAL,
    },
  ];
  const [leftActiveKey, setLeftActiveKey] = useState(DataSourceTypes.GENERAL);
  const orgId = useGlobalState()?.user?.orgIds?.[0] || '';

  const [postList, setPostList] = useState([]);
  const [curSelectPost, setCurSelectPost] = useState({});

  // 获取最早创建的岗位
  const getEarlyPost = (postLists) => {
    let earlyPost;
    postLists.forEach((item) => {
      if (!earlyPost) {
        earlyPost = item;
      } else {
        if (new Date(item.createTime || '').getTime() - new Date(earlyPost.createTime || '').getTime() < 0) {
          earlyPost = item;
        }
      }
    });

    return earlyPost;
  };

  const getPostLists = async () => {
    const data = await orgAPI.getOrgPostTree();
    let postLists = [];
    const handlePosts = (data) => {
      data.forEach((item) => {
        if (item.children?.length > 0) {
          handlePosts(item.children);
        }
        if (item?.posts?.length > 0) {
          postLists = [...postLists, ...item.posts];
        }
      });
    };
    handlePosts(data);
    postLists = postLists
      .filter((item) =>
        [
          PostPropertyEnums.ADMIN1,
          PostPropertyEnums.ADMIN2,
          PostPropertyEnums.TOP,
          // PostPropertyEnums.MANAGE,
          // PostPropertyEnums.CHIEF_SUPERVISOR,
          // PostPropertyEnums.SUPERVISOR_MEMBER,
          // PostPropertyEnums.BOARD_CHAIRMAN,
          // PostPropertyEnums.BOARD_MEMBER,
        ].includes(item.property),
      )
      .map((item) => ({
        ...item,
        label: `${item?.orgShortName}-${item?.postName}`,
        value: item.postId,
      }));
    setPostList(postLists);
    // const maxPermissionPost = findMaxPermissionPost(postLists);
    // 寻找人事岗位或最早创建岗位，产品定
    // const personPost = postLists.find((item) => item.personalType === 'PERSON') || getEarlyPost(postLists);
    setCurSelectPost(postLists[0]);
  };

  useEffect(() => {
    getPostLists();
  }, []);

  const getClassifyTabs = async (type?: DataSourceTypes) => {
    const data = await getOrgCategoryLists({
      type,
    });
    if (data.length > 0) {
      setClassifyTabs(data.map((item) => ({ ...item, key: String(item.categoryId), label: item.categoryName })));
      if (!currentClassify) {
        setCurrentClassify(String(data[0]?.categoryId));
      }
    } else {
      setClassifyTabs([
        {
          categoryId: null,
          categoryName: '全部',
          label: '全部',
          key: null,
        },
      ]);
      setCurrentClassify(null);
    }
  };
  const getDatasource = async (categoryId?: string, type?: DataSourceTypes, orgId?: string) => {
    const res = await getOrgUnitLists({
      orgId,
      type,
      categoryId: categoryId,
    }).catch(() => {
      return false;
    });

    if (res) {
      const data = res.map((item) => ({
        ...item,
        tempUnitId: uniqueId(),
      }));

      setDatasourceList(data);
    }
  };

  useEffect(() => {
    if (leftActiveKey && postList.length > 0) {
      getClassifyTabs(leftActiveKey);
    }
  }, [leftActiveKey]);
  useEffect(() => {
    if (postList.length > 0) {
      getDatasource(currentClassify, leftActiveKey, orgId);
    }
  }, [currentClassify, orgId, postList]);
  const versionItems = (item: MetadataManageTypes.DataUnitVO): MenuProps['items'] => {
    const versions = item.versions ?? [];
    const tempVersions = (item?.versions || []).filter((version) => version.status === APPROVE_ENUM.STASH);
    let tempVersionId = tempVersions?.length ? tempVersions[tempVersions?.length - 1]?.dataUnitVersionId : null;

    let _isView = false;

    return versions.map((version, index) => {
      return {
        key: version.dataUnitVersionId,
        label: (
          <Button
            color="default"
            variant="link"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              const isLatest = item.currentVersionId === version.dataUnitVersionId; // 是否是最新版本
              const inStash = [APPROVE_ENUM.STASH].includes(version.status);
              const inApproving = [APPROVE_ENUM.ING].includes(version.status);
              let type = openType.editDatasource;
              const isApproved = [APPROVE_ENUM.AGREE].includes(version.status);
              tempVersionId = isApproved ? tempVersionId : null;

              if ((!isLatest && !inStash) || _isView || inApproving) {
                type = openType.viewDataSource;
              }

              setDropOpen({
                open: false,
                tempUnitId: null,
              });
              setOpenModal({
                open: true,
                type: type,
                data: {
                  ...currentClassify,
                  currentVersionId: version.dataUnitVersionId,
                  preVersionId: version.preVersionId,
                  tempVersionId,
                  version,
                  unitData: item,
                },
              });
            }}
          >
            {version.version}
            <div
              className={styles.tag}
              style={{
                color: APPROVE_TYPE?.[version.status]?.color,
                borderColor: APPROVE_TYPE?.[version.status]?.color,
                backgroundColor: APPROVE_TYPE?.[version.status]?.backgroundColor,
              }}
            >
              {APPROVE_TYPE?.[version.status]?.label}
            </div>
          </Button>
        ),
      };
    });
  };
  const renderDataSourceItem = (item: MetadataManageTypes.DataUnitVO) => {
    const isApproved = item.status === APPROVE_ENUM.ING;
    return (
      <div
        key={item.id}
        className={styles.datasourceItem}
        onClick={() => {
          let checkType = openType.viewDataSource;
          setOpenModal({
            open: true,
            type: checkType,
            data: {
              currentVersionId: item.currentVersionId,
              addModalType: isApproved ? 'view' : 'add',
              unitData: item,
            },
          });
        }}
      >
        <div className={styles.datasourceItemTitleBg}></div>
        <div className={styles.datasourceTop}>
          <div className={styles.datasourceTitle}>
            <div className={styles.nameBox}>
              <div className={styles.textName}>
                <TextEllipsisTooltip text={item.name} />
              </div>
            </div>
            <div className={styles.createdTime}>
              <TextEllipsisTooltip
                text={
                  metadataUtils.isCommonMeta(item) ? (
                    '系统预设'
                  ) : (
                    <>
                      <div> {item.updateUser ? '更新信息' : `创建信息`}: &nbsp;&nbsp;</div>
                      {item.updateUser ? item.updateUser : item.createUser}
                      &nbsp;&nbsp;
                      {formatDateTime(item.updateUser ? item.bizUpdateTime : item.bizCreateTime)}
                    </>
                  )
                }
              />
              <div>
                <span style={{ marginRight: '10px', fontWeight: '600', color: '#1D2129', fontSize: '14px' }}>{item.tagNum ?? 0}</span>
                <span>标签</span>
                {item?.noMetaDataCodeConfigFlag && (
                  <Tooltip title="含有编码标签未配置编码组">
                    <ExclamationCircleOutlined style={{ color: '#ffa500', marginLeft: '10px' }} />
                  </Tooltip>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className={styles.descDiv}>
          <div className={styles.tagsDiv}>
            <div className={styles.left}>
              {metadataUtils.isCommonMeta(item) ? null : (
                <Dropdown
                  key="versionsDropdown"
                  open={dropOpen?.open && dropOpen.tempUnitId === item.tempUnitId}
                  trigger={['click']}
                  onOpenChange={() => {
                    console.log('1111');
                    setDropOpen({
                      open: false,
                      tempUnitId: null,
                    });
                  }}
                  menu={{ items: versionItems(item) }}
                >
                  <span
                    onClick={(e) => {
                      e.stopPropagation();
                      setDropOpen({
                        tempUnitId: item?.tempUnitId,
                        open: true,
                      });
                    }}
                  >
                    <Space>
                      {item?.versions?.length > 0 && <CaretDownOutlined />}
                      {item.currentVersion}
                      {item.versionsLoading && <LoadingOutlined />}
                      {[APPROVE_ENUM.ING, APPROVE_ENUM.STASH].includes(item?.status as APPROVE_ENUM) && (
                        <InfoCircleOutlined style={{ color: '#FF7D00' }} />
                      )}
                    </Space>
                  </span>
                </Dropdown>
              )}
            </div>
            <div className={styles.tagBox}>
              {item?.versions?.length === 0 || item.versions === null ? (
                <div
                  className={styles.tag}
                  style={{
                    color: APPROVE_TYPE?.[item.status]?.color,
                    borderColor: APPROVE_TYPE?.[item.status]?.color,
                    backgroundColor: APPROVE_TYPE?.[item.status]?.backgroundColor,
                  }}
                >
                  {APPROVE_TYPE?.[item.status]?.label}
                </div>
              ) : (
                <div
                  className={styles.tag}
                  style={{
                    color: OPEN_TYPE?.[(item?.enable || 'false')?.toString()]?.color,
                    borderColor: OPEN_TYPE?.[(item?.enable || 'false')?.toString()]?.color,
                    backgroundColor: OPEN_TYPE?.[(item?.enable || 'false')?.toString()]?.backgroundColor,
                  }}
                >
                  {OPEN_TYPE?.[(item?.enable || 'false').toString()]?.label}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderDataSourceList = () => {
    return (datasourceList || [])
      .filter((item) => (item.name || '').includes(searchName))
      .map((item) => {
        return item ? renderDataSourceItem(item) : null;
      });
  };
  return (
    <div className={styles.container}>
      <div className={styles.leftTabs}>
        <div className={styles.header}>
          <LeftOutlined
            onClick={() => {
              bridge.close();
            }}
          />
          <div className={styles.title}>数据单元管理</div>
        </div>
        <div className={styles.postInfo}>
          <Select value={curSelectPost} options={postList} onChange={(value) => setCurSelectPost(value)} style={{ width: '100%' }} />
        </div>
        <div className={styles.tabsDetail}>
          {leftTabDatas.map((item) => (
            <div
              key={item.key}
              onClick={() => setLeftActiveKey(item.key)}
              className={classNames(styles.leftTabItem, {
                [styles.active]: leftActiveKey === item.key,
              })}
            >
              {item.label}
            </div>
          ))}
        </div>
      </div>
      <div className={styles.rightContent}>
        {postList?.length === 0 ? (
          <Empty
            description="暂无权限"
            style={{
              display: 'flex',
              height: '100%',
              alignContent: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
            }}
          />
        ) : (
          <>
            <div className={styles.topWrap}>
              <div className={styles.classifyTabs}>
                <Tabs activeKey={currentClassify} onChange={(key) => setCurrentClassify(key)} items={classifyTabs} size="small" />
              </div>
              <div className={styles.searchBar}>
                <Input.Search placeholder="请输入关键词" onSearch={(e) => setSearchName(e)} />
              </div>
            </div>
            <div className={styles.contentDetail}>{renderDataSourceList()}</div>
          </>
        )}
      </div>
      {openModal.open && [openType.editDatasource, openType.viewDataSource].includes(openModal.type) && (
        <MetaDataEdit
          operateType={openModal.type}
          onCancel={() => setOpenModal((prev) => ({ ...prev, open: false, type: openType.close }))}
          onOk={() => setOpenModal((prev) => ({ ...prev, open: false, type: openType.close }))}
          refresh={() => getDatasource(currentClassify, leftActiveKey, orgId)}
          {...openModal.data}
          dataSourceType={leftActiveKey}
          formPage="system-datameta-manage"
        />
      )}
    </div>
  );
};

export default SystemDataMetaList;
