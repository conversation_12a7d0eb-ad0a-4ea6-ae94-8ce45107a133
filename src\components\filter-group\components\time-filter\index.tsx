import { DatePicker, DatePickerProps } from '@gwy/components-web';
import { RangePickerProps } from 'antd/lib/date-picker';
import dayjs, { Dayjs } from 'dayjs';
import { memo, useCallback, useMemo } from 'react';

const { RangePicker } = DatePicker;

function CustomRangePicker({ value, onChange, ...rest }: RangePickerProps & { value?: { min; max }; onChange?: (val: { min; max }) => void }) {
  const handleChange = useCallback((vals, valStrs) => {
    const [min, max] = valStrs || [];
    if (min && max) {
      onChange({ min, max });
    } else {
      onChange(undefined);
    }
  }, []);

  const formatValue = useMemo(() => {
    if (!value) return undefined;
    const { min, max } = value || {};
    return [dayjs(min), dayjs(max)] as [Dayjs, Dayjs];
  }, [value]);

  return (
    <RangePicker
      value={formatValue}
      format="YYYY-MM-DD HH:mm:ss"
      onChange={handleChange}
      placeholder={['开始日期', '结束日期']}
      showTime
      {...rest}
      style={{
        width: '100%',
      }}
    />
  );
}

function CustomDatePicker({ value, onChange, ...rest }: DatePickerProps & { value?: string; onChange?: (val: string) => void }) {
  const formatValue = useMemo(() => {
    return value ? dayjs(value) : undefined;
  }, [value]);

  return (
    <DatePicker
      value={formatValue}
      onChange={(v, dateStr) => onChange && onChange(dateStr as string)}
      placeholder="请选择日期"
      format="YYYY-MM-DD HH:mm:ss"
      showTime
      {...rest}
      style={{
        width: '100%',
      }}
    />
  );
}

export { CustomDatePicker };

export default memo(CustomRangePicker);
