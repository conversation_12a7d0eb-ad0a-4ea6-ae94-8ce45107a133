import { addDict, getDictById, updateDict } from '@/services/datasource';
import { Form, Input, message, Modal } from '@gwy/components-web';
import { isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import TreeByList, { OPERATE_TYPE } from '../../tree-by-list';
import styles from './index.less';

type Props = {
  disabled?: boolean;
  modalConfig?: any;
  tagOprateType?: number;
  onCancel?: () => void;
  onOk?: () => void;
};

const keysConfig = {
  idKey: 'itemCode',
  valueKey: 'itemLabel',
  childrenKey: 'subItemList',
};

const OperateModal = ({ disabled, modalConfig = {}, tagOprateType, onCancel, onOk }: Props) => {
  const [enumLibraryInfo, setEnumLibraryInfo] = useState<{ name?: string; itemList?: any[] }>({});
  const [form] = Form.useForm();
  const { validateFields, setFieldsValue } = form;

  useEffect(() => {
    const fetchEnumLibraryById = async () => {
      const data = await getDictById(modalConfig?.dictId);
      setEnumLibraryInfo(data);
      setFieldsValue({
        name: data.name,
        itemList: data?.itemList || [],
      });
    };
    if (!modalConfig?.dictId) return;
    fetchEnumLibraryById();
  }, []);

  const handleCancel = () => {
    onCancel && onCancel();
  };

  const filterExisedNodes = (tree) => {
    let result = [];
    tree?.forEach((node) => {
      if (!node.isNewData) {
        result.push(node);
      }
      if (node[keysConfig.childrenKey] && node[keysConfig.childrenKey].length > 0) {
        node[keysConfig.childrenKey] = filterExisedNodes(node[keysConfig.childrenKey]);
      }
    });
    return result;
  };

  const findTreeNodeNameEq = (arr) => {
    let tag = false;
    let hash = {};
    arr.forEach((item) => {
      if (hash[item[keysConfig.valueKey]]) tag = true;
      hash[item[keysConfig.valueKey]] = 1;
      if (item[keysConfig.childrenKey]) {
        if (findTreeNodeNameEq(item[keysConfig.childrenKey])) {
          tag = true;
        }
      }
    });
    return tag;
  };

  const findTreeNodeNameEmpty = (arr) => {
    let itemNameList = [];
    arr.forEach((item) => {
      itemNameList.push(item[keysConfig.valueKey]);
      if (item[keysConfig.childrenKey]) {
        findTreeNodeNameEmpty(item[keysConfig.childrenKey]);
      }
    });

    return isEmpty(itemNameList.filter((name) => (name || '').trim() === ''));
  };

  const loopItemList = (list) => {
    for (const item of list) {
      item.itemCode = item.isNewData ? '' : item.itemCode;
      if (item[keysConfig.childrenKey]) {
        loopItemList(item[keysConfig.childrenKey]);
      }
    }
    return list;
  };

  const handleOk = async () => {
    try {
      const values = await validateFields();
      if (findTreeNodeNameEq(values.itemList)) {
        message.warning('同一层级选项名称不可重复');
        return;
      }
      if (!findTreeNodeNameEmpty(values.itemList)) {
        message.warning('选项名称不能为空');
        return;
      }
      let params = {
        ...values,
        itemList: loopItemList(values.itemList),
        optType: tagOprateType,
      };
      try {
        if (tagOprateType === OPERATE_TYPE.EDIT) {
          params['dictId'] = modalConfig.dictId;
          await updateDict(modalConfig.dictId, params);
        } else await addDict(params);

        message.success('操作成功！');
        onOk && onOk();
      } catch (err) {
        Modal.error({
          title: '错误提示',
          content: err.desc,
        });
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <Modal
      {...(disabled ? { footer: null } : {})}
      centered
      wrapClassName={styles.modal}
      width={560}
      open
      onCancel={handleCancel}
      onOk={handleOk}
      title={enumLibraryInfo.name ? enumLibraryInfo.name : '新增枚举库'}
    >
      <div className={styles.container}>
        <div className={styles.body}>
          <Form labelCol={{ style: { width: 95, textAlign: 'left' } }} form={form}>
            <Form.Item label="枚举库名称" name="name" initialValue={enumLibraryInfo.name} rules={[{ required: true, message: '请输入' }]}>
              <Input maxLength={20} placeholder="请输入" disabled={disabled} />
            </Form.Item>
            <Form.Item
              rules={[{ required: true, message: '请输入' }]}
              label="枚举值"
              name={'itemList'}
              initialValue={filterExisedNodes(enumLibraryInfo.itemList)}
            >
              <TreeByList tagOprateType={tagOprateType} style={{ marginLeft: -24 }} canAdd={!disabled} disabled={disabled} keysConfig={keysConfig} />
            </Form.Item>
          </Form>
        </div>
      </div>
    </Modal>
  );
};

export default OperateModal;
