import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Form } from '@gwy/components-web';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';
import ObjectMeta from './index';

export type ObjectMetaFormProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  label?: React.ReactNode;
  prefix?: any[];
  isPreview?: boolean;
  options?: any;
};

export const validator = (rule, value) => {
  if (!value) {
    return Promise.reject('请填写必填项');
  }
  return Promise.resolve();
};

const ObjectMetaForm = memo(({ useIn, tag, field, label, prefix, isPreview, options }: ObjectMetaFormProps) => {
  const { readonly, required } = field || {};
  const rules = useMemo(
    () =>
      !readonly && [UseIn.App].includes(useIn)
        ? [
            required && {
              required: true,
              message: '请填写必填项',
            },
          ].filter(Boolean)
        : undefined,
    [required, readonly],
  );

  return (
    <Form.Item label={label} name={[...(prefix || []), getMetaTagUniqueId(field), 'value']} rules={rules} required={required && !readonly}>
      <ObjectMeta useIn={useIn} tag={tag} field={field} isPreview={isPreview} options={options} />
    </Form.Item>
  );
});

export default ObjectMetaForm;
