import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Select } from '@gwy/components-web';
import { useMemo } from 'react';
import { UseIn } from '../const';

interface Iprops {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean; // 仅预览模式
  options?: any;
}
const TextEnumMeta = (props: Iprops) => {
  const { options, value, onChange } = props;
  console.log(value, options, '枚举------');
  const ops = useMemo(() => {
    if (!Array.isArray(options)) return [];
    return (options || [])?.map((opt) => ({
      lebel: opt?.value,
      value: opt?.value,
      data: opt.data,
    }));
  }, [options]);
  const initValue = ops?.find((opt) => JSON.stringify(opt.data) === JSON.stringify(value))?.value;
  return (
    <Select
      options={ops}
      placeholder="请选择"
      value={initValue}
      onChange={(e) => {
        const opt = ops?.find((item) => item.value === e);
        onChange?.(opt.data);
      }}
    />
  );
};

export default TextEnumMeta;
