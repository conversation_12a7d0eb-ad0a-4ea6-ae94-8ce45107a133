import { Col, InputNumber, Row } from '@gwy/components-web';
import { useMemo } from 'react';

type Props = {
  value?: { min; max };
  onChange?: (val: { min; max }) => void;
  disabled?: boolean;
};

export default function NumberRange({ value, onChange, disabled = false }: Props) {
  const [min, max] = useMemo(() => {
    const { min, max } = value || {};
    return [min ? Number(min) : undefined, max ? Number(max) : undefined];
  }, [value]);

  const handleChange = (val, type: 'max' | 'min') => {
    onChange && onChange(Object.assign({ min, max }, { [type]: val }));
  };

  return (
    <Row gutter={8}>
      <Col span={11}>
        <InputNumber style={{ width: '100%' }} value={min} max={max} onChange={(val) => handleChange(val, 'min')} disabled={disabled} />
      </Col>
      <Col span={2} style={{ textAlign: 'center' }}>
        -
      </Col>
      <Col span={11}>
        <InputNumber style={{ width: '100%' }} value={max} min={min} onChange={(val) => handleChange(val, 'max')} disabled={disabled} />
      </Col>
    </Row>
  );
}
