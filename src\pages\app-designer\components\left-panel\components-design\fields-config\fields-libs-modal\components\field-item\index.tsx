import { CalculatorOutlined, CloseCircleFilled } from '@ant-design/icons';
import { Checkbox, message, Modal, Tooltip } from 'antd';
import { memo, useState } from 'react';
// import PreviewLibFields from '../../../preview-lib-fields';
import styles from './index.less';

interface IProps {
  field: Record<string, any>;
  canDelete?: boolean;
  refresh?: () => void;
}

const FieldItem = memo<IProps>(({ field, canDelete, refresh }) => {
  // 是否展示字段详情弹窗
  const [isPreview, setIsPreivew] = useState(false);

  const handleClick = () => {
    setIsPreivew(true);
  };

  const handleDel = () => {
    Modal.confirm({
      centered: true,
      title: '是否确定删除该字段',
      onOk: async () => {
        try {
          // await dataSourceV3API.fieldsLibDelFields({
          //   calcId: field.id,
          // });
          message.success('删除成功');
          refresh && refresh();
        } catch (e) {}
      },
    });
  };

  return (
    <>
      <Tooltip title={field.calcName}>
        <div className={styles.container}>
          <Checkbox style={{ marginRight: 16 }} value={field.fieldId} />
          <div className={styles.detail} onClick={handleClick}>
            <CalculatorOutlined style={{ marginRight: 10 }} />
            <div className={styles.fieldName}>{field.fieldName}</div>
            {/* <div className={styles.icon}>计算</div> */}
          </div>
          {canDelete && <CloseCircleFilled className={styles.closeIcon} onClick={handleDel} />}
        </div>
      </Tooltip>

      {/* {isPreview && (
        <PreviewLibFields
          onCancel={() => setIsPreivew(false)}
          onOk={() => setIsPreivew(false)}
          fieldItem={field}
          dataRuleList={[]}
        />
      )} */}
    </>
  );
});

export default FieldItem;
