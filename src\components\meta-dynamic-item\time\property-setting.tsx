import TimeConfig from '@/components/metadata-dialog/metadata-config/time-config';
import { CodeDateType, codeDateTypeOptions } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Form, Select } from '@gwy/components-web';
import dayjs from 'dayjs';
import { useEffect, useMemo } from 'react';
import { getDisabledTime } from './index';

type Props = {
  field: FormField;
  tag: DataUnitTagVO;
};

const TimePropertySetting = ({ field, tag }: Props) => {
  const form = Form.useFormInstance();

  const { config } = field || {};
  const { dateForm } = config || {};

  const { end, start } = tag.tagMetaDataConfig.metaDataDateTimeDTO;
  const minDate = useMemo(() => (start ? dayjs(start) : undefined), [start]);
  const maxDate = useMemo(() => (end ? dayjs(end) : undefined), [end]);

  const disabledTime = useMemo(() => getDisabledTime(minDate, maxDate, dateForm), [minDate, maxDate, dateForm]);

  useEffect(() => {
    form.setFieldsValue(config);
  }, []);

  return (
    <>
      <Form.Item label="时间精度" name={['dateForm']}>
        <Select options={codeDateTypeOptions.filter((item) => item.value !== CodeDateType.unlimited)} />
      </Form.Item>

      <TimeConfig form={form} minDate={minDate} maxDate={maxDate} disabledTime={disabledTime} />
    </>
  );
};

export default TimePropertySetting;
