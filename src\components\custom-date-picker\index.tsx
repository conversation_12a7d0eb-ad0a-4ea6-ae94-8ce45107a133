import { DatePicker, DatePickerProps } from '@gwy/components-web';
import dayjs from 'dayjs';

type Props = {
  value?: string;
  onChange?: (date: string) => void;
} & DatePickerProps;

const CustomDatePicker = ({ value, onChange, ...rest }: Props) => {
  return (
    <DatePicker
      value={value ? dayjs(value) : undefined}
      onChange={(date) => {
        onChange?.(date?.format('YYYY-MM-DD HH:mm:ss'));
      }}
      {...rest}
    />
  );
};

export default CustomDatePicker;
