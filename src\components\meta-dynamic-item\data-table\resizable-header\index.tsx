import { memo, useState } from 'react';
import { Resizable } from 'react-resizable';
import styles from './index.less';

const ResizableTitle = (props) => {
  const { onResize, width, onClick, ...restProps } = props;
  const [resizing, setResizing] = useState(false);
  if (!width) {
    return <th {...restProps} />;
  }
  return (
    <Resizable
      width={width}
      height={40}
      className={styles.resizableHeader}
      onResizeStart={() => setResizing(true)}
      onResizeStop={() => {
        setTimeout(() => {
          setResizing(false);
        }, 0);
      }}
      onResize={onResize}
    >
      <th
        {...restProps}
        onClick={(...args) => {
          if (!resizing && onClick) {
            onClick(...args);
          }
        }}
      />
    </Resizable>
  );
};

export default memo(ResizableTitle);
