import { Modal } from '@gwy/components-web';
import DataListWrapper from '../data-list-wrapper';

type Props = {
  title?: string;
  dataUnitId?: string;
  lightDataUnitId?: string;
  dataUnitVersionId?: number;
  onClose?: () => void;
};

const DataListModal = ({ title, dataUnitId, lightDataUnitId, dataUnitVersionId, onClose }: Props) => {
  return (
    <Modal title={title} open footer={null} onCancel={onClose} size="large">
      <div>
        <DataListWrapper dataUnitId={dataUnitId} lightDataUnitId={lightDataUnitId} dataUnitVersionId={dataUnitVersionId} />
      </div>
    </Modal>
  );
};

export default DataListModal;
