import { APPROVE_ENUM } from '@/const/datasource';
import { datasourceLightAPI, systemDatametaAPI } from '@/services';
import {
  approveDataUnitAgree,
  approveDataUnitRefuse,
  cancelStashDataUnit,
  editDataUnit,
  getDataUnitApproveDetail,
  getDataUnitVersionInfo,
  submitDatasourceApprove,
} from '@/services/datasource';
import { deepEqual } from '@/utils';
import { ExclamationCircleOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Button, Input, message, Modal, ModalFuncProps, Popover, TextEllipsisTooltip } from '@gwy/components-web';
import { cloneDeep, isEmpty } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import MetaDataContent from './components/metadata-content';
import styles from './index.less';
type Iprops = {
  onCancel: () => void;
  onOk?: () => void;
  currentVersionId?: string; // 当前版本id
  // editData?: any;
  operateType: 'add' | 'edit' | 'approve' | 'view';
  refresh?: () => void;
  isCommonMeta?: boolean;
  dataSourceType: 'NORMAL' | 'GENERAL';
  approveId?: string;
  editPermission?: boolean;
  versions?: any[];
  unitData?: any;
  tempVersionId?: string; // 暂存版本Id
  formPage?: string;
  incompleteTag?: any; // 制表过来的标签，配置不完整，需要跳转
  onCompleteTag?: () => void; // 配置完成后的回调
};
const { TextArea } = Input;
const MetaDataEdit = (props: Iprops) => {
  const {
    onCancel,
    onOk,
    operateType,
    refresh,
    isCommonMeta,
    dataSourceType,
    currentVersionId,
    approveId,
    editPermission,
    versions,
    unitData,
    tempVersionId,
    formPage,
    incompleteTag,
    onCompleteTag,
  } = props;

  // const masterProps = useModel('@@qiankunStateFromMaster');
  // console.log('masterProps', masterProps);
  // const { route, useBridge } = masterProps?.globalState || {};

  // 是否轻量数据单元
  const isLight = unitData?.light;
  const isOrgMeta = formPage === 'system-datameta-manage';
  // 内部维护的一个操作类型，枚举值与operateType保持一致,用于重新发起直接修改状态
  const [insideOptType, setInsideOptType] = useState<'add' | 'edit' | 'approve' | 'view'>(operateType);
  const [isResubmitting, setIsResubmitting] = useState(false);
  const mainDataRef = useRef(null);
  const [curVersonInfo, setCurVersonInfo] = useState(null);
  // 副本用于对比有没有修改
  const [curVersionPlan, setCurVersionPlan] = useState(null);
  // 副本标签信息
  const [planVersionTags, setPlanVersionTags] = useState(null);
  const [compareInfo, setCompareInfo] = useState(null);
  const [approveInfo, setApproveInfo] = useState(null);
  const [actionConfirm, setActionConfirm] = useState({
    open: false,
    suggestion: '',
    actionType: null,
  });

  // 内部维护的versionId,当版本变更后需要重新请求数据
  const [versionId, setVersionId] = useState(currentVersionId);

  const geneModal = ({ title, content, onOk, onCancel }: ModalFuncProps) => {
    return Modal.confirm({
      centered: true,
      title,
      content,
      onCancel,
      onOk,
    });
  };

  const getDetailInfo = async (versionId) => {
    if (insideOptType === 'approve') {
      const data = await getDataUnitApproveDetail(approveId);
      setCurVersonInfo(data?.currentVersionInfo);
      setCompareInfo(data?.preVersionInfo);
      setApproveInfo(data);
    } else {
      const data = await getDataUnitVersionInfo(versionId);
      setCurVersonInfo(data);
      if (data?.dataUnitVersionId === currentVersionId) {
        setCurVersionPlan(data);
      }
    }
  };

  // 轻量数据单元详情
  const getDetailInfoLight = async () => {
    const data = await datasourceLightAPI.getDataUnitInfo(unitData.lightDataUnitId);
    setCurVersonInfo(data);
  };

  // 公务云系统设置入口的详情接口
  const getOrgMetaDetail = async (id) => {
    const data = await systemDatametaAPI.getOrgUnitHistoryDetail(id);
    setCurVersonInfo(data);
  };

  // 默认是查看模式，可手动开启
  useEffect(() => {
    if (operateType === 'edit') {
      setInsideOptType('view');
    }
  }, []);

  useEffect(() => {
    if (operateType === 'approve' && curVersonInfo?.status === APPROVE_ENUM.AGREE) {
      setInsideOptType('view');
    }
  }, [operateType, curVersonInfo]);

  useEffect(() => {
    if (isLight) {
      getDetailInfoLight();
    } else if (isOrgMeta) {
      getOrgMetaDetail(currentVersionId);
    } else {
      getDetailInfo(versionId);
    }
  }, [operateType]);

  const handleAgree = () => {
    approveDataUnitAgree(approveId).then(() => {
      message.success('操作成功');
      refresh && refresh();
      onOk && onOk();
    });
  };

  const handleRefuse = (msg) => {
    approveDataUnitRefuse(approveId, {
      message: msg,
    }).then(() => {
      message.success('操作成功');
      refresh && refresh();
      onOk && onOk();
    });
  };

  const handleData = async () => {
    const allData = await mainDataRef?.current.getValues();

    const buildData = {
      ...allData?.basicValues,
      tags: (allData?.tagsValues?.configTags || []).filter((t) => !t.parentFlag),
      taskId: isResubmitting ? approveInfo?.taskId : null,
    };
    return buildData;
  };

  const sameTagNameCheck = (arr) => {
    const nameMap = new Map();
    let count = 0;
    for (let i = 0; i < arr.length; i++) {
      const itemName = arr[i].name;

      if (nameMap.has(itemName)) {
        count++;
      } else {
        nameMap.set(itemName, true);
      }
    }
    return count;
  };

  const stringfyStr = (data) => {
    const { name, description, categoryId, enable, allowCustomLabel, tags } = data;
    const stringfyData = {
      name,
      description,
      categoryId,
      enable,
      // allowCustomLabel,
      // tags: needHandleTags,
    };
    return JSON.stringify(stringfyData);
  };

  const isEqualTags = (tags = [], compareTags = []) => {
    if (tags?.length !== compareTags?.length) return false;
    let result = true;
    (tags || []).forEach((tag, index) => {
      const tagObj = {
        name: tag.name,
        enable: tag.enable,
        identifierFlag: tag.identifierFlag,
      };
      const compareTagObj = {
        name: compareTags[index].name,
        enable: compareTags[index].enable,
        identifierFlag: compareTags[index].identifierFlag,
      };
      if (JSON.stringify(tagObj) !== JSON.stringify(compareTagObj) || !deepEqual(tag.tagMetaDataConfig, compareTags[index].tagMetaDataConfig)) {
        result = false;
      }
    });
    return result;
  };
  const ifVersionInfoUnChange = (changeInfo, planInfo) => {
    console.log(changeInfo, '===changeInfo', planInfo);
    return stringfyStr(changeInfo) === stringfyStr(planInfo) && isEqualTags(changeInfo.tags, planInfo.tags);
  };

  const checkValuesFn = (allData) => {
    let title = '';
    let { tags } = allData;
    tags = (tags || []).filter((tag) => !tag?.deletedFlag);
    let emptyIndexs = [];
    (tags || []).forEach((tag, index) => {
      if (!tag?.name || isEmpty(tag?.tagMetaDataConfig)) {
        emptyIndexs.push(`第${index + 1}项`);
      }
    });
    if (emptyIndexs.length > 0) {
      return (title = `${emptyIndexs.join('、')}标签属性不完整，请填写完整后再提交`);
    }
    const sameNameCount = sameTagNameCheck(tags || []);
    if (sameNameCount > 0) {
      return `当前数据单元有${sameNameCount}个标签存在名称重复，请重新修改后再提交！`;
    }
    return title;
  };

  // 版本变更，执行人手动触发提交正式版本
  const tempSave = async (needCheck = true, versionId?) => {
    try {
      const buildData = await handleData();
      const title = checkValuesFn(buildData);
      if (title && needCheck) {
        return message.error(title);
      }
      if (
        ifVersionInfoUnChange(buildData, {
          ...(curVersionPlan || {}),
          tags: planVersionTags,
        }) &&
        needCheck
      ) {
        return geneModal({ title: '当前页面暂无修改内容，确定退出？', onOk: onCancel });
      }
      const approveId = await editDataUnit(versionId || curVersonInfo.dataUnitVersionId, buildData);
      // 基于最新暂存版本进行操作
      if (approveId && !needCheck && versionId) {
        setVersionId(approveId);
        getDetailInfo(approveId);
      }
      if (approveId && needCheck) {
        message.success('保存成功');
        refresh && refresh();
        onOk && onOk();
      }
    } catch (error) {
      if (typeof error === 'string') {
        return message.error(error);
      }
    }
  };

  const cancleTempSave = (currentVersionId?) => {
    const id = currentVersionId || curVersonInfo.dataUnitVersionId;
    cancelStashDataUnit(id).then(() => {
      // 版本变更，执行人手动触发确定自动取消props传递的版本
      if (!currentVersionId) {
        message.success('取消成功');
        refresh && refresh();
        onOk && onOk();
      }
    });
  };

  const handleSubmit = async () => {
    try {
      const buildData = await handleData();
      const title = checkValuesFn(buildData);
      if (title) {
        return message.error(title);
      }
      if (
        tempVersionId !== curVersonInfo.dataUnitVersionId &&
        ifVersionInfoUnChange(buildData, {
          ...(curVersionPlan || {}),
          tags: planVersionTags,
        })
      ) {
        return geneModal({ title: '当前页面暂无修改内容，确定退出？', onOk: onCancel });
      }
      geneModal({
        title: '确认提交该数据单元的编辑吗',
        onOk: async () => {
          try {
            const approveId = await editDataUnit(curVersonInfo.dataUnitVersionId, buildData);
            if (approveId) {
              try {
                await submitDatasourceApprove(approveId);
                message.success('操作成功');
              } catch (e) {}
              refresh && refresh();
              onOk && onOk();
            }
          } catch (error) {}
        },
      });
    } catch (error) {
      if (typeof error === 'string') {
        return message.error(error);
      }
    }
  };

  const handleSubmitLight = async () => {
    const allData = await mainDataRef?.current.getValues();
    const buildData = {
      ...allData?.basicValues,
      relateTagIds: allData?.tagsLightValues?.relateTagIds,
    };
    const title = checkValuesFn(buildData);
    if (title) {
      return message.error(title);
    }
    geneModal({
      title: '确认提交该数据单元的编辑吗',
      onOk: async () => {
        try {
          await datasourceLightAPI.updateDataUnit(unitData.lightDataUnitId, buildData);
          message.success('操作成功');
          setTimeout(() => {
            refresh && refresh();
            onOk && onOk();
          }, 500);
        } catch (error) {
          message.error(error.desc || '操作失败');
        }
      },
    });
  };

  /**
   * 暂存版本过时提示
   * 例：暂存版本为v2.0，但此时最新版本为v3.0，则作此提示
   * 单击确定会取消当前暂存版本，基于最新版自动重新生成一次暂存版本
   */
  const tempIngVersionChange = () => {
    geneModal({
      title: '温馨提示',
      content: '当前数据单元版本发生变化，需要更新数据后，才可继续进行操作！',
      onOk: () => {
        setVersionId(curVersonInfo?.currentVersionId);
        getDetailInfo(curVersonInfo?.currentVersionId).then(() => {
          const cancleVersionId = tempVersionId ? tempVersionId : currentVersionId;
          // 取消过时的暂存版本
          cancleTempSave(cancleVersionId);
          // 基于最新版重新暂存一次
          tempSave(false, curVersonInfo?.currentVersionId);
        });
      },
      onCancel,
    });
  };

  useEffect(() => {
    if (operateType === 'edit' && curVersonInfo?.status === APPROVE_ENUM.STASH && curVersonInfo?.currentVersionId !== curVersonInfo?.preVersionId) {
      tempIngVersionChange();
    }
  }, [curVersonInfo]);

  const openEdit = async () => {
    // 如果是从正式版本进来时有暂存版本，应该用暂存版本的数据
    // 暂存版本保存暂存版本数据，有正式版本保存正式版本数据，有tempVersionId说明有暂存版本
    const buildData = await handleData();
    if (curVersonInfo?.dataUnitVersionId === currentVersionId) {
      setPlanVersionTags(cloneDeep(buildData.tags));
    }
    if (tempVersionId) {
      await getDetailInfo(tempVersionId);
    }

    setInsideOptType('edit');
  };

  const popoverContent = (currentActionType, btnActionInfo) => {
    return (
      <>
        <div className={styles.popConTitle}>
          <ExclamationCircleOutlined style={{ marginRight: 8, color: '#FF7D00' }} />
          确定{btnActionInfo.label}吗?
        </div>
        <div className={styles.popConBody}>
          <div className={styles.tips}>请填写{btnActionInfo.label}原因</div>
          <TextArea
            rows={4}
            maxLength={300}
            placeholder={`请输入${btnActionInfo.label}原因`}
            value={actionConfirm.suggestion}
            onChange={(event) => {
              setActionConfirm((prev) => ({
                ...prev,
                suggestion: event.target.value,
              }));
            }}
          />
        </div>
        <div className={styles.popFooter}>
          <Button
            color="default"
            variant="outlined"
            size="small"
            onClick={() => {
              setActionConfirm({
                open: false,
                suggestion: '',
                actionType: null,
              });
            }}
          >
            取消
          </Button>
          &nbsp;&nbsp;
          <Button
            color="primary"
            variant="solid"
            size="small"
            onClick={() => {
              if (!actionConfirm.suggestion) {
                return message.error('请填写拒绝原因');
              }
              handleRefuse(actionConfirm.suggestion);
            }}
          >
            确定
          </Button>
        </div>
      </>
    );
  };

  const renderFooter = () => {
    const ifCanReSubmit = insideOptType === 'approve' && approveInfo?.status === APPROVE_ENUM.REFUSE;
    console.log('ifCanReSubmit', ifCanReSubmit);
    const ifcanApprove = insideOptType === 'approve' && approveInfo?.status === APPROVE_ENUM.ING;
    const inTemping = insideOptType === 'edit' && curVersonInfo?.status === APPROVE_ENUM.STASH;
    if (isCommonMeta) return null;

    if (!editPermission) {
      return null;
    }
    if (operateType === 'view') {
      return null;
    }
    if (!ifcanApprove && insideOptType === 'view' && operateType === 'approve' && !ifCanReSubmit) {
      return null;
    }

    if (editPermission && operateType === 'edit' && insideOptType === 'view') {
      return (
        <Button type="primary" onClick={() => openEdit()}>
          编辑
        </Button>
      );
    }
    if (ifCanReSubmit) {
      // 只能重新发起 1 次
      if ((approveInfo?.redoNum || 0) > 0) {
        return null;
      }
      return (
        <div className={styles.footer}>
          <div className={styles.footerTips}>
            <TextEllipsisTooltip
              text={
                <>
                  <InfoCircleOutlined style={{ color: '#4D7BF6', marginRight: '8px' }} />
                  {`${approveInfo?.approverUserName}拒绝了您发起的数据单元审批，拒绝原因：${approveInfo?.message}`}
                </>
              }
            />
          </div>
          <Button
            type="primary"
            onClick={() => {
              setInsideOptType('edit');
              setIsResubmitting(true);
            }}
          >
            重新发起
          </Button>
        </div>
      );
    }
    if (ifcanApprove) {
      return (
        <div className={styles.footer}>
          <div>
            <InfoCircleOutlined style={{ color: '#4D7BF6', marginRight: '8px' }} />
            {`${approveInfo?.editUserName}发起的的数据单元审批`}
          </div>
          <div>
            <Popover
              overlayClassName={styles.popover}
              trigger="click"
              arrow={false}
              open={actionConfirm.open}
              onOpenChange={(visible) => {
                if (visible) {
                  setActionConfirm({
                    open: true,
                    actionType: insideOptType,
                    suggestion: '',
                  });
                } else {
                  setActionConfirm({
                    open: false,
                    actionType: null,
                    suggestion: '',
                  });
                }
              }}
              content={popoverContent(insideOptType, {
                label: '拒绝',
              })}
            >
              <Button danger style={{ marginRight: '8px' }}>
                拒绝
              </Button>
            </Popover>
            <Button type="primary" onClick={handleAgree}>
              同意
            </Button>
          </div>
        </div>
      );
    }
    return (
      <>
        <Button
          onClick={() => {
            if (inTemping) {
              Modal.confirm({
                title: '是否取消暂存？',
                onOk: () => {
                  cancleTempSave();
                  onCancel();
                },
              });
            } else {
              onCancel();
            }
          }}
          style={{ marginRight: '8px' }}
        >
          取消
        </Button>
        {!isLight && (
          <Button color="primary" variant="outlined" onClick={() => tempSave()} style={{ marginRight: '8px' }}>
            暂存
          </Button>
        )}
        <Button
          type="primary"
          onClick={() => {
            if (isLight) {
              handleSubmitLight();
            } else {
              handleSubmit();
            }
          }}
          style={{ marginRight: '8px' }}
        >
          确定
        </Button>
      </>
    );
  };
  return (
    <Modal
      title={`${curVersonInfo?.name || unitData?.name || '数据单元编辑'}`}
      open
      onCancel={onCancel}
      onOk={onOk}
      size="large"
      footer={renderFooter()}
    >
      <div className={styles.content}>
        {curVersonInfo && (
          <MetaDataContent
            ref={mainDataRef}
            operateType={insideOptType}
            dataSourceType={dataSourceType}
            editData={curVersonInfo}
            compareData={compareInfo}
            versions={versions}
            dataUnit={unitData}
            isOrgMeta={isOrgMeta}
            incompleteTag={incompleteTag}
            onCompleteTag={onCompleteTag}
          />
        )}
      </div>
    </Modal>
  );
};

export default MetaDataEdit;
