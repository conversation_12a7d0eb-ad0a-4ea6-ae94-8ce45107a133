import { dataWrapper, requestUtil } from '@gwy/libs-web';

/**
 * 添加表单子数据单元配置
 */
export const addSubDataUnitConfig = (params) => dataWrapper(requestUtil.put(`gwy-datasource/api/form_versions/addSubDataUnitConfig`, params));

/**
 * 获取表单子数据单元配置
 */
export const getSubDataUnitConfigByVersionId = (params) =>
  dataWrapper(requestUtil.get(`gwy-datasource/api/form_versions/getSubDataUnitConfigByVersionId`, { params }));

/**
 * 获取表单子数据单元的数据---聚合查询
 */

export const getSubDataUnitData = (dataId, params) =>
  dataWrapper(requestUtil.get(`gwy-datasource-data/api/form_datas/${dataId}/getRowDetail`, { params }));
