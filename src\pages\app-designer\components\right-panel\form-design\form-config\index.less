.page {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
  overflow: auto;
}

.header {
  flex-shrink: 0;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;

  .headerLeft {
    flex-grow: 1;
    overflow: auto;
  }

  .headerRight {
  }
}

.dropBox {
  // height: 100%;
  flex-grow: 1;
  overflow: auto;
  padding: 0 20px;
  display: flex;
  flex-direction: column;

  :global {
    .ant-form-item {
      margin-bottom: 0;

      .ant-form-item-label {
        > label {
          width: 100%;

          &::after {
            display: none;
          }
        }
      }
    }
  }

  .baseGroup {
    position: relative;
    flex-grow: 1;
    overflow: auto;
    border: 1px solid #c9cdd4;
    border-radius: 4px;
    padding: 8px 16px;
    min-height: 440px;
    display: flex;
    flex-direction: column;

    &.active {
      border: 2px solid #4d7bf6;
    }

    .baseGroupTitle {
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .formWrapper {
      // padding: 40px 100px;
    }

    .formItemList {
      display: grid;
      grid-template-columns: repeat(12, 1fr);
      gap: 16px;
      align-items: start;

      .formItemWrapper {
        position: relative;
        padding: 6px;
        border: 2px dashed transparent;
        border-radius: 4px;
        cursor: grab;
        transition: all 0.2s ease;

        // &:hover {
        //   border-color: rgba(77, 123, 246, 30%);
        //   background-color: rgba(77, 123, 246, 5%);
        // }

        &.active {
          border: 2px dashed #4d7bf6;
          // background-color: rgba(77, 123, 246, 10%);
        }

        // 拖拽时的样式
        &:active {
          cursor: grabbing;
        }

        .labelWrapper {
          flex-grow: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 8px;
        }

        .deleteIcon {
          position: absolute;
          right: 0;
          top: -18px;
          color: #4d7bf6;
          cursor: pointer;
        }
      }
    }

    .combineTagWrapper {
      margin: 20px 0;
    }

    .emptyWrapper {
      margin-top: -20px;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #86909c;
      font-size: 12px;
    }
  }

  .dataTableList {
    flex-shrink: 0;
    min-height: 100px;

    .dataTableWrapper {
      // border: 1px solid #c9cdd4;
      border-radius: 4px;
      margin: 20px 0;
    }
  }
}

.footer {
  flex-shrink: 0;
  height: 50px;
  box-shadow: 0 -1px 4px 0 rgba(0, 0, 0, 12%);
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background-color: #fff;

  &.active {
    border: 2px solid #4d7bf6;
  }
}
