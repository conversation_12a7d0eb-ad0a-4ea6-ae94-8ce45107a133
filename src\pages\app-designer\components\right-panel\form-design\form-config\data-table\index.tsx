import MetaDynamicItem from '@/components/meta-dynamic-item';
import { MetaTagType } from '@/const/metadata';
import { SelectedStateType } from '@/pages/app-designer/const';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { FormField, SourceType } from '@/types/form-field';
import { getCalcFieldDisplayName, getMetaTagUniqueId } from '@/utils/metadata';
import { DeleteOutlined, InfoCircleOutlined, RetweetOutlined } from '@ant-design/icons';
import type { DragEndEvent, DragOverEvent, UniqueIdentifier } from '@dnd-kit/core';
import { closestCenter, DndContext, DragOverlay, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { restrictToHorizontalAxis } from '@dnd-kit/modifiers';
import { arrayMove, horizontalListSortingStrategy, SortableContext, useSortable } from '@dnd-kit/sortable';
import { Form, Table, TableColumnsType, Tag, TextEllipsisTooltip, Tooltip } from '@gwy/components-web';
import classNames from 'classnames';
import { produce } from 'immer';
import { isEmpty } from 'lodash-es';
import React, { useContext, useMemo, useState } from 'react';
import { validateFormFields } from '../../utils';
import styles from './index.less';

export type DataTableProps = {
  onClick?: () => void;
  onDelete?: () => void;
  selected?: boolean;
  columnSelectable?: boolean;
  type?: 'combineTag' | 'table';
  tableId?: string;
  onDeleteColumn?: (field: FormField) => void;
  noBorder?: boolean;
};

// 拖拽状态接口
interface DragIndexState {
  active: UniqueIdentifier;
  over: UniqueIdentifier | undefined;
  direction?: 'left' | 'right';
}

// 拖拽状态上下文
const DragIndexContext = React.createContext<DragIndexState>({ active: -1, over: -1 });

// 拖拽激活样式
const dragActiveStyle = (dragState: DragIndexState, id: string) => {
  const { active, over } = dragState;
  let style: React.CSSProperties = {};
  if (active && active === id) {
    style = { backgroundColor: 'rgba(24, 144, 255, 0.1)', opacity: 0.8 };
  } else if (over && id === over && active !== over) {
    style = { borderLeft: '2px solid #1890ff' };
  }
  return style;
};

// 可拖拽的表头单元格
interface HeaderCellProps extends React.HTMLAttributes<HTMLTableCellElement> {
  id: string;
}

const TableHeaderCell: React.FC<HeaderCellProps> = (props) => {
  const dragState = React.useContext<DragIndexState>(DragIndexContext);
  const { attributes, listeners, setNodeRef, isDragging } = useSortable({ id: props.id });
  const style: React.CSSProperties = {
    ...props.style,
    cursor: 'move',
    ...(isDragging ? { position: 'relative', zIndex: 9999, userSelect: 'none' } : {}),
    ...dragActiveStyle(dragState, props.id),
  };
  return <th {...props} ref={setNodeRef} style={style} {...attributes} {...listeners} />;
};

// 可拖拽的表体单元格
interface BodyCellProps extends React.HTMLAttributes<HTMLTableCellElement> {
  id: string;
}

const TableBodyCell: React.FC<BodyCellProps> = (props) => {
  const dragState = React.useContext<DragIndexState>(DragIndexContext);
  return <td {...props} style={{ ...props.style, ...dragActiveStyle(dragState, props.id) }} />;
};

const DataTable = ({ onClick, onDelete, selected, columnSelectable = true, type, tableId, onDeleteColumn, noBorder }: DataTableProps) => {
  const { tagsMap, combineTagConfig, tableList, setTableList, selectedState, setSelectedState, getPropertyConfigRef, setCombineTagConfig } =
    useContext(AppDesignerContext);

  // 拖拽状态管理
  const [dragIndex, setDragIndex] = useState<DragIndexState>({ active: -1, over: -1 });

  // 拖拽传感器配置
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 1,
      },
    }),
  );

  // 是否全部是输出
  const isAllOutput = useMemo(() => {
    if (tableList?.find((t) => t.id === tableId)?.fields?.some((field) => field.sourceType === SourceType.Tag && !field.readonly)) {
      return false;
    }
    return true;
  }, [tableList, tableId]);

  // 拖拽结束处理
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      if (type === 'combineTag') {
        // 处理组合标签的字段排序
        setCombineTagConfig(
          produce(combineTagConfig, (draft) => {
            if (draft?.fields) {
              const oldIndex = draft.fields.findIndex((field) => getMetaTagUniqueId(field) === active.id);
              const newIndex = draft.fields.findIndex((field) => getMetaTagUniqueId(field) === over.id);
              if (oldIndex !== -1 && newIndex !== -1) {
                draft.fields = arrayMove(draft.fields, oldIndex, newIndex);
              }
            }
          }),
        );
      } else if (type === 'table' && tableId) {
        // 处理数据表格的字段排序
        setTableList(
          produce(tableList, (draft) => {
            const table = draft.find((t) => t.id === tableId);
            if (table?.fields) {
              const oldIndex = table.fields.findIndex((field) => getMetaTagUniqueId(field) === active.id);
              const newIndex = table.fields.findIndex((field) => getMetaTagUniqueId(field) === over.id);
              if (oldIndex !== -1 && newIndex !== -1) {
                table.fields = arrayMove(table.fields, oldIndex, newIndex);
              }
            }
          }),
        );
      }
    }

    // 重置拖拽状态
    setDragIndex({ active: -1, over: -1 });
  };

  // 拖拽悬停处理
  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    const activeIndex = fields.findIndex((field) => getMetaTagUniqueId(field) === active.id);
    const overIndex = fields.findIndex((field) => getMetaTagUniqueId(field) === over?.id);
    setDragIndex({
      active: active.id,
      over: over?.id,
      direction: overIndex > activeIndex ? 'right' : 'left',
    });
  };

  // 切换输入输出
  const toggleIO = () => {
    setTableList(
      produce(tableList, (draft) => {
        const t = draft.find((t) => t.id === tableId);
        t?.fields?.forEach((field) => {
          const tag = tagsMap[getMetaTagUniqueId(field)];
          if (field.sourceType === SourceType.Tag && tag?.type !== MetaTagType.System) {
            field.readonly = !isAllOutput;
          }
        });
      }),
    );
  };

  const fields = useMemo(() => {
    if (type === 'combineTag') {
      return combineTagConfig?.fields || [];
    } else if (type === 'table') {
      return tableList?.find((t) => t.id === tableId)?.fields || [];
    }
  }, [type, combineTagConfig, tableList, tableId]);

  // 渲染表格列配置
  const renderColumns = (): TableColumnsType<FormField> => {
    return fields?.map((field) => {
      const uniqId = getMetaTagUniqueId(field);
      const tag = tagsMap[uniqId];
      const columnSelected =
        columnSelectable &&
        ((type === 'combineTag' && selectedState.type === SelectedStateType.combineTagItem && selectedState.formItemId === uniqId) ||
          (type === 'table' &&
            selectedState.type === SelectedStateType.tableItem &&
            selectedState.formItemId === uniqId &&
            selectedState.tableId === tableId));

      return {
        key: uniqId,
        dataIndex: uniqId,
        width: 150,
        onHeaderCell: () => ({ id: uniqId }),
        onCell: () => ({ id: uniqId }),
        title: (
          <div key={uniqId} className={styles.column}>
            {/* 标签、字段 */}
            <div
              className={classNames(styles.headerCell, {
                [styles.selected]: columnSelected,
              })}
              onClick={async (e) => {
                e.stopPropagation();
                await validateFormFields(getPropertyConfigRef());

                if (type === 'combineTag') {
                  setSelectedState({
                    type: SelectedStateType.combineTagItem,
                    formItemId: uniqId,
                    formItemType: field.sourceType,
                  });
                } else if (type === 'table') {
                  setSelectedState({
                    type: SelectedStateType.tableItem,
                    formItemId: uniqId,
                    formItemType: field.sourceType,
                    tableId,
                  });
                }
              }}
            >
              {field.sourceType === SourceType.Tag && (
                <>
                  <span className={styles.name}>
                    <TextEllipsisTooltip
                      text={
                        <span>
                          {field.fieldName || tag?.name}
                          {field.fieldName && (
                            <Tooltip title={`原标签名称：${tag?.name}`}>
                              <InfoCircleOutlined style={{ marginLeft: 5, color: '#4D7BF6' }} />
                            </Tooltip>
                          )}
                        </span>
                      }
                    />
                  </span>
                  <Tag className={styles.readonly} color={field.readonly ? 'orange' : 'blue'}>
                    {field.readonly ? '输出' : '输入'}
                  </Tag>
                </>
              )}
              {field.sourceType === SourceType.Field && (
                <>
                  <span className={styles.name}>
                    {getCalcFieldDisplayName(field)}
                    {field.fieldConfig?.formFieldConfig?.fieldOtherName && (
                      <Tooltip title={`原名称：${field.fieldConfig?.fieldName}`}>
                        <InfoCircleOutlined style={{ marginLeft: 5, color: '#4D7BF6' }} />
                      </Tooltip>
                    )}
                  </span>
                  <Tag className={styles.readonly} color="orange">
                    输出
                  </Tag>
                </>
              )}
            </div>

            {/* 删除列 */}
            {columnSelected && (
              <DeleteOutlined
                style={{ position: 'absolute', right: 2, top: 2, color: '#4d7bf6' }}
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteColumn?.(field);
                }}
              />
            )}
          </div>
        ),
        render: () => {
          const renderItem = () => {
            const uniqueId = getMetaTagUniqueId(field);
            // 标签
            const renderTag = () => {
              const tag = tagsMap[getMetaTagUniqueId(field)];
              if (!tag) {
                return null;
              }
              return (
                <>
                  <MetaDynamicItem hiddenLabel tag={tag} field={field} />
                </>
              );
            };

            // 计算字段
            const renderCalcField = () => {
              return <span>回显数据</span>;
            };

            return (
              <div key={uniqueId} className={classNames(styles.formItemWrapper)}>
                {field.sourceType === SourceType.Tag && renderTag()}
                {field.sourceType === SourceType.Field && renderCalcField()}
              </div>
            );
          };

          return renderItem();
        },
      };
    });
  };

  const [form] = Form.useForm();

  return (
    <div
      className={classNames(styles.container, {
        [styles.selected]: selected,
        [styles.noBorder]: noBorder,
      })}
      onClick={(e) => {
        e.stopPropagation();
        onClick?.();
      }}
    >
      <div className={styles.title}>
        {type === 'combineTag' ? combineTagConfig?.combineTagName || `数据表格` : tableList?.find((t) => t.id === tableId)?.tableName || `数据表格`}
        {type === 'combineTag' && '（组合标签）'}
      </div>

      <div className={styles.tableWrapper}>
        {!isEmpty(fields) && (
          <DndContext
            sensors={sensors}
            modifiers={[restrictToHorizontalAxis]}
            onDragEnd={handleDragEnd}
            onDragOver={handleDragOver}
            collisionDetection={closestCenter}
          >
            <SortableContext items={fields.map((field) => getMetaTagUniqueId(field))} strategy={horizontalListSortingStrategy}>
              <DragIndexContext.Provider value={dragIndex}>
                <Form form={form}>
                  <Table
                    bordered
                    columns={renderColumns()}
                    dataSource={[{}]}
                    scroll={{ x: 'max-content' }}
                    pagination={false}
                    components={{
                      header: { cell: TableHeaderCell },
                      body: { cell: TableBodyCell },
                    }}
                    summary={
                      type === 'combineTag'
                        ? undefined
                        : () => {
                            const statisticList = tableList?.find((t) => t.id === tableId)?.statisticList;
                            if (isEmpty(statisticList)) return null;
                            return (
                              <>
                                <Table.Summary.Row>
                                  {fields.map((field, index) => {
                                    const statisticalItem = statisticList?.find(
                                      (s) =>
                                        s.location &&
                                        ((s.location.sourceType === SourceType.Tag &&
                                          s.location.dataUnitId === field.dataUnitId &&
                                          s.location.sourceId === field.sourceId) ||
                                          (s.location.sourceType === SourceType.Field && s.location.sourceId === field.fieldConfig?.fieldId)),
                                    );
                                    return (
                                      <Table.Summary.Cell key={index} index={index}>
                                        <div
                                          style={{ textAlign: 'center' }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                          }}
                                        >
                                          {statisticalItem?.statisticName}
                                        </div>
                                      </Table.Summary.Cell>
                                    );
                                  })}
                                </Table.Summary.Row>
                              </>
                            );
                          }
                    }
                  />
                </Form>
              </DragIndexContext.Provider>
            </SortableContext>
            <DragOverlay>
              <th style={{ backgroundColor: '#f0f8ff', padding: 16, border: '1px solid #1890ff' }}>
                {fields.find((field) => getMetaTagUniqueId(field) === dragIndex.active)?.fieldName ||
                  tagsMap[dragIndex.active as string]?.name ||
                  'Column'}
              </th>
            </DragOverlay>
          </DndContext>
        )}
        {isEmpty(fields) && <div style={{ backgroundColor: '#deecfc', height: 40 }} />}
      </div>

      {/* <div className={styles.table}>
        <div className={styles.tableInner}>
          <div className={styles.headerBg} />

          {fields?.map((field) => {
            const uniqId = getMetaTagUniqueId(field);
            const tag = tagsMap[uniqId];
            const columnSelected =
              columnSelectable &&
              ((type === 'combineTag' && selectedState.type === SelectedStateType.combineTagItem && selectedState.formItemId === uniqId) ||
                (type === 'table' &&
                  selectedState.type === SelectedStateType.tableItem &&
                  selectedState.formItemId === uniqId &&
                  selectedState.tableId === tableId));
            return (
              <div key={uniqId} className={styles.column}>
                <div
                  className={classNames(styles.headerCell, {
                    [styles.selected]: columnSelected,
                  })}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (type === 'combineTag') {
                      setSelectedState({
                        type: SelectedStateType.combineTagItem,
                        formItemId: uniqId,
                        formItemType: field.sourceType,
                      });
                    } else if (type === 'table') {
                      setSelectedState({
                        type: SelectedStateType.tableItem,
                        formItemId: uniqId,
                        formItemType: field.sourceType,
                        tableId,
                      });
                    }
                  }}
                >
                  {field.sourceType === SourceType.Tag && (
                    <>
                      <span className={styles.name}>
                        <TextEllipsisTooltip text={tag?.name} />
                      </span>
                      <Tag className={styles.readonly} color={field.readonly ? 'orange' : 'blue'}>
                        {field.readonly ? '输出' : '输入'}
                      </Tag>
                    </>
                  )}
                  {field.sourceType === SourceType.Field && (
                    <>
                      <span className={styles.name}> {field.fieldConfig?.fieldName}</span>
                      <Tag className={styles.readonly} color="orange">
                        输出
                      </Tag>
                    </>
                  )}
                </div>

                <div className={styles.statisticsItem}>aaaa</div>

                {columnSelected && (
                  <DeleteOutlined
                    style={{ position: 'absolute', right: 0, top: -18, color: '#4d7bf6' }}
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteColumn?.(field);
                    }}
                  />
                )}
              </div>
            );
          })}
        </div>
      </div> */}

      {/* 删除表格 */}
      {selected && (
        <div style={{ position: 'absolute', right: 4, top: 4, color: '#4d7bf6', display: 'flex', gap: 8 }}>
          {type === 'table' && (
            <RetweetOutlined
              onClick={() => {
                toggleIO();
              }}
            />
          )}
          <DeleteOutlined
            onClick={(e) => {
              e.stopPropagation();
              onDelete?.();
            }}
          />
        </div>
      )}
    </div>
  );
};

export default DataTable;
