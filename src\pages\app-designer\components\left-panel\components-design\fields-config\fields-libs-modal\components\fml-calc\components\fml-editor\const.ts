import { ExpressionType, FuncType } from '@/types/calc-field';

export const SYMBOL_OPTIONS = [
  {
    label: '+',
    value: '+',
  },
  {
    label: '-',
    value: '-',
  },
  {
    label: '*',
    value: '*',
  },
  {
    label: '/',
    value: '/',
  },
  {
    label: '(',
    value: '(',
  },
  {
    label: ')',
    value: ')',
  },
];

export const FUNC_OPTIONS = [
  {
    title: '平均值',
    value: FuncType.AVERAGE,
    label: 'AVERAGE',
  },
  {
    title: '最大值',
    value: FuncType.MAX,
    label: 'MAX',
  },
  {
    title: '最小值',
    value: FuncType.MIN,
    label: 'MIN',
  },

  {
    title: '四舍五入',
    value: FuncType.ROUND,
    label: 'ROUND',
  },
  {
    title: '求和',
    value: FuncType.SUM,
    label: 'SUM',
  },
  {
    title: '计数',
    value: FuncType.COUNT,
    label: 'COUNT',
  },
  {
    title: '去重计数',
    value: FuncType.DISTINCT_COUNT,
    label: 'DISTINCT_COUNT',
  },
  {
    title: '向上取整',
    value: FuncType.CEIL,
    label: 'CEIL',
  },
  {
    title: '向下取整',
    value: FuncType.FLOOR,
    label: 'FLOOR',
  },
];

/** 公式计算：字段配置项 */
export const FIELDS_TYPE_OPTIONS = {
  [ExpressionType.FUNC_CALC]: { key: ExpressionType.FUNC_CALC, label: '计算' },
  // [ExpressionType.SYSTEM_FUNC_CALC]: { key: ExpressionType.SYSTEM_FUNC_CALC, label: '系统' },
};
