import { ConfigProvider, Segmented } from '@gwy/components-web';
import { memo, useContext } from 'react';
import { AppDesignerContext } from '../../context';
import { RightPanelTabType } from '../right-panel';
import ComponentsDesign from './components-design';
import BaseInfoConfig from './form-info-config';
import styles from './index.less';
import WordDesignMaterials from './word-design-materials';

export enum LeftPanelTabType {
  baseInfo = 'baseInfo', // 基础信息
  components = 'components', // 设计组件
}

export enum AppConfigSection {
  FormInfo = '1',
  DataUnit = '2',
  Field = '3',
  Message = '4',
}

const LeftPanel = memo(() => {
  const { leftPanelTabType, setLeftPanelTabType, rightPanelTabType } = useContext(AppDesignerContext);

  return (
    <div className={styles.container}>
      {/* 表单设计时 */}
      {rightPanelTabType === RightPanelTabType.FormDesign && (
        <div className={styles.formDesign}>
          <div className={styles.header}>
            <Segmented
              size="large"
              block
              style={{ width: '100%' }}
              options={[
                { label: '基础信息', value: LeftPanelTabType.baseInfo },
                { label: '设计组件', value: LeftPanelTabType.components },
              ]}
              value={leftPanelTabType}
              onChange={(val) => setLeftPanelTabType(val as LeftPanelTabType)}
            />
          </div>
          <div className={styles.content}>
            {leftPanelTabType === LeftPanelTabType.baseInfo && <BaseInfoConfig />}
            {leftPanelTabType === LeftPanelTabType.components && <ComponentsDesign />}
          </div>
        </div>
      )}

      {/* word设计时 */}
      {rightPanelTabType === RightPanelTabType.WordDesign && (
        <ConfigProvider theme={{ token: { fontSize: 14 } }}>
          <WordDesignMaterials />
        </ConfigProvider>
      )}
    </div>
  );
});

export default LeftPanel;
