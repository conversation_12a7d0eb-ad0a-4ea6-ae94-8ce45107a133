import classNames from 'classnames';
import styles from './index.less';

interface Iprops {
  conditionGroups: any[];
  theme: string;
}
const FilterItemPreview = (props: Iprops) => {
  const { conditionGroups, theme } = props;
  const logicObj = {
    AND: '且',
    OR: '或',
  };

  const renderValue = (value) => {
    if (typeof value === 'string') {
      return <span className={styles.value}>{value || '_'}</span>;
    }
    return '_';
  };

  return (
    <div className={styles.filterWrapper}>
      {(conditionGroups || []).map((item, index) => {
        const { conditionItems, groupLogic } = item || {};
        return (
          <div
            key={index}
            className={classNames(styles.filterContainer, {
              [styles.blueTheme]: theme === 'blue',
              [styles.whiteTheme]: theme !== 'blue',
            })}
          >
            <div className={styles.filterLabel}>条件组{index + 1}</div>
            {(conditionItems || []).map((conditionItem, itIdx) => {
              const { itemLogic, valueType, value, operatorDesc, tagName } = conditionItem || {};
              return (
                <div className={styles.filterItem} key={itIdx}>
                  <div>{itIdx === 0 ? '当' : logicObj[itemLogic] || '且'}</div>
                  <div className={classNames(styles.commomWrap, styles.tagWrap)}>{tagName}</div>
                  <div className={styles.commomWrap}>{operatorDesc}</div>
                  <div className={styles.valueWrap}>{renderValue(value)}</div>
                </div>
              );
            })}
            {index === conditionGroups.length - 1 ? null : (
              <div className={styles.groupLogic}>
                <div>{logicObj[groupLogic]}</div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default FilterItemPreview;
