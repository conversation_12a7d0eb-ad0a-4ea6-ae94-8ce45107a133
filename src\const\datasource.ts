// 数据归属
export const DATA_BELONGS = [
  { label: '公司', value: 1 },
  { label: '部门', value: 2 },
  { label: '岗位', value: 3 },
  { label: '用户', value: 4 },
];

// 标签操作类型
export enum TAG_OPT_ENUM {
  VIEW = -1,
  ADD = 1,
  REMOVE = 2,
  UPDATE = 3,
  Select = 4,
  ENABLE = 5,
  DISABLE = 6,
}

export const TAG_OPT_TYPE = {
  [TAG_OPT_ENUM.ADD]: {
    backgroundColor: '#00B42A',
    label: '增',
  },
  [TAG_OPT_ENUM.REMOVE]: {
    backgroundColor: '#F53F3F',
    label: '删',
  },
  [TAG_OPT_ENUM.UPDATE]: {
    backgroundColor: '#FF7D00',
    label: '改',
  },
  [TAG_OPT_ENUM.ENABLE]: {
    backgroundColor: '#4D7BF6',
    label: '启',
  },
  [TAG_OPT_ENUM.DISABLE]: {
    backgroundColor: '#F53F3F',
    label: '禁',
  },
};

export enum OPEN_ENUM {
  ENABLE = 'true',
  DISABLE = 'false',
}

export const OPEN_TYPE = {
  [OPEN_ENUM.ENABLE]: {
    color: '#00B42A',
    backgroundColor: '#E8FFEA',
    label: '启用',
  },
  [OPEN_ENUM.DISABLE]: {
    color: '#86909C',
    backgroundColor: '#F2F3F5',
    label: '禁用',
  },
};

export enum APPROVE_ENUM {
  // NO = 'NO',
  ING = 'PENDING_APPROVAL',
  AGREE = 'APPROVED',
  REFUSE = 'REJECTED',
  // RETURN = 'RETURN',
  INVALID = 'INVALID',
  STASH = 'STASH',
}

export const APPROVE_TYPE = {
  [APPROVE_ENUM.ING]: {
    color: '#FF7D00',
    backgroundColor: '#FFF7E8',
    label: '审核中',
  },
  [APPROVE_ENUM.STASH]: {
    color: '#4D7BF6',
    backgroundColor: '#E8F3FF',
    label: '暂存',
  },
};

export enum APPROVE_STATUS_ENUM {
  AGREE = 2,
  REFUSE,
  RETURN,
  CANCEL,
  REAPPROVE,
}

export const APPROVE_STATUS_TYPE = {
  [APPROVE_STATUS_ENUM.AGREE]: {
    color: 'primary',
    variant: 'solid',
    label: '同意',
  },
  [APPROVE_STATUS_ENUM.REFUSE]: {
    color: 'danger',
    variant: 'outlined',
    label: '拒绝',
  },
  [APPROVE_STATUS_ENUM.RETURN]: {
    color: 'primary',
    variant: 'outlined',
    label: '退回',
  },
  [APPROVE_STATUS_ENUM.CANCEL]: {
    color: 'primary',
    variant: 'outlined',
    label: '取消申请',
  },
  [APPROVE_STATUS_ENUM.REAPPROVE]: {
    color: 'primary',
    variant: 'solid',
    label: '重新发起',
  },
};
