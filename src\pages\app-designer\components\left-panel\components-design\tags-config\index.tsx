import { MetaTagType } from '@/const/metadata';
import Draggable from '@/pages/app-designer/components/left-panel/components-design/tags-config/draggable';
import { SelectedStateType } from '@/pages/app-designer/const';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { SourceType } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Empty } from '@gwy/components-web';
import { useContext, useMemo } from 'react';
import { addFormItem, createFormField } from '../utils';
import styles from './index.less';
import TagItem from './tag-item';

const TagsConfig = () => {
  const appDesignerContext = useContext(AppDesignerContext);
  const {
    configuredDataUnits,
    allConfiguredDataUnitsWithTags: configuredDataUnitsWithTags,
    configuredFields,
    setConfiguredFields,
    selectedState,
    setSelectedState,
    combineTagConfig,
    setCombineTagConfig,
    tableList,
    setTableList,
  } = appDesignerContext;

  const addFormField = (tag: DataUnitTagVO, dropResult) => {
    addFormItem({ formField: createFormField(tag), sourceType: SourceType.Tag }, dropResult, appDesignerContext);
  };

  const _configuredDataUnitsWithTags = useMemo(() => {
    let currentConfiguredDataUnits = [];
    if (
      [SelectedStateType.baseGroup, SelectedStateType.baseGroupItem, SelectedStateType.combineTag, SelectedStateType.combineTagItem].includes(
        selectedState.type,
      )
    ) {
      currentConfiguredDataUnits = configuredDataUnits;
    } else if ([SelectedStateType.table, SelectedStateType.tableItem].includes(selectedState.type)) {
      const table = tableList?.find((t) => t.id === selectedState.tableId);
      currentConfiguredDataUnits = table?.dataUnits;
    }
    return configuredDataUnitsWithTags
      ?.filter((dataUnit) => currentConfiguredDataUnits?.some((d) => d.dataUnitId === dataUnit.dataUnitId))
      .sort(
        (a, b) =>
          currentConfiguredDataUnits.findIndex((d) => d.dataUnitId === a.dataUnitId) -
          currentConfiguredDataUnits.findIndex((d) => d.dataUnitId === b.dataUnitId),
      );
  }, [selectedState.type, selectedState.tableId, configuredDataUnitsWithTags, tableList, configuredDataUnits]);

  if (!_configuredDataUnitsWithTags?.length) {
    return <Empty description="请先在右侧选择数据单元" style={{ margin: '24px 0' }} />;
  }

  return (
    <div className={styles.container}>
      {_configuredDataUnitsWithTags?.map((dataUnit) => {
        return (
          <div key={dataUnit.dataUnitId} className={styles.dataUnit}>
            <div className={styles.title}>
              {dataUnit.categoryName || '通用'}-{dataUnit.name}
            </div>
            <div className={styles.tagList}>
              {dataUnit.tagList
                ?.filter((t) => t.type !== MetaTagType.Special)
                ?.map((tag) => {
                  let active = false;
                  if (
                    [
                      SelectedStateType.baseGroup,
                      SelectedStateType.baseGroupItem,
                      SelectedStateType.combineTag,
                      SelectedStateType.combineTagItem,
                    ].includes(selectedState.type)
                  ) {
                    active =
                      configuredFields?.some((item) => getMetaTagUniqueId(item) === getMetaTagUniqueId(tag)) ||
                      combineTagConfig?.fields?.some((item) => getMetaTagUniqueId(item) === getMetaTagUniqueId(tag));
                  } else if ([SelectedStateType.table, SelectedStateType.tableItem].includes(selectedState.type)) {
                    active = tableList
                      ?.find((t) => t.id === selectedState.tableId)
                      ?.fields?.some((item) => getMetaTagUniqueId(item) === getMetaTagUniqueId(tag));
                  }
                  return (
                    <div key={tag.tagId} className={styles.tagItem}>
                      <Draggable<DataUnitTagVO> item={tag} onDragEnd={addFormField}>
                        <TagItem tag={tag} active={active} />
                      </Draggable>
                    </div>
                  );
                })}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default TagsConfig;
