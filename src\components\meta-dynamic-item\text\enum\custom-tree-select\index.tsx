import { ValueFormat } from '@/components/meta-dynamic-item/object/const';
import { EnumType } from '@/const/metadata';
import { MetaDataTextEnumDTO } from '@/types/metadata';
import { Empty, Select, Spin, TreeSelect, TreeSelectProps } from '@gwy/components-web';
import { useMemo } from 'react';

type Props = {
  enumType?: EnumType;
  treeData?: any[];
  value?: MetaDataTextEnumDTO[];
  onChange?: (value?: MetaDataTextEnumDTO[]) => void;
  multiple?: boolean;
  valueFormat?: ValueFormat;
} & Omit<TreeSelectProps, 'value' | 'onChange' | 'treeData'>;

export const fieldNames = {
  [EnumType.custom]: { value: 'id', label: 'item', children: 'subEnumList' },
  [EnumType.general]: { value: 'itemCode', label: 'itemLabel', children: 'subItemList' },
};
const CustomTreeSelect = ({ enumType, treeData, value, onChange, multiple, valueFormat = ValueFormat.ValueArray, loading, ...rest }: Props) => {
  const idKey = fieldNames[enumType]?.value;
  const labelKey = fieldNames[enumType]?.label;
  const childrenKey = fieldNames[enumType]?.children;

  // Helper function to find object by value in tree data
  const findObjectByValue = (searchValue: any, data: any[]): MetaDataTextEnumDTO | undefined => {
    if (!data || !Array.isArray(data)) return undefined;

    for (const item of data) {
      if (item[idKey] === searchValue) {
        return item;
      }

      if (item[childrenKey] && Array.isArray(item[childrenKey])) {
        const found = findObjectByValue(searchValue, item[childrenKey]);
        if (found) return found;
      }
    }
    return undefined;
  };

  // Helper function to extract values from objects for TreeSelect/Select
  const extractValues = (value: MetaDataTextEnumDTO[] | MetaDataTextEnumDTO | undefined): any => {
    if (!value) return undefined;

    if (Array.isArray(value)) {
      return value.map((obj) => obj.id).filter((val) => val !== undefined);
    } else {
      return value.id;
    }
  };

  // Convert object values to primitive values for the UI components
  let val: any = [];
  if (valueFormat === ValueFormat.ObjectArray) {
    val = extractValues(value);
  } else if (valueFormat === ValueFormat.ValueArray) {
    val = value;
  }

  // Handle multiple/single mode conversion
  if (val !== undefined) {
    if (multiple) {
      if (!Array.isArray(val)) {
        val = [val];
      }
    } else {
      if (Array.isArray(val)) {
        val = val[0];
      }
    }
  }

  const isTree = useMemo(() => treeData?.some((d) => Array.isArray(d[childrenKey]) && d[childrenKey].length > 0), [childrenKey, treeData]);

  // Handle change events - convert primitive values back to objects
  const handleChange = (primitiveValues: any) => {
    if (!primitiveValues) {
      return onChange?.(undefined);
    }

    const valuesArray = Array.isArray(primitiveValues) ? primitiveValues : [primitiveValues];
    const objectsArray: MetaDataTextEnumDTO[] = [];

    for (const primitiveValue of valuesArray) {
      const foundObject = findObjectByValue(primitiveValue, treeData);
      if (foundObject) {
        if (valueFormat === ValueFormat.ValueArray) {
          objectsArray.push(foundObject[idKey]);
        } else if (valueFormat === ValueFormat.ObjectArray) {
          objectsArray.push({
            id: foundObject[idKey],
            item: foundObject[labelKey],
          });
        }
      }
    }

    if (multiple) {
      return onChange?.(objectsArray.length > 0 ? objectsArray : undefined);
    } else {
      return onChange?.(objectsArray.length > 0 ? objectsArray : undefined);
    }
  };

  if (isTree) {
    return (
      <div>
        <TreeSelect
          {...rest}
          multiple={multiple}
          treeData={treeData}
          fieldNames={fieldNames[enumType]}
          value={val}
          notFoundContent={loading ? <Spin spinning /> : <Empty />}
          onChange={handleChange}
        />
      </div>
    );
  }

  return (
    <Select
      {...rest}
      mode={multiple ? 'multiple' : undefined}
      options={treeData}
      fieldNames={fieldNames[enumType]}
      value={val}
      notFoundContent={loading ? <Spin spinning /> : <Empty />}
      onChange={handleChange}
    />
  );
};

export default CustomTreeSelect;
