.container {
  position: relative;
  display: flex;
  align-items: center;

  .detail {
    position: relative;
    display: flex;
    align-items: center;
    flex: 1;
    overflow: auto;
    height: 36px;
    padding: 0 10px;
    border: 1px solid #e5e6eb;
    border-radius: 2px;
    cursor: pointer;

    &.active {
      border-color: #4d7bf6;
      background-color: #e8f3ff;
    }

    .fieldName {
      flex-grow: 1;
      overflow: auto;
      font-size: 12px;
    }
  }

  .closeIcon {
    display: none;
    position: absolute;
    top: -8px;
    right: -8px;
    color: #ccc;
    font-size: 16px;
  }

  &:hover {
    .closeIcon {
      display: block;
    }
  }
}
