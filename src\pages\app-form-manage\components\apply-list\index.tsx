/* eslint-disable max-lines */
import SortList from '@/pages/app-form-premission/components/sort-list';
import { formManageApi } from '@/services';
import { SearchOutlined } from '@ant-design/icons';
import { Col, Input, Row, Select } from '@gwy/components-web';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import ApplyListCards from './cards';
import styles from './index.module.less';

export interface ApplyListProps {
  bridge: any;
  baseInfo: any;
  setLoading: any;
  loading: boolean;
  setBaseInfo: (p: any) => void;
  searchInput: any;
  setSearchInput: (p: any) => void;
  getAppClickStatistics: () => void;
  openAppApply: (data: any) => void;
}

export type ApplyListRef = {
  refreshAppList: () => void;
};

const ApplyList = forwardRef<ApplyListRef, ApplyListProps>(
  ({ baseInfo, bridge, setLoading, setBaseInfo, searchInput, setSearchInput, openAppApply }, ref) => {
    const [folderAppList, setFolderAppList] = useState([]);

    const getFolderAppList = async (postId = undefined) => {
      const params: any = {
        postId: postId || baseInfo?.postId,
        name: searchInput,
      };

      setLoading(true);

      if (postId || baseInfo?.postId) {
        const queryApi = 'getFormApplyLists';
        const queryParams = params;
        const data = await formManageApi[queryApi]?.({ ...queryParams });
        setFolderAppList(data);
      }
      setLoading(false);
    };

    useImperativeHandle(ref, () => ({
      refreshAppList: () => {
        getFolderAppList();
      },
    }));

    useEffect(() => {
      if (baseInfo?.postId !== undefined) getFolderAppList(baseInfo?.postId);
    }, [searchInput, baseInfo?.postId]);

    const sortForms = async (values) => {
      await formManageApi.updateFormSort({
        orderType: 'FORM_APP_SORT',
        formSortList: values.map((item, index) => ({
          formVersionId: item.formVersionId,
          orderNum: index + 1,
        })),
      });
      getFolderAppList();
    };

    return (
      <div className={styles.applyList}>
        <div className={styles.headerWrapper}>
          <Row gutter={24} style={{ width: '100%' }}>
            <Col span={18} style={{ display: 'flex', columnGap: '8px' }}>
              <Input
                prefix={<SearchOutlined style={{ color: '#909296' }} />}
                style={{ width: '60%' }}
                placeholder={`搜索表单名称`}
                allowClear
                onBlur={(e) => {
                  setSearchInput(e.target.value);
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    setSearchInput((e?.target as HTMLInputElement)?.value.trim());
                  }
                }}
              />
              <Select
                value={1}
                style={{ width: '30%' }}
                options={[
                  {
                    label: '普通表单',
                    value: 1,
                  },
                ]}
              />
            </Col>
            {folderAppList?.length > 0 && (
              <Col span={6} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                <SortList
                  list={folderAppList}
                  idKeyName="formId"
                  namerKeyName="name"
                  onOk={(value) => {
                    sortForms(value);
                    // console.log(value, 'value--------');
                  }}
                />
              </Col>
            )}
          </Row>
        </div>
        <ApplyListCards
          bridge={bridge}
          baseInfo={baseInfo}
          setBaseInfo={setBaseInfo}
          folderAppList={folderAppList}
          handleAppClick={openAppApply}
          refreshAppList={getFolderAppList}
        />
      </div>
    );
  },
);

export default ApplyList;
