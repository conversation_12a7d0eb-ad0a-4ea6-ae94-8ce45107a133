import DataQuerySetting from '@/components/data-query-setting';
import { queryFieldList } from '@/services/calc-field';
import { CalcFieldVO, ExpressionType } from '@/types/calc-field';
import { getBeloneDataUnitTags } from '@/utils/calc-field/func';
import { isEmpty } from 'lodash-es';
import { useEffect, useMemo, useState } from 'react';
import { renderFormula } from '../fml-editor/utils';

type Props = {
  calcField: CalcFieldVO;
  post: any;
  configuredDataUnits: any[];
  allDataUnitsWithTags: any[];
};

const FmlDesc = ({ calcField, post, configuredDataUnits, allDataUnitsWithTags }: Props) => {
  // 当前选中的标签
  const [selected, setSelected] = useState<any>({});
  /**
   * 标签条件组设置里条件左侧数据单元标签选项
   * 取当前选中标签所在数据单元的标签
   */
  const conditionDataUnitTags = useMemo(() => {
    if (selected && selected.expressionType === ExpressionType.TAG) {
      const { dataUnitId } = selected.refTag;
      const targetDataUnit = (allDataUnitsWithTags || []).find((t) => t.dataUnitId === dataUnitId);
      return getBeloneDataUnitTags([targetDataUnit], allDataUnitsWithTags);
    }
    return [];
  }, [allDataUnitsWithTags, selected]);
  /**
   * 筛选条件右侧变量标签选项
   * 包含当前选中标签所在的数据单元，以及配置的主副数据单元的标签
   */
  const variableDataUnitTags = useMemo(() => {
    if (selected && selected.expressionType === ExpressionType.TAG) {
      const { dataUnitId } = selected.refTag;

      if ((configuredDataUnits || []).some((t) => t.dataUnitId === dataUnitId)) {
        return getBeloneDataUnitTags(configuredDataUnits, allDataUnitsWithTags);
      }
      const dataUnits = (allDataUnitsWithTags || []).filter((t) => t.dataUnitId === dataUnitId);
      return getBeloneDataUnitTags(dataUnits.concat(configuredDataUnits), allDataUnitsWithTags);
    }
    return [];
  }, [allDataUnitsWithTags, configuredDataUnits, selected]);

  const closeModal = () => {
    setOpenOpt({ open: false, type: undefined });
  };

  // 是否开启条件组设置弹窗
  const [openOpt, setOpenOpt] = useState<{ open: boolean; type: ExpressionType }>({
    open: false,
    type: undefined,
  });

  // 有权限的所有字段列表
  const [authFieldList, setAuthFieldList] = useState<CalcFieldVO[]>([]);
  const getAuthFieldList = async () => {
    const data = await queryFieldList({
      postId: post.postId,
      ifQueryRefField: false,
      fieldQueryType: 'GET_HAVE_AUTH_QUERY',
    });

    setAuthFieldList(data || []);
  };
  useEffect(() => {
    getAuthFieldList();
  }, [post]);

  /**
   * 点击公式中的标签或者运算符或者字段
   * @param {obj} 当前点击的对象
   * @param {tagPathname} 当前点击的标签在value里的路径
   */
  const handleClick = ({ obj, tagPathname }: any) => {
    // 处理标签类型
    if (obj.expressionType === ExpressionType.TAG && !isEmpty(allDataUnitsWithTags)) {
      setOpenOpt({ open: true, type: ExpressionType.TAG });
    }
    setSelected(obj);
  };

  return (
    <div>
      {renderFormula(false, calcField.formulasCalcs, handleClick)}

      {openOpt.type === ExpressionType.TAG && openOpt.open && (
        <DataQuerySetting
          disabled
          fieldType={calcField.fieldType}
          dataUnitName={selected?.refTag?.dataUnitName}
          dataUnitId={selected?.refTag?.dataUnitId}
          conditionGroups={selected?.refTag?.conditionGroups}
          extConfig={selected?.refTag?.extConfig}
          tags={conditionDataUnitTags}
          canRelateTags={variableDataUnitTags}
          calcFields={authFieldList}
          onOk={closeModal}
          onCancel={closeModal}
        />
      )}
    </div>
  );
};

export default FmlDesc;
