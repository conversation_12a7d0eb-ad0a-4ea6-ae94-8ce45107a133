import { ButtonOperateType } from '@/const/button-config';
import { ExecutionEventType } from '@/pages/app-designer/components/right-panel/form-design/button-options/constants';

/**
 * com.ideal.gwy.datasource.model.request.form.FormUpdateRequest.FormButtonConfig
 *
 * FormButtonConfig
 */
export type FormButtonConfig = {
  /**
   * 按钮样式
   */
  buttonStyle?: null | string;
  /**
   * 执行事件
   */
  events?: FormButtonEvent[] | null;
  /**
   * 按钮名称
   */
  operateType?: ButtonOperateType;
  [property: string]: any;
};

/**
 * com.ideal.gwy.datasource.model.request.form.FormUpdateRequest.FormButtonEvent
 *
 * FormButtonEvent
 */
export type FormButtonEvent = {
  /**
   * 数据同步配置
   */
  dataSyncConfigs?: any[] | null;
  /**
   * 按钮事件类型
   */
  eventType?: ExecutionEventType;
  [property: string]: any;
};
