{"name": "gwy-platform-datasource", "private": true, "author": "ggm", "scripts": {"build": "cross-env UMI_APP_ENV=prod max build", "build:dev": "cross-env UMI_APP_ENV=dev max build", "build:prep": "cross-env UMI_APP_ENV=pre max build", "build:qa": "cross-env UMI_APP_ENV=test max build", "dev": "cross-env SOCKET_SERVER=http://localhost:8003/ PORT=8003 UMI_APP_ENV=local max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky", "setup": "max setup", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@ant-design/pro-components": "^2.8.6", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@gwy/components-web": "^0.4.12", "@gwy/libs-web": "^0.2.36", "@onlyoffice/document-editor-react": "^2.0.0", "@umijs/max": "^4.2.5", "ahooks": "^3.8.1", "antd": "^5.25.2", "classnames": "^2.5.1", "data-model-trans": "^0.1.5", "dayjs": "^1.11.13", "immer": "^10.1.1", "js-base64": "^3.7.2", "lodash-es": "^4.17.21", "react-beautiful-dnd": "^13.1.1", "react-bmapgl": "^1.0.1", "react-custom-scrollbars": "~4.1.2", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-resizable": "^3.0.5", "react-sortable-hoc": "^2.0.0"}, "devDependencies": {"@types/bmapgl": "^0.0.7", "@types/lodash-es": "^4.17.12", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "cross-env": "^7.0.3", "husky": "^9", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "typescript": "^5.0.3"}, "engines": {"node": ">=20.x", "npm": "10.x"}}