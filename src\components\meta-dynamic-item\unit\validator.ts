import { MetaDataUnitDTO, UnitTypes } from '@/types/metadata';
import { isNil } from 'lodash-es';
import { validator as numberValidator } from '../number/validator';
import { unitTransform } from './utils';

export const validator = async (
  rule,
  value: {
    raw_unit: UnitTypes;
    raw_value;
  },
  required,
  metaDataUnitDTO: Required<Pick<MetaDataUnitDTO, 'unit' | 'min' | 'max' | 'type' | 'rangeType'>>,
) => {
  const { raw_unit, raw_value } = value || {};
  if (required && (!value || !raw_unit || isNil(raw_value))) {
    return Promise.reject('请填写必填项');
  }
  try {
    let { min, max, unit, type, rangeType } = metaDataUnitDTO;
    min = unitTransform(type, unit, raw_unit, min);
    max = unitTransform(type, unit, raw_unit, max);
    await numberValidator(rule, raw_value, { rangeType, min, max });
  } catch (error) {
    return Promise.reject(error);
  }
  return Promise.resolve();
};
