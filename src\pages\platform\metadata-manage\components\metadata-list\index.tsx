// import Empty from '@/components/empty';
import { APPROVE_ENUM, APPROVE_TYPE, OPEN_TYPE } from '@/const/datasource';
import { MetaUnitType } from '@/const/metadata';
import { useGlobalState } from '@/hooks';
import { datasourceAPI } from '@/services';
import {
  addDatasourceCategory,
  dataUnitCustomSort,
  deleteDatasourceCategory,
  getDatasourceCategoryList,
  getDatasourceList,
  getToursList,
  resetDataUnitCustomSort,
  sortDatasourceCategory,
  submitTourResult,
  updateDatasourceCategory,
} from '@/services/datasource';
import { MetadataManageTypes } from '@/types';
import { formatDateTime, metadataUtils } from '@/utils';
import { CaretDownOutlined, InfoCircleOutlined, LoadingOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Dropdown, Empty, Input, MenuProps, message, Select, Space, TextEllipsisTooltip, Tour } from '@gwy/components-web';
import { isEmpty, uniqueId } from 'lodash-es';
import { useEffect, useMemo, useRef, useState } from 'react';
import CreateDataSource from '../metadata-create-modal';
import MetaDataEdit from '../metadata-edit-modal';
import SortButton from '../sort-button';
import DragTabs, { Dashboard } from './components/board-tabs';
import SortableList from './components/sortable-list';
import styles from './index.less';

type openModalType = {
  type: openType;
  open: boolean;
  data: any;
};
export enum openType {
  addDatasource = 'add',
  editDatasource = 'edit',
  viewDataSource = 'view',
  approveDataSource = 'approve',
}
export enum DataSourceTypes {
  GENERAL = 'GENERAL',
  NORMAL = 'NORMAL',
}
interface Iprops {
  canEdit?: boolean;
  dataSourceType?: DataSourceTypes;
  loading: boolean;
  setLoading: (loading: boolean) => void;
}

const MetaDataList = (props: Iprops) => {
  const { userInfo } = useGlobalState() || {};
  const { canEdit, dataSourceType, loading, setLoading } = props;
  const [classifyTabs, setClassifyTabs] = useState<Dashboard[]>([
    {
      dashboardName: '全部',
      dashboardId: 'all',
      key: 'all',
      label: '全部',
      closeIcon: null,
    },
  ]);
  const [currentClassify, setCurrentClassify] = useState<Dashboard>(null);
  const [datasourceList, setDatasourceList] = useState<MetadataManageTypes.DataUnitVO[]>(null);
  const [openModal, setOpenModal] = useState<openModalType>({
    open: false,
    type: openType.viewDataSource,
    data: null,
  });
  const [dropOpen, setDropOpen] = useState({
    tempUnitId: null,
    open: false,
  });
  const [searchParams, setSearchParams] = useState({
    name: '',
    dataUnitId: '',
  });

  const [tourOpen, setTourOpen] = useState(false);
  const [tourSteps, setTourSteps] = useState([
    {
      title: '点击这里可以新增数据单元',
    },
    {
      title: '分类中没有数据单元，可以删除分类',
    },
  ]);
  const tourStep1 = useRef(null);
  const tourStep2 = useRef(null);
  const [tourCurrent, setTourCurrent] = useState(0);

  const [datasourceTypes, setDatasourceTypes] = useState([]);

  const fetchNotTour = async () => {
    const res = await Promise.all([getToursList(1), getToursList(2)]);
    if (res.length > 0) {
      let result = [];
      if (res[0] === '0') {
        result.push({
          title: '点击这里可以新增数据单元',
          target: tourStep1.current,
        });
      }
      if (res[1] === '0') {
        result.push({
          title: '分类中没有数据单元，可以删除分类',
          target: tourStep2.current,
        });
      }
      setTourSteps(result);
      if (result.length > 0) {
        setTourOpen(true);
      }
    }
  };

  const saveTourData = async (tourCurrent) => {
    const res = await submitTourResult({
      guideStatus: 2,
      type: tourCurrent,
    })
      .then((res) => true)
      .catch((res) => false);
    if (res) {
      // setTourCurrent(tourCurrent);
    }
  };

  useEffect(() => {
    if (canEdit && datasourceList?.length === 0) {
      fetchNotTour();
    }
  }, [canEdit, datasourceList]);

  const fetchClassify = async (type) => {
    const data = await getDatasourceCategoryList({
      type,
    });
    if (data.length > 0) {
      const classifyTabs = data.map((item) => ({
        dashboardId: item.categoryId,
        dashboardName: item.categoryName,
        key: item.categoryId?.toString(),
        ...item,
        label: item.categoryName,
        closeIcon: null,
      }));
      setClassifyTabs(classifyTabs);
      const useabelClassify = classifyTabs.find((item) => item.categoryId === currentClassify?.categoryId);
      console.log('useabelClassify', useabelClassify);
      isEmpty(currentClassify) && setCurrentClassify(classifyTabs[0]);
      isEmpty(useabelClassify) && setCurrentClassify(classifyTabs[0]);
      return classifyTabs;
    } else {
      if (dataSourceType === DataSourceTypes.GENERAL) {
        setCurrentClassify(classifyTabs[0]);
      }
    }
  };

  const onDashboardSwitch = (dashboardId: string) => {
    setCurrentClassify(classifyTabs.find((tab) => tab.dashboardId === dashboardId || tab.key === dashboardId));
  };

  const onChangeDashboardName = async (dashboardId: any, dashboardName: string, tabItem) => {
    console.log(tabItem, 'tabItem---');
    const { categoryId } = tabItem;
    if (categoryId && dashboardName) {
      const data = await updateDatasourceCategory(categoryId, {
        categoryName: dashboardName,
      })
        .then((res) => true)
        .catch((err) => false);
      if (data) {
        fetchClassify(dataSourceType);
        return message.success('修改成功');
      }
    }
    if (!categoryId && dashboardName) {
      const data = await addDatasourceCategory({
        categoryName: dashboardName,
      })
        .then((res) => true)
        .catch((err) => {
          // 新增失败，比如重名
          setClassifyTabs((prev) => prev.filter((item) => item.categoryId));
          setCurrentClassify(classifyTabs?.[0]);
        });
      if (data) {
        const classifyTabs: Dashboard[] = await fetchClassify(dataSourceType);
        if (classifyTabs?.length > 0) {
          setCurrentClassify(classifyTabs[classifyTabs.length - 1]);
        }
        return message.success('新增成功');
      }
    }
    if (!categoryId && !dashboardName) {
      setClassifyTabs((prev) => prev.filter((item) => !item.categoryId));
    }
  };
  const onAddDashboard = () => {
    const dashboardId = uniqueId();
    const tempDashboard: Dashboard = {
      dashboardName: `未命名分类${classifyTabs.length + 1}`,
      label: `未命名分类${classifyTabs.length + 1}`,
      dashboardId,
      key: dashboardId?.toString(),
      closeIcon: null,
    };
    setClassifyTabs((prev) => [...prev, tempDashboard]);
    setCurrentClassify(tempDashboard);
  };

  const deleteTab = async () => {
    const res = await deleteDatasourceCategory(currentClassify?.categoryId)
      .then((res) => true)
      .catch(() => false);
    if (res) {
      const prevClassifyIndex = classifyTabs.findIndex((item) => item.categoryId === currentClassify?.categoryId);
      const prevClassify = classifyTabs[prevClassifyIndex];
      if (prevClassifyIndex !== -1) {
        setCurrentClassify(prevClassify);
      } else {
        setCurrentClassify(classifyTabs[0]);
      }
      message.success('删除成功');
      fetchClassify(dataSourceType);
    }
  };

  const sortCategoryFn = async (tabList) => {
    // console.log(tabList, 'tabList');
    setClassifyTabs(tabList);
    const categoryIds = tabList.map((item) => item.categoryId);
    const data = await sortDatasourceCategory({
      categoryIds,
      type: dataSourceType,
    });
    if (data) {
      setClassifyTabs(
        data.map((item) => ({
          dashboardId: item.categoryId,
          dashboardName: item.categoryName,
          key: item.categoryId?.toString(),
          ...item,
          closeIcon: null,
        })),
      );
      setClassifyTabs(tabList);
      // fetchClassify(dataSourceType);
    }
  };

  const getDatasources = async () => {
    setLoading(true);
    const res = await getDatasourceList({
      include_draft: true,
      include_approving: true,
      type: dataSourceType,
      categoryId: currentClassify?.categoryId,
    }).catch(() => {
      return false;
    });

    if (res) {
      const data = res.map((item) => ({
        ...item,
        tempUnitId: uniqueId(),
      }));
      if (!canEdit) {
        setDatasourceList(data.filter((item) => item.status !== APPROVE_ENUM.STASH));
      }
      setDatasourceList(data);
    }
    setLoading(false);
  };

  useEffect(() => {
    // 获取数据单元类型选项
    (async () => {
      try {
        const data = await datasourceAPI.getDatasourceList({
          type: MetaUnitType.Normal,
          subType: MetaUnitType.Normal,
        });
        setDatasourceTypes(data || []);
      } catch (e) {}
    })();
  }, []);

  const datasourceTypeOptions = useMemo(() => {
    return [{ label: '全部', value: '' }].concat(datasourceTypes.map((ite) => ({ label: ite.name, value: ite.dataUnitId })));
  }, [datasourceTypes]);

  useEffect(() => {
    if (currentClassify?.categoryId && dataSourceType === DataSourceTypes.NORMAL) {
      getDatasources();
    }
    if (dataSourceType === DataSourceTypes.GENERAL) {
      getDatasources();
    }
  }, [currentClassify]);

  useEffect(() => {
    fetchClassify(dataSourceType);
  }, [dataSourceType]);

  const handleSortByTime = (key: string, sort: number) => {
    const sortedDatasourceList = metadataUtils.sortByTimeKey(datasourceList, key, sort);
    setDatasourceList(sortedDatasourceList);
  };

  const canSortableDatasourceList = useMemo(() => {
    // 1.【是否启用】状态为启用的数据单元
    // 2.审批通过的数据单元
    // 3.生成临时id（用于排序）
    return datasourceList
      ?.filter((item) => item.enable && item.status === APPROVE_ENUM.AGREE)
      .map((item) => ({
        ...item,
        _sortId: item.lightDataUnitId ? `${item.lightDataUnitId}-${item.currentVersionId}` : item.currentVersionId,
      }));
  }, [datasourceList]);

  const handleSortableSubmit = async (newList: MetadataManageTypes.DataUnitVO[]) => {
    // 如果是轻量级数据单元，则传轻量数据单元id (lightDataUnitId)，否则传数据单元id (dataUnitId)
    const dataUnitIds = newList.map((item) => {
      const { light, lightDataUnitId, dataUnitId } = item;
      return {
        light,
        dataUnitId: light ? lightDataUnitId : dataUnitId,
      };
    });
    await dataUnitCustomSort({
      type: dataSourceType,
      categoryId: currentClassify?.categoryId,
      dataUnitIds,
    });
    message.success('排序成功');
    getDatasources();
  };

  const handleResetSortable = async () => {
    await resetDataUnitCustomSort({
      type: dataSourceType,
      categoryId: currentClassify?.categoryId,
      dataUnitIds: [],
    });
    message.success('重置成功');
    getDatasources();
  };

  const versionItems = (item: MetadataManageTypes.DataUnitVO): MenuProps['items'] => {
    const versions = item.versions ?? [];
    const tempVersions = (item?.versions || []).filter((version) => version.status === APPROVE_ENUM.STASH);
    let _isView = false;

    if (!canEdit) {
      // 无编辑权限时，只能查看
      _isView = true;
    }

    return versions.map((version, index) => {
      return {
        key: version.dataUnitVersionId,
        label: (
          <Button
            color="default"
            variant="link"
            size="small"
            // disabled={version.approveStatus === APPROVE_ENUM.ING}
            onClick={(e) => {
              e.stopPropagation();
              const isLatest = item.currentVersionId === version.dataUnitVersionId; // 是否是最新版本
              const inStash = [APPROVE_ENUM.STASH].includes(version.status);
              const inApproving = [APPROVE_ENUM.ING].includes(version.status);
              let type = openType.editDatasource;
              const tempVersionId = tempVersions?.length ? tempVersions[tempVersions?.length - 1]?.dataUnitVersionId : null;
              const submitIsMySelf = version.createUserId === userInfo?.id;
              // const isNearest = item.currentVersionId === version.dataUnitVersionId;

              if ((!isLatest && !inStash) || _isView || inApproving) {
                type = openType.viewDataSource;
              }

              if (inApproving && !submitIsMySelf) {
                return message.warning('他人提交审核版本，您暂无查看权限');
              }

              setDropOpen({
                open: false,
                tempUnitId: null,
              });
              setOpenModal({
                open: true,
                type: type,
                data: {
                  ...currentClassify,
                  currentVersionId: version.dataUnitVersionId,
                  canEdit,
                  preVersionId: version.preVersionId,
                  tempVersionId,
                  version,
                  unitData: item,
                },
              });
            }}
          >
            {version.version}
            <div
              className={styles.tag}
              style={{
                color: APPROVE_TYPE?.[version.status]?.color,
                borderColor: APPROVE_TYPE?.[version.status]?.color,
                backgroundColor: APPROVE_TYPE?.[version.status]?.backgroundColor,
              }}
            >
              {APPROVE_TYPE?.[version.status]?.label}
            </div>
          </Button>
        ),
      };
    });
  };
  const renderDataSourceItem = (item: MetadataManageTypes.DataUnitVO) => {
    const tempVersions = (item?.versions || []).filter((version) => version.status === APPROVE_ENUM.STASH);
    const tempVersionId = tempVersions?.length ? tempVersions[tempVersions?.length - 1]?.dataUnitVersionId : null;

    const submitIsMySelf = item.createUserId === userInfo?.id;
    const isApproved = item.status === APPROVE_ENUM.ING;
    return (
      <div
        key={item.lightDataUnitId ? `${item.lightDataUnitId}-${item.currentVersionId}` : item.currentVersionId}
        className={styles.datasourceItem}
        onClick={() => {
          let checkType = !item.dataUnitId ? openType.addDatasource : openType.editDatasource;
          if (isApproved && !submitIsMySelf) {
            return message.warning('他人提交审核版本，您暂无查看权限');
          }
          setOpenModal({
            open: true,
            type: checkType,
            data: {
              ...currentClassify,
              currentVersionId: item.currentVersionId,
              addModalType: isApproved ? 'view' : 'add',
              canEdit,
              isLatest: true,
              versions: item?.versions,
              tempVersionId,
              unitData: item,
            },
          });
        }}
        // onClick={() =>
        //   navigateToDatasourceDetail({ datasourceId: item.id, version: item.version, isLatest: true, state: null, isView: true })
        // }
      >
        <div className={styles.datasourceItemTitleBg}></div>
        <div className={styles.datasourceTop}>
          {/* {renderDatabaseIcon(item)} */}
          <div className={styles.datasourceTitle}>
            <div className={styles.nameBox}>
              <div className={styles.textName}>
                <TextEllipsisTooltip text={item.name} />
              </div>
            </div>
            <div className={styles.createdTime}>
              <TextEllipsisTooltip
                text={
                  metadataUtils.isCommonMeta(item) ? (
                    '系统预设'
                  ) : (
                    <>
                      <div> {item.updateUser ? '更新信息' : `创建信息`}: &nbsp;&nbsp;</div>
                      {item.updateUser ? item.updateUser : item.createUser}
                      &nbsp;&nbsp;
                      {formatDateTime(item.updateUser ? item.bizUpdateTime : item.bizCreateTime)}
                    </>
                  )
                }
              />
              <div>
                <span style={{ marginRight: '10px', fontWeight: '600', color: '#1D2129', fontSize: '14px' }}>{item.tagNum ?? 0}</span>
                <span>标签</span>
              </div>
            </div>
          </div>
        </div>
        {/* <Divider style={{ margin: '10px 0' }} /> */}
        <div className={styles.descDiv}>
          {/* <div className={styles.desc}>{item.description || '暂无描述'}</div> */}
          <div className={styles.tagsDiv}>
            <div className={styles.left}>
              {/* <div className={styles.tagNum}>标签 ({item.tagNumber ?? 0})</div> */}
              {metadataUtils.isCommonMeta(item) ? null : (
                <Dropdown
                  key="versionsDropdown"
                  open={dropOpen?.open && dropOpen.tempUnitId === item.tempUnitId}
                  trigger={['click']}
                  onOpenChange={() => {
                    setDropOpen({
                      open: false,
                      tempUnitId: null,
                    });
                  }}
                  menu={{ items: versionItems(item) }}
                  overlayClassName={styles.versionDropdown}
                >
                  <span
                    onClick={(e) => {
                      e.stopPropagation();
                      setDropOpen({
                        tempUnitId: item?.tempUnitId,
                        open: true,
                      });
                    }}
                  >
                    <Space>
                      {item?.versions?.length > 0 && <CaretDownOutlined />}
                      {item.currentVersion}
                      {item.versionsLoading && <LoadingOutlined />}
                      {item.versions?.some((version) => [APPROVE_ENUM.ING, APPROVE_ENUM.STASH].includes(version.status as APPROVE_ENUM)) && (
                        <InfoCircleOutlined style={{ color: '#FF7D00' }} />
                      )}
                    </Space>
                  </span>
                </Dropdown>
              )}
            </div>
            <div className={styles.tagBox}>
              {(item?.versions?.length === 0 || item.versions === null) && !item?.light ? (
                <div
                  className={styles.tag}
                  style={{
                    color: APPROVE_TYPE?.[item.status]?.color,
                    borderColor: APPROVE_TYPE?.[item.status]?.color,
                    backgroundColor: APPROVE_TYPE?.[item.status]?.backgroundColor,
                  }}
                >
                  {APPROVE_TYPE?.[item.status]?.label}
                </div>
              ) : (
                <div
                  className={styles.tag}
                  style={{
                    color: OPEN_TYPE?.[(item?.enable || 'false')?.toString()]?.color,
                    borderColor: OPEN_TYPE?.[(item?.enable || 'false')?.toString()]?.color,
                    backgroundColor: OPEN_TYPE?.[(item?.enable || 'false')?.toString()]?.backgroundColor,
                  }}
                >
                  {OPEN_TYPE?.[(item?.enable || 'false').toString()]?.label}
                </div>
              )}
            </div>
            {/* || !canEdit */}
            {/* {metadataUtils.isCommonMeta(item) ? null : (
              <Dropdown
                key="dropdown"
                open={item.isDropdownOpen}
                trigger={['click']}
                menu={{ items: items(item) }}
                // onOpenChange={(open) => handleDropdownOpen(open, item)}
                placement="bottom"
              >
                <Button
                  type="text"
                  size="small"
                  style={{ fontSize: 24, padding: 6 }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <span style={{ height: 48 }}>...</span>
                </Button>
              </Dropdown>
            )} */}
          </div>
        </div>
      </div>
    );
  };

  const renderDataSourceList = () => {
    return (datasourceList || [])
      .filter(
        (item) => (item.name || '').includes(searchParams.name) && (!searchParams.dataUnitId || item.parentDataUnitId === searchParams.dataUnitId),
      )
      .map((item) => {
        return item ? renderDataSourceItem(item) : null;
      });
  };

  const genFuncBar = () => (
    <div className={styles.sortBar} onClick={(event) => event.stopPropagation()}>
      <div className={styles.sortItem}>
        创建时间&nbsp;&nbsp;
        <SortButton
          onClick={(sort) => {
            handleSortByTime('bizCreateTime', sort);
          }}
        />
      </div>
      <div className={styles.sortItem}>
        更新时间&nbsp;&nbsp;
        <SortButton
          onClick={(sort) => {
            handleSortByTime('bizUpdateTime', sort);
          }}
        />
      </div>
      {canEdit && (
        <SortableList
          datasourceList={canSortableDatasourceList}
          className={styles.sortItem}
          onSubmit={handleSortableSubmit}
          onReset={handleResetSortable}
        />
      )}

      {canEdit && (
        <div
          ref={tourStep1}
          className={styles.sortItem}
          style={{ color: '#5881f5' }}
          onClick={() => {
            if (tourOpen) return;
            setOpenModal({
              open: true,
              type: openType.addDatasource,
              data: {
                ...currentClassify,
              },
            });
          }}
        >
          <PlusCircleOutlined style={{ marginRight: '8px' }} />
          新增数据单元
        </div>
      )}
    </div>
  );
  return (
    <>
      <div className={styles.datasourceWrap}>
        <Tour
          open={tourOpen}
          onClose={() => setTourOpen(false)}
          steps={tourSteps}
          current={tourCurrent}
          onFinish={() => {
            setTourOpen(false);
          }}
          actionsRender={(originNode, { current, total }) => (
            <>
              <Button
                size="small"
                type="primary"
                onClick={() => {
                  setTourCurrent(current + 1);
                  saveTourData(current + 1);
                }}
              >
                知道了
              </Button>
              {/* {originNode} */}
            </>
          )}
        />
        <div className={styles.classifyTabs}>
          <DragTabs
            tabs={classifyTabs}
            activeKey={currentClassify?.key}
            onDashboardSwitch={onDashboardSwitch}
            onChangeDashboardName={onChangeDashboardName}
            onAddDashboard={onAddDashboard}
            canAdd={canEdit}
            onTabsChange={(tabs) => {
              sortCategoryFn(tabs);
            }}
          />
        </div>
        <div className={styles.datasourceContent}>
          <div className={styles.searchfunHeader}>
            <div style={{ display: 'flex' }}>
              <Input.Search
                placeholder="搜索数据单元名称"
                className={styles.searchInput}
                onSearch={(e) => {
                  setSearchParams((pre) => ({ ...pre, name: e }));
                }}
              />
              <Select
                style={{ width: 150 }}
                value={searchParams.dataUnitId}
                options={datasourceTypeOptions}
                onChange={(v) => setSearchParams((pre) => ({ ...pre, dataUnitId: v || '' }))}
                allowClear={false}
              />
            </div>
            <div className={styles.funcList}>{genFuncBar()}</div>
          </div>
          {datasourceList?.length > 0 ? (
            <div className={styles.datasourceListBox}>
              <div className={styles.datasourceList}>{renderDataSourceList()}</div>
            </div>
          ) : (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 'calc(100% - 60px)' }}>
              <Empty
                description={
                  <div ref={tourStep2}>
                    暂无数据单元，
                    <span
                      onClick={() => {
                        if (tourOpen) {
                          return;
                        }
                        deleteTab();
                      }}
                      style={{ color: '#2878ff', cursor: 'pointer' }}
                    >
                      删除分类
                    </span>
                  </div>
                }
              />
            </div>
          )}
        </div>
      </div>
      {openModal.open && openType.addDatasource === openModal.type && (
        <CreateDataSource
          onCancel={() => setOpenModal((prev) => ({ ...prev, open: false }))}
          onOk={() => setOpenModal((prev) => ({ ...prev, open: false }))}
          refresh={getDatasources}
          categoryId={openModal?.data?.categoryId}
          dataSourceType={dataSourceType}
          currentVersionId={openModal?.data?.currentVersionId}
          operateType={openModal.type}
          addModalType={openModal?.data?.addModalType}
          datasourceTypes={datasourceTypes}
          {...openModal.data}
        />
      )}
      {openModal.open && [openType.editDatasource, openType.viewDataSource].includes(openModal.type) && (
        <MetaDataEdit
          operateType={openModal.type}
          onCancel={() => setOpenModal((prev) => ({ ...prev, open: false }))}
          onOk={() => setOpenModal((prev) => ({ ...prev, open: false }))}
          refresh={getDatasources}
          {...openModal.data}
          dataSourceType={dataSourceType}
          editPermission={canEdit}
          versions={openModal?.data?.versions?.filter((ver) => ![APPROVE_ENUM.STASH, APPROVE_ENUM.ING].includes(ver.status))}
        />
      )}
    </>
  );
};

export default MetaDataList;
