import dayjs from 'dayjs';

/**
 * 格式化时间
 * 格式: 年-月-日 时:分，示例：2023-01-01 01:01、01-01 01:01（如果是当年的话，不显示年份）
 */
export const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';

  const dateTime = dayjs(dateTimeStr);
  let formatStr = 'YYYY/MM/DD HH:mm';

  // 当年时间，不展示年份
  if (dateTime.year() === dayjs().year()) {
    formatStr = 'MM/DD HH:mm';
  }

  return dateTime.format(formatStr);
};

// 若在集团和企业中只有负责人/法定代表人/管理员的身份，左侧岗位列表中，在集团和公司层级直接展示对应的身份，
// 显示简称（例如：理想连线—董事长；连线网络—法定代表人；连线策划—管理员1），该字段可点击，右侧展示该岗位的表单；岗位前面不展示箭头；
export const getMergeTreeData = (data) => {
  function loop(arr) {
    arr.map((item) => {
      const id = item.id || item.postId || item.blocId || item.orgId;
      item.key = id;
      item.title = item.postName || item.name || item.title;
      item.value = id;
      item.label = `${item.orgShortName || item.orgName || item.blocOrganizationName}/${item.name || item.postName}`;
      item.selectable = !!(item.id || item.postId);
      item.isLeaf = !!(item.id || item.postId);
      if (item.children || item.posts) {
        item.children = [...(item.posts || []), ...(item.children || [])];
        const { children = [] } = item;

        for (let i = 0; i < children.length; i++) {
          const child = children[i];
          const { deptProperty, postProperty, postId, postName, orgShortName, orgName, blocOrganizationName } = child;
          // 表示管理岗或者管理员
          const isManager = deptProperty === 1 && [1, 3].includes(postProperty);

          if (isManager) {
            children.splice(i, 1);
            Object.assign(item, {
              ...child,
              key: postId,
              title: postName,
              value: postId,
              label: `${orgShortName || orgName || blocOrganizationName}/${item.postName}`,
              selectable: !!postId,
              isLeaf: !!postId,
              children,
            });
            break;
          }
        }
        loop([...(item.posts || []), ...(item.children || [])]);
      }
      return item;
    });
  }
  loop(data);

  return data;
};

/**
 * 树形岗位选择treeSelect 选中第一个
 */
export const getFirstChildPost = (treeData) => {
  const getFirstChild = (treeData) => {
    if (!treeData || treeData.length === 0) return null;
    const leafNode = treeData.find((item) => item.isLeaf);
    if (leafNode) {
      return leafNode;
    }
    return getFirstChild(
      treeData.reduce((next, item) => {
        if (Array.isArray(item.children) && item.children.length > 0) {
          return next.concat(item.children);
        }
        return next;
      }, []),
    );
  };
  return getFirstChild(treeData);
};

/**
 * 平铺树形岗位数据
 */
export function collectPosts(tree) {
  let result = [];
  function traverse(node) {
    if (node.posts) {
      result = result.concat(node.posts);
    }
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        traverse(child);
      }
    }
  }
  for (const root of tree) {
    traverse(root);
  }
  return result;
}

/**
 * 树形岗位选择treeSelect 选中第一个
 */
export const getTreeData = (data) => {
  // let data = [...treeData];
  function loop(arr) {
    arr.map((item) => {
      const id = item.id || item.postId || item.blocId || item.orgId;
      item.key = id;
      item.title = item.postName || item.name || item.title;
      item.value = id;
      item.label = `${item.orgShortName || item.orgName || item.blocOrganizationName}/${item.name || item.postName}`;
      item.selectable = !!(item.id || item.postId);
      item.isLeaf = !!(item.id || item.postId);
      if (item.children || item.posts) {
        item.children = [...(item.posts || []), ...(item.children || [])];
        loop([...(item.posts || []), ...(item.children || [])]);
      }
      return item;
    });
  }
  loop(data);

  return data;
};
