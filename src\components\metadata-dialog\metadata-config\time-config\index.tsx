import CustomDatePicker from '@/components/custom-date-picker';
import CustomRangePicker from '@/components/custom-range-picker';
import { TimeRangeType, timeRangeTypeOptions } from '@/const/metadata';
import { DatePickerProps, Form, FormInstance, Select, Space } from '@gwy/components-web';
import { Dayjs } from 'dayjs';
import { useEffect } from 'react';

export const DateTimeDTOKey = 'metaDataDateTimeDTO';

type Props = {
  form?: FormInstance;
  minDate?: Dayjs;
  maxDate?: Dayjs;
  disabledTime?: DatePickerProps['disabledTime'];
};

const TimeConfig = ({ form, minDate, maxDate, disabledTime }: Props) => {
  const rangeType = Form.useWatch([DateTimeDTOKey, 'rangeType'], form);
  const start = Form.useWatch([DateTimeDTOKey, 'start'], form);
  const end = Form.useWatch([DateTimeDTOKey, 'end'], form);

  useEffect(() => {
    if (start && end) {
      form.setFieldValue([DateTimeDTOKey, 'startAndEnd'], [start, end]);
    } else {
      form.setFieldValue([DateTimeDTOKey, 'startAndEnd'], []);
    }
  }, [start, end]);

  return (
    <div>
      <Form.Item label="配置时间范围" required style={{ marginBottom: 0 }}>
        <Space align="start">
          <Form.Item name={[DateTimeDTOKey, 'rangeType']} initialValue={TimeRangeType.unlimited} rules={[{ required: true, message: '请选择' }]}>
            <Select
              options={timeRangeTypeOptions}
              onChange={() => {
                form.setFieldValue([DateTimeDTOKey, 'start'], undefined);
                form.setFieldValue([DateTimeDTOKey, 'end'], undefined);
                form.setFieldValue([DateTimeDTOKey, 'startAndEnd'], undefined);
              }}
            />
          </Form.Item>

          {rangeType === TimeRangeType.before && (
            <Form.Item name={[DateTimeDTOKey, 'end']} rules={[{ required: true, message: '请选择' }]}>
              <CustomDatePicker minDate={minDate} maxDate={maxDate} disabledTime={disabledTime} showTime />
            </Form.Item>
          )}

          {rangeType === TimeRangeType.after && (
            <Form.Item name={[DateTimeDTOKey, 'start']} rules={[{ required: true, message: '请选择' }]}>
              <CustomDatePicker minDate={minDate} maxDate={maxDate} disabledTime={disabledTime} showTime />
            </Form.Item>
          )}

          {rangeType === TimeRangeType.between && (
            <>
              <Form.Item name={[DateTimeDTOKey, 'startAndEnd']} rules={[{ required: true, message: '请选择' }]}>
                <CustomRangePicker minDate={minDate} maxDate={maxDate} disabledTime={disabledTime} showTime />
              </Form.Item>
              <Form.Item hidden name={[DateTimeDTOKey, 'start']} />
              <Form.Item hidden name={[DateTimeDTOKey, 'end']} />
            </>
          )}
        </Space>
      </Form.Item>
    </div>
  );
};

export default TimeConfig;
