import CustomRangePicker from '@/components/custom-range-picker';
import { datasourceAPI, datasourceDataAPI, datasourceLightAPI } from '@/services';
import { LeftOutlined } from '@ant-design/icons';
import { Button, Select, TablePaginationConfig } from '@gwy/components-web';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import DataList from '../data-list';
import styles from './index.less';

type Props = {
  dataUnitId?: string;
  lightDataUnitId?: string;
  dataUnitVersionId?: number;
  orgId?: string;
  orgName?: string;
  showBack?: boolean;
  onClickBack?: () => void;
};

const DataListWrapper = ({ dataUnitId, lightDataUnitId, dataUnitVersionId, orgId, orgName, showBack, onClickBack }: Props) => {
  const [tags, setTags] = useState([]);
  const [dataSource, setDataSource] = useState([]);

  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [type, setType] = useState(0);

  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
  });
  const [total, setTotal] = useState(0);

  const fetchTags = async () => {
    let tags: any[] = [];
    if (lightDataUnitId) {
      tags = await datasourceLightAPI.getTagList(lightDataUnitId, { systemFlag: true });
    } else {
      tags = await datasourceAPI.getVersionTagList(dataUnitVersionId, { systemFlag: true });
    }
    setTags(tags);
  };

  useEffect(() => {
    fetchTags();
  }, []);

  const fetchDataSource = async () => {
    const params = {
      orgId,
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
      startTime,
      endTime,
      type,
    };
    const { records, total } = await datasourceDataAPI.getDataStatisticsDataPage(dataUnitId, params);
    setDataSource(records);
    setTotal(total);
  };

  useEffect(() => {
    fetchDataSource();
  }, [dataUnitId, orgId, type, pagination]);

  return (
    <div className={styles.container}>
      {showBack && (
        <div className={styles.pageHeader}>
          <span className={styles.backWrapper} onClick={() => onClickBack?.()}>
            <LeftOutlined />
            <span>查看数据-{orgName}</span>
          </span>
        </div>
      )}

      <DataList
        tags={tags}
        dataSource={dataSource}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total,
          onChange: (page, pageSize) => {
            setPagination((pre) => ({ ...pre, current: page, pageSize }));
          },
        }}
        headerTitle={
          <div className={styles.headerTitle}>
            <div>
              <span style={{ marginRight: 10, fontWeight: 'normal' }}>操作时间</span>
              <CustomRangePicker
                value={[startTime, endTime]}
                onChange={(dateStrings) => {
                  setStartTime(dayjs(dateStrings[0])?.startOf('days')?.format('YYYY-MM-DD HH:mm:ss'));
                  setEndTime(dayjs(dateStrings[1])?.endOf('days')?.format('YYYY-MM-DD HH:mm:ss'));
                }}
              />
            </div>
            <Button
              type="primary"
              onClick={() => {
                setPagination((pre) => ({ ...pre, current: 1 }));
              }}
            >
              搜索
            </Button>
            <Button
              onClick={() => {
                setStartTime('');
                setEndTime('');
              }}
            >
              清空
            </Button>
          </div>
        }
        toolBarRender={() => [
          <Select
            key={'type'}
            options={[
              { label: '全部数据', value: 0 },
              { label: '最新数据', value: 1 },
            ]}
            value={type}
            onChange={(value) => setType(value)}
            allowClear={false}
          />,
        ]}
      />
    </div>
  );
};

export default DataListWrapper;
