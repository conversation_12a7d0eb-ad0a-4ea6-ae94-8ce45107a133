import { dataWrapper, requestUtil } from '@gwy/libs-web';

/**
 *
 * 获取数据单元的数据统计
 */
export const getDataStatistics = (data_unit_id) => dataWrapper(requestUtil.get(`gwy-datasource-data/api/dataStatistics/${data_unit_id}`));

/**
 *
 * 分页获取数据单元的有数据公司列表
 */
export const getDataStatisticsOrgPage = (data_unit_id, params) =>
  dataWrapper(requestUtil.get(`gwy-datasource-data/api/dataStatistics/${data_unit_id}/orgPage`, { params }));

/**
 * 获取数据单元对应的标签列表（拼上了系统固定标签）
 */
export const getDataStatisticsTagList = (data_unit_id) =>
  dataWrapper(requestUtil.get(`gwy-datasource-data/api/dataStatistics/${data_unit_id}/tagList`));

/**
 * 分页获取数据单元的数据列表
 */
export const getDataStatisticsDataPage = (data_unit_id, params) =>
  dataWrapper(requestUtil.get(`gwy-datasource-data/api/dataStatistics/${data_unit_id}/dataPage`, { params }));

/**
 * 发起表单-提交
 */
export const submitFormData = (params) => dataWrapper(requestUtil.post(`gwy-datasource-data/api/form_datas/initiate/submit`, params));

/**
 * 审核表单-提交
 */
export const examineFormData = (params) => dataWrapper(requestUtil.post(`gwy-datasource-data/api/form_datas/examine/submit`, params));

/**
 * word渲染
 */
export const renderWord = (params) => dataWrapper(requestUtil.post(`gwy-datasource-data/api/form_datas/renderWord`, params));

/**
 * 查询数据
 */
export const queryFormData = (params) => dataWrapper(requestUtil.post(`gwy-datasource-data/api/form_datas/query`, params));

/**
 * 查询数据单元唯一标签的数据
 */
export const queryIdentifierTagData = (params) => dataWrapper(requestUtil.post(`gwy-datasource-data/api/form_datas/queryIdentifierTagData`, params));

/**
 * 获取默认值
 * @param formVersionId required
 * @param tableId
 * @param postId
 */

export const getDefaultValue = (params) =>
  dataWrapper(
    requestUtil.get(`gwy-datasource-data/api/form_datas/getFormDefaultValue`, {
      params,
    }),
  );

/**
 * 数据历史----查看
 * @param formVersionId
 * @param dataId
 * @param postId
 * @param dataUnitId
 */

export const getHistoryData = (params) => dataWrapper(requestUtil.post(`gwy-datasource-data/api/form_datas/viewDataHistory`, params));

/**
 * 数据查询---支持多数据格式
 */

export const queryMutData = (params) => dataWrapper(requestUtil.post(`gwy-datasource-data/api/form_datas/queryMul`, params));
