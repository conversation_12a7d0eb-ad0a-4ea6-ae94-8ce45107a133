// 编辑器模式
export enum MODE_TYPE {
  VIEW = 1, // 查看
  EDIT = 2, // 编辑
}

// 七牛云空白文档
export const blankWordUrl = 'https://doc.gongwuyun.net/2fd78dce5875432d8c03ae46ec12a88f.docx';

export enum ControlType {
  TEXT = 'text',
  AREATEXT = 'areatext',
  PICTURE = 'picture',
  LIST = 'list',
  DATE = 'date',
  CHECKBOX = 'checkbox',
  IMAGE = 'image',
  GROUP = 'group',
}

// 请注意以下枚举需与OO插件内部设定关键字一致，请勿随意改动
export enum ExpCommandType {
  LISTLINKAGE = 'listlinkage', // 下拉框数据联动
  SETTEXTVALUE = 'settextvalue', // 设置文本值
  SETCHECKBOXVALUE = 'setcheckboxvalue', // 设置多选按钮
  SETDATEVALUE = 'setdatevalue', // 设置时间值
  SETLISTVALUE = 'setlistvalue', // 设置下拉框值
  SETPICTUREVALUE = 'setpicturevalue', // 设置图片
}

// 编辑器的数据类型
export enum EditorTargetType {
  tag = 1, // 标签
  field = 2, // 字段
  widget = 3, // 控件
}

export interface IConfig {
  targetType: EditorTargetType;
  targetId: string;
  targetName: string;
  // 数列id
  ruleId?: string;
}

// 构造占位控件数据（目前只有tag）
export const createOnlyofficeControlTag = (config: IConfig) => {
  const NamedSource = JSON.stringify(config);

  return {
    Id: config.targetId,
    Tag: generateTag({ NamedSource, ExpCommand: ExpCommandType.SETTEXTVALUE }),
    Lock: 3,
    Appearance: 1,
    InternalId: `${Date.now()}_${config.targetId}`,
    Alias: config.targetName,
    PlaceHolderText: config.targetName || '',
  };
};

// 生成control tag
export function generateTag(config: any) {
  // 注意Tag的划分 Tag字符长度限制60 一下为Tag设定划分
  // 源数据Key|自身guid|inputhelp标记/input对应字典中的Key|跟随选定input的字典结构guid/FS是关系标记
  // namedSource|guid|inputhelp/inputKey|guid/Fs
  let _tag = `${config.NamedSource}|${config.NamedSource}`;
  if (config.IsSelectInput && config.SelectInputKey) {
    _tag += `|inputhelp/${config.SelectInputKey}`;
  } else if (config.IsListHelp && config.ListHelpKey && config.ControlType?.toLocaleLowerCase() === 'list') {
    _tag += `|listhelp/${config.ListHelpKey}`;
  }
  if (config.ExpCommand) {
    _tag += `|ExpCom/${config.ExpCommand}`;
  }
  if (config.RelationKey) {
    _tag += `||${config.RelationKey}/FS`;
  }
  return _tag;
}
