import classNames from 'classnames';
import { CSSProperties } from 'react';
import styles from './index.less';

type Props = {
  text: string;
  isSmall?: boolean;
  className?: string;
  backgroundColor?: CSSProperties['backgroundColor'];
};

const TagCustomIcon = ({ className, isSmall, backgroundColor, text }: Props) => {
  return (
    <div
      style={{ backgroundColor }}
      className={classNames(styles.container, className, {
        [styles.small]: isSmall,
      })}
    >
      {text}
    </div>
  );
};

export default TagCustomIcon;
