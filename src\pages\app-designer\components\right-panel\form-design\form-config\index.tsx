import MetaDynamicItem from '@/components/meta-dynamic-item';
import { UseIn } from '@/components/meta-dynamic-item/const';
import { MetaTagType } from '@/const/metadata';
import { useBridge } from '@/hooks';
import { openAppApply } from '@/pages/app-apply';
import { SelectedStateType } from '@/pages/app-designer/const';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { SourceType } from '@/types/form-field';
import { getCalcFieldDisplayName, getMetaTagUniqueId } from '@/utils/metadata';
import { DeleteOutlined, EyeOutlined, InfoCircleOutlined, RetweetOutlined } from '@ant-design/icons';
import { closestCenter, DndContext, DragEndEvent, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, rectSortingStrategy, SortableContext, sortableKeyboardCoordinates, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Form, Modal, Tag, Tooltip } from '@gwy/components-web';
import classNames from 'classnames';
import { produce } from 'immer';
import { isEmpty } from 'lodash-es';
import { forwardRef, ForwardRefRenderFunction, useContext, useImperativeHandle, useMemo } from 'react';
import { DraggableType, DropResultName } from '../../../left-panel/components-design/tags-config/draggable';
import SubFormTabs from '../../../sub-form-tabs';
import { validateFormFields } from '../utils';
import ButtonConfigs from './button-configs';
import DataTable from './data-table';
import Dustbin from './dustbin';
import styles from './index.less';

export type FormConfigProps = {
  onDelete?: (id: number | string) => void;
};

export type FormConfigRef = {
  getFieldsValue: () => any;
};

export const FormConfigListKey = 'formConfigList';

// 简洁的 SortableItem 组件
const SortableItem = ({ id, widgetWith, children }) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });

  // 根据 widgetWith 计算 grid-column 跨度
  const getGridColumnSpan = (width: string) => {
    switch (width) {
      case '100%':
        return 'span 12';
      case '50%':
        return 'span 6';
      default:
        return 'span 6';
    }
  };

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    gridColumn: getGridColumnSpan(widgetWith || '50%'),
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {children}
    </div>
  );
};

const FormConfig: ForwardRefRenderFunction<FormConfigRef, FormConfigProps> = (_props, ref) => {
  const {
    post,
    appInfo,
    appType,
    handleUpdate,
    tagsMap,
    configuredFields,
    setConfiguredFields,
    combineTagConfig,
    setCombineTagConfig,
    tableList,
    setTableList,
    selectedState,
    setSelectedState,
    buttonConfigs,
    baseGroupName,
    getPropertyConfigRef,
    groupFlag,
    currSubDataUnitId,
    handleAggregationSubUpdate,
    handleAggregationUpdate,
  } = useContext(AppDesignerContext);

  const [form] = Form.useForm();

  const bridge = useBridge();

  useImperativeHandle(ref, () => ({
    getFieldsValue: () => {
      return form.getFieldsValue();
    },
  }));

  // 配置传感器 - 添加激活约束来区分点击和拖拽
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 需要拖拽8px才激活拖拽
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  // 拖拽排序处理
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setConfiguredFields((items) => {
        const oldIndex = items.findIndex((item) => getMetaTagUniqueId(item) === active.id);
        const newIndex = items.findIndex((item) => getMetaTagUniqueId(item) === over.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  const renderItems = () => {
    return configuredFields?.map((field) => {
      const uniqueId = getMetaTagUniqueId(field);

      // 标签
      const renderTag = () => {
        const tag = tagsMap[getMetaTagUniqueId(field)];
        if (!tag) {
          return null;
        }
        return (
          <>
            <Form.Item hidden name={[uniqueId, 'id']} initialValue={field.sourceId} />
            {/* name 方便调试 */}
            <Form.Item hidden name={[uniqueId, 'name']} initialValue={tag.name} />

            <Form.Item hidden name={[uniqueId, 'dataUnitId']} initialValue={field.dataUnitId} />
            <MetaDynamicItem useIn={UseIn.Designer} tag={tag} field={field} />
          </>
        );
      };

      // 计算字段
      const renderCalcField = () => {
        return (
          <>
            <Form.Item
              label={
                <div className={styles.labelWrapper}>
                  <span>
                    {getCalcFieldDisplayName(field)}
                    {field.fieldConfig?.formFieldConfig?.fieldOtherName && (
                      <Tooltip title={`原名称：${field.fieldConfig?.fieldName}`}>
                        <InfoCircleOutlined style={{ marginLeft: 5, color: '#4D7BF6' }} />
                      </Tooltip>
                    )}
                  </span>
                  <Tag color="orange" style={{ margin: 0 }}>
                    输出
                  </Tag>
                </div>
              }
            >
              <span>回显数据</span>
            </Form.Item>
          </>
        );
      };

      const selected = selectedState.type === SelectedStateType.baseGroupItem && getMetaTagUniqueId(field) === selectedState.formItemId;
      const widgetWith = field.sourceType === SourceType.Tag ? field.widgetWith : field.fieldConfig?.formFieldConfig?.widgetWith;

      return (
        <SortableItem key={uniqueId} id={uniqueId} widgetWith={widgetWith}>
          <div
            className={classNames(styles.formItemWrapper, {
              [styles.active]: selected,
            })}
            onClick={async (e) => {
              e.stopPropagation();
              await validateFormFields(getPropertyConfigRef());
              setSelectedState({
                type: SelectedStateType.baseGroupItem,
                formItemId: getMetaTagUniqueId(field),
              });
            }}
          >
            {field.sourceType === SourceType.Tag && renderTag()}
            {field.sourceType === SourceType.Field && renderCalcField()}
            {selected && (
              <DeleteOutlined
                className={styles.deleteIcon}
                onClick={() => {
                  // onDelete(getMetaTagUniqueId(field))
                  setConfiguredFields(configuredFields.filter((f) => getMetaTagUniqueId(f) !== getMetaTagUniqueId(field)));
                  setSelectedState({
                    type: SelectedStateType.baseGroup,
                  });
                }}
              />
            )}
          </div>
        </SortableItem>
      );
    });
  };

  // 是否全部是输出
  const isAllOutput = useMemo(() => {
    if (configuredFields?.some((field) => field.sourceType === SourceType.Tag && !field.readonly)) {
      return false;
    }
    if (combineTagConfig?.fields?.some((field) => field.sourceType === SourceType.Tag && !field.readonly)) {
      return false;
    }
    return true;
  }, [configuredFields, combineTagConfig]);

  // 切换输入输出
  const toggleIO = () => {
    setConfiguredFields(
      produce(configuredFields, (draft) => {
        draft.forEach((field) => {
          const tag = tagsMap[getMetaTagUniqueId(field)];
          if (field.sourceType === SourceType.Tag && tag?.type !== MetaTagType.System) {
            field.readonly = !isAllOutput;
          }
        });
      }),
    );

    setCombineTagConfig(
      produce(combineTagConfig, (draft) => {
        draft?.fields?.forEach((field) => {
          const tag = tagsMap[getMetaTagUniqueId(field)];
          if (field.sourceType === SourceType.Tag && tag?.type !== MetaTagType.System) {
            field.readonly = !isAllOutput;
          }
        });
      }),
    );
  };

  const handlePreviewClick = async () => {
    let formVersionId;
    if (groupFlag) {
      await handleAggregationSubUpdate(currSubDataUnitId);
      formVersionId = await handleAggregationUpdate();
    } else {
      formVersionId = await handleUpdate(true);
    }

    openAppApply(bridge, {
      state: {
        isPreview: true,
        app: {
          ...appInfo,
          appType,
          formVersionId,
        },
        post,
      },
    });
  };

  return (
    <div className={styles.page}>
      <div className={styles.header}>
        <span className={styles.headerLeft}>
          <SubFormTabs />
        </span>
        <span className={styles.headerRight}>
          <Button type="link" icon={<EyeOutlined />} style={{ padding: 0 }} onClick={handlePreviewClick}>
            预览
          </Button>
        </span>
      </div>

      {/* 能拖入的最大容器 */}
      <Dustbin className={styles.dropBox} onDrop={() => ({ name: DropResultName.Container })} accept={[DraggableType.Table]}>
        {/* 基础分组 */}
        <Dustbin
          className={classNames(styles.baseGroup, {
            [styles.active]: selectedState.type === SelectedStateType.baseGroup,
          })}
          onDrop={() => ({ name: DropResultName.BaseGroup })}
          accept={
            [SelectedStateType.baseGroup, SelectedStateType.baseGroupItem, SelectedStateType.combineTag, SelectedStateType.combineTagItem].includes(
              selectedState.type,
            )
              ? [DraggableType.Tag, DraggableType.Table]
              : []
          }
          onClick={async () => {
            await validateFormFields(getPropertyConfigRef());
            setSelectedState({
              type: SelectedStateType.baseGroup,
            });
          }}
        >
          <div className={styles.baseGroupTitle}> {baseGroupName || '基础分组'} </div>
          <span style={{ position: 'absolute', right: 4, top: 4, color: '#4d7bf6', display: 'flex', gap: 8 }}>
            <RetweetOutlined onClick={toggleIO} />
            <DeleteOutlined
              disabled={!configuredFields?.length && isEmpty(combineTagConfig)}
              onClick={() => {
                Modal.confirm({
                  title: '是否确定清空当前画布全部内容？',
                  onOk: () => {
                    setConfiguredFields([]);
                    setCombineTagConfig(undefined);
                  },
                });
              }}
            />
          </span>

          {/* 表单区域 */}
          {configuredFields?.length > 0 && (
            <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <div className={styles.formWrapper}>
                <Form form={form} layout="vertical" colon={false}>
                  <SortableContext items={configuredFields.map((field) => getMetaTagUniqueId(field))} strategy={rectSortingStrategy}>
                    <div className={styles.formItemList}>{renderItems()}</div>
                  </SortableContext>
                </Form>
              </div>
            </DndContext>
          )}

          {/* 组合标签 */}
          {combineTagConfig && (
            <Dustbin
              onDrop={() => ({ name: DropResultName.CombineTag })}
              accept={
                [
                  SelectedStateType.baseGroup,
                  SelectedStateType.baseGroupItem,
                  SelectedStateType.combineTag,
                  SelectedStateType.combineTagItem,
                ].includes(selectedState.type)
                  ? [DraggableType.Tag]
                  : []
              }
            >
              <div className={styles.combineTagWrapper}>
                <DataTable
                  type="combineTag"
                  selected={selectedState.type === SelectedStateType.combineTag}
                  onClick={async () => {
                    await validateFormFields(getPropertyConfigRef());
                    setSelectedState({
                      type: SelectedStateType.combineTag,
                    });
                  }}
                  onDelete={() => {
                    setCombineTagConfig(undefined);
                    setSelectedState({
                      type: SelectedStateType.baseGroup,
                    });
                  }}
                  onDeleteColumn={(tag) => {
                    setCombineTagConfig(
                      produce(combineTagConfig, (draft) => {
                        draft.fields = draft.fields.filter((f) => getMetaTagUniqueId(f) !== getMetaTagUniqueId(tag));
                      }),
                    );
                  }}
                />
              </div>
            </Dustbin>
          )}

          {configuredFields?.length === 0 && !combineTagConfig && (
            <div className={styles.emptyWrapper}>
              <img width={180} src={require('@/assets/app-designer/drag-tip.png')} />
              <div style={{ marginTop: 20, textAlign: 'center' }}>
                <div>先右侧选择数据单元，再拖拽设计组件里</div>
                <div>的标签、字段到这里开始使用吧！</div>
              </div>
            </div>
          )}
        </Dustbin>

        {/* 数据表格 */}
        <div className={styles.dataTableList}>
          {tableList?.map((table) => {
            return (
              <Dustbin
                key={table.id}
                onDrop={() => ({ name: DropResultName.DataTable, id: table.id })}
                accept={
                  table.id === selectedState.tableId && [SelectedStateType.table, SelectedStateType.tableItem].includes(selectedState.type)
                    ? [DraggableType.Tag]
                    : []
                }
              >
                <div className={styles.dataTableWrapper}>
                  <DataTable
                    type="table"
                    tableId={table.id}
                    selected={selectedState.type === SelectedStateType.table && selectedState.tableId === table.id}
                    onClick={async () => {
                      await validateFormFields(getPropertyConfigRef());
                      setSelectedState({
                        type: SelectedStateType.table,
                        tableId: table.id,
                      });
                    }}
                    onDelete={() => {
                      setTableList(tableList.filter((t) => t.id !== table.id));
                      setSelectedState({
                        type: null,
                      });
                    }}
                    onDeleteColumn={(tag) => {
                      setTableList(
                        produce(tableList, (draft) => {
                          const t = draft.find((t) => t.id === table.id);
                          t.fields = t.fields.filter((f) => getMetaTagUniqueId(f) !== getMetaTagUniqueId(tag));
                        }),
                      );
                    }}
                  />
                </div>
              </Dustbin>
            );
          })}
        </div>
      </Dustbin>

      <div
        className={classNames(styles.footer, {
          [styles.active]: selectedState.type === SelectedStateType.buttonList,
        })}
        onClick={() => {
          setSelectedState({
            type: SelectedStateType.buttonList,
          });
        }}
      >
        <ButtonConfigs buttonConfigs={buttonConfigs} />
      </div>
    </div>
  );
};

export default forwardRef(FormConfig);
