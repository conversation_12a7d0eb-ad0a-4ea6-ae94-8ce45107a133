import { AmountUnit, AreaUnit, LengthUnit, UnitType, VolumeUnit, WeightUnit } from '@/const/metadata';
import { UnitTypes } from '@/types/metadata';

/**
 * 统一单位转换
 * @param type 单位类型
 * @param originUnit 原单位
 * @param targetUnit 目标单位
 * @param value 原值
 * @param precision 小数位数
 * @returns 转换后的新值
 */
export const unitTransform = (type: UnitType, originUnit: UnitTypes, targetUnit: UnitTypes, value: number, precision?: number) => {
  if (originUnit === targetUnit) return value;
  let unitMap = null;
  switch (type) {
    case UnitType.weight:
      // 以 WeightUnit.g 为基准
      unitMap = {
        [WeightUnit.ton]: 1000000,
        [WeightUnit.kg]: 1000,
        [WeightUnit.g]: 1,
        [WeightUnit.jin]: 500,
        [WeightUnit.liang]: 50,
      };
      break;
    case UnitType.length:
      // 以 LengthUnit.mm 为基准
      unitMap = {
        [LengthUnit.km]: 1000000,
        [LengthUnit.mile]: 500000,
        [LengthUnit.m]: 1000,
        [LengthUnit.dm]: 100,
        [LengthUnit.cm]: 10,
        [LengthUnit.mm]: 1,
        [LengthUnit.inch]: 33.3,
      };
      break;
    case UnitType.area:
      // 以 AreaUnit.m2 为基准
      unitMap = {
        [AreaUnit.km2]: 1000000,
        [AreaUnit.m2]: 1,
        [AreaUnit.acres]: 666.67,
        [AreaUnit.hectares]: 10000,
      };
      break;
    case UnitType.volume:
      // 以 VolumeUnit.m2 为基准
      unitMap = {
        [VolumeUnit.m3]: 1000000,
        [VolumeUnit.l]: 1000,
        [VolumeUnit.ml]: 1,
      };
      break;
    case UnitType.amount:
      // 以 AmountUnit.yuan 为基准
      unitMap = {
        [AmountUnit.millionYuan]: 1000000,
        [AmountUnit.tenThousandYuan]: 10000,
        [AmountUnit.yuan]: 1,
      };
      break;
  }
  let newValue = value && typeof value === 'string' ? Number(value) : value;
  if (typeof newValue === 'number') {
    newValue = newValue * unitMap[originUnit];
    newValue = newValue / unitMap[targetUnit];
    const originValuePrecision = (value.toString().split('.')[1] || []).length;
    if (newValue.toString().includes('.')) {
      if (precision || originValuePrecision) {
        newValue = Number(
          (Math.round(newValue * 10 * (precision || originValuePrecision)) / (10 * (precision || originValuePrecision))).toFixed(
            precision || originValuePrecision,
          ),
        );
      } else {
        newValue = Math.round(newValue);
      }
    }
    return newValue;
  }
  return value;
};
