import { Tabs } from '@gwy/components-web';
import { useContext } from 'react';
import { AppDesignerContext } from '../../context';
import styles from './index.less';

const SubFormTabs = () => {
  const {
    configuredDataUnits,
    aggregationMainDataUnits,
    aggregationMainDataUnitId,
    aggregationSubDataUnits,
    currSubDataUnitId,
    setCurrSubDataUnitId,
    handleAggregationSubUpdate,
  } = useContext(AppDesignerContext);

  const dataUnit = configuredDataUnits?.[0];

  const items = [
    aggregationMainDataUnitId && {
      label: aggregationMainDataUnits?.find((item) => item.dataUnitId === aggregationMainDataUnitId)?.name,
      key: String(aggregationMainDataUnitId),
    },
    ...(dataUnit?.extConfig?.subDataUnitIds?.map((id) => {
      const item = aggregationSubDataUnits.find((item) => item.dataUnitId === id);
      return {
        label: item?.name,
        key: String(item?.dataUnitId),
      };
    }) || []),
  ].filter(Boolean);

  return (
    <div className={styles.container}>
      <Tabs
        type="card"
        size="small"
        tabBarGutter={16}
        key={items.map((item) => item.key).join(',')}
        items={items}
        activeKey={String(currSubDataUnitId)}
        onChange={async (key) => {
          await handleAggregationSubUpdate(currSubDataUnitId);
          setCurrSubDataUnitId(Number(key));
        }}
      />
    </div>
  );
};

export default SubFormTabs;
