import { TextDTOKey } from '@/components/metadata-dialog/metadata-config/text-config';
import { EnumType } from '@/const/metadata';
import { datasourceAPI } from '@/services';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Form, Radio } from '@gwy/components-web';
import { useEffect, useState } from 'react';
import { ValueFormat } from '../../object/const';
import CustomTreeSelect from './custom-tree-select';

type Props = {
  tag?: DataUnitTagVO;
  field?: FormField;
};

const TextEnumPropertySetting = ({ tag, field }: Props) => {
  const form = Form.useFormInstance();

  const { enumList, dictCode, enumType } = tag.tagMetaDataConfig?.metaDataTextDTO || {};

  const [loading, setLoading] = useState(false);

  const [treeData, setTreeData] = useState([]);

  useEffect(() => {
    if (enumType === EnumType.general && dictCode) {
      setLoading(true);
      try {
        datasourceAPI.getDictTreeDataByCode(dictCode).then((res) => {
          setTreeData(res);
        });
      } catch (error) {}
      setLoading(false);
    } else {
      setTreeData(enumList);
    }
    form.setFieldsValue(field.config);
  }, []);

  return (
    <div>
      <Form.Item label="选项类型" name={['multipleChoice']}>
        <Radio.Group>
          <Radio value={true}>多选</Radio>
          <Radio value={false}>单选</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item label="选项设置" name={['appoint']}>
        <Radio.Group>
          <Radio value={true}>指定选项</Radio>
          <Radio value={false}>排除选项</Radio>
        </Radio.Group>
      </Form.Item>
      <Form.Item name={[TextDTOKey, 'enumList']}>
        <CustomTreeSelect valueFormat={ValueFormat.ObjectArray} enumType={enumType} loading={loading} treeData={treeData} multiple treeCheckable />
      </Form.Item>
    </div>
  );
};

export default TextEnumPropertySetting;
