import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Form } from '@gwy/components-web';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';
import CodeMeta from './index';
import { validator } from './validator';

export type CodeMetaFormProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  label?: React.ReactNode;
  prefix?: any[];
  isPreview?: boolean;
};

const CodeMetaForm = memo(({ useIn, tag, field, label, prefix, isPreview }: CodeMetaFormProps) => {
  const { required, readonly, config } = field || {};
  const { metaDataCodeDTO } = config || {};

  const rules = useMemo(
    () =>
      !readonly && [UseIn.App].includes(useIn)
        ? [
            {
              validator: (rule, value) => validator(rule, value, metaDataCodeDTO, required),
            },
          ]
        : undefined,
    [required, metaDataCodeDTO, readonly],
  );

  return (
    <Form.Item label={label} name={[...(prefix || []), getMetaTagUniqueId(field), 'value']} rules={rules} required={required && !readonly}>
      <CodeMeta useIn={useIn} tag={tag} field={field} isPreview={isPreview} />
    </Form.Item>
  );
});

export default CodeMetaForm;
