import TriggerConditionSetting from '@/components/data-query-setting/trigger-condition-setting';
import { ResultType } from '@/types/calc-field';
import { DeleteOutlined, EditOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Form, Switch } from '@gwy/components-web';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { FieldCreateContext } from '../../../field-create-modal/context';
import FormItemCalc from '../form-item-calc';
import styles from './index.less';

interface IProps {
  disabled?: boolean;
}

const FormItemCondition = (props: IProps) => {
  const { disabled } = props;
  const { configuredDataUnits, allDataUnitsWithTags, fieldList, fieldType } = useContext(FieldCreateContext);
  const form = Form.useFormInstance();
  const calculationCalcsWatch = Form.useWatch('calculationCalcs', form);
  const calculationCalcs = useMemo<any[]>(() => calculationCalcsWatch || [], [calculationCalcsWatch]);
  const [canAddGroup, setCanAddGroup] = useState(false);
  const [triggerSettingModal, setTriggerSettingModal] = useState({ open: false, data: null });

  // 仅在首次加载且没有值时，初始化一个默认规则组
  const hasInitializedRef = useRef(false);
  useEffect(() => {
    if (hasInitializedRef.current) return;
    const current = form.getFieldValue('calculationCalcs');
    if (!current || current.length === 0) {
      form.setFieldValue('calculationCalcs', [
        {
          triggerCondition: false,
          conditionGroups: [],
          extConfig: null,
          resultType: ResultType.CALCULATION_MODE,
        },
      ]);
    }
    hasInitializedRef.current = true;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 当某一个规则组触发条件为 false 时，该规则组之后的所有规则组都删除
  useEffect(() => {
    const triggerConditionUncheck = calculationCalcs.findIndex((item) => item.triggerCondition === false);
    if (triggerConditionUncheck !== -1) {
      const current: any[] = form.getFieldValue('calculationCalcs') || [];
      form.setFieldValue('calculationCalcs', current.slice(0, triggerConditionUncheck + 1));
      setCanAddGroup(false);
    } else {
      setCanAddGroup(true);
    }
  }, [calculationCalcs, form]);

  const handleOpenGroupConfig = (idx: number) => {
    const { conditionGroups, extConfig } = calculationCalcs[idx] || {};

    setTriggerSettingModal({
      open: true,
      data: {
        currentIdx: idx,
        conditionGroups,
        extConfig,
      },
    });
  };

  const handleTriggerSettingOk = (data: any) => {
    const { currentIdx } = triggerSettingModal.data;
    const current = form.getFieldValue('calculationCalcs') || [];
    current[currentIdx] = {
      ...current[currentIdx],
      ...data,
    };
    form.setFieldValue('calculationCalcs', current);
    closeTriggerSettingModal();
  };

  const closeTriggerSettingModal = () => {
    setTriggerSettingModal({
      open: false,
      data: null,
    });
  };

  return (
    <>
      <Form.List
        name="calculationCalcs"
        rules={[
          {
            validator: async (_, names) => {
              if (!names || names.length === 0) {
                return Promise.reject(new Error('请至少配置一个阶梯公式'));
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        {(fields, { add, remove }) => {
          return (
            <>
              {fields.map(({ key, name, ...restField }, idx) => {
                const { triggerCondition = true, calculationModes, conditionGroups } = calculationCalcs[key] || {};
                return (
                  <div className={styles.itemWrapper} key={key}>
                    <div className={styles.header}>
                      <div className={styles.left}>规则组</div>
                      <div className={styles.right}>
                        <Form.Item
                          {...restField}
                          name={[name, 'triggerCondition']}
                          label="触发条件"
                          initialValue={triggerCondition}
                          valuePropName="checked"
                        >
                          <Switch disabled={disabled} />
                        </Form.Item>
                        {triggerCondition && (
                          <Button color="primary" variant="link" onClick={() => handleOpenGroupConfig(idx)} icon={<EditOutlined />}>
                            {conditionGroups?.length > 0 ? `${conditionGroups?.length}个条件` : '配置条件'}
                          </Button>
                        )}
                        {key !== 0 && <DeleteOutlined style={{ marginLeft: 10, fontSize: 16, cursor: 'pointer' }} onClick={() => remove(idx)} />}
                      </div>
                    </div>
                    <div className={styles.content}>
                      <FormItemCalc subPrefixName={[name]} calculationModes={calculationModes} disabled={disabled} />
                    </div>
                  </div>
                );
              })}
              {canAddGroup && !disabled && (
                <div className={styles.addBtnWrapper}>
                  <Button color="primary" variant="link" onClick={() => add({})} icon={<PlusCircleOutlined style={{ fontSize: 16 }} />}>
                    添加规则组
                  </Button>
                </div>
              )}
            </>
          );
        }}
      </Form.List>
      {triggerSettingModal.open && (
        <TriggerConditionSetting
          disabled={disabled}
          fieldType={fieldType}
          conditionGroups={triggerSettingModal.data?.conditionGroups}
          extConfig={triggerSettingModal.data?.extConfig}
          allDataUnitsWithTags={allDataUnitsWithTags}
          configuredDataUnits={configuredDataUnits}
          calcFields={fieldList}
          onOk={handleTriggerSettingOk}
          onCancel={closeTriggerSettingModal}
        />
      )}
    </>
  );
};

export default FormItemCondition;
