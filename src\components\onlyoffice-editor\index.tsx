import { OnlyofficeDocumentServerUrl } from '@/config';
import { onlyofficeAPI } from '@/services';
import { Empty, message } from '@gwy/components-web';
import { DocumentEditor } from '@onlyoffice/document-editor-react';
import { uniqueId } from 'lodash-es';
import { forwardRef, memo, PropsWithoutRef, ReactElement, RefAttributes, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { ControlType, createOnlyofficeControlTag, IConfig, MODE_TYPE } from './const';

const uid = 1;

interface IProps {
  onDocAndPluginReady?: (isSuccess: boolean) => void;
}
export interface IEditorRef {
  getEditor: () => any;
  setFileUrl: (p: { fileUrl: string; mode?: MODE_TYPE; disableCache?: boolean }) => Promise<void>;
  getFileUrl: () => Promise<string>;
  updateDocEditor: (e, config: IConfig, callback?: (controlTag: any) => void) => void;
  updateDocText: (text: string) => void;
  reloadWord: () => void;
}
const Editor = memo(
  forwardRef<IEditorRef, IProps>((props, ref) => {
    const { onDocAndPluginReady } = props;
    const editorRef = useRef({
      fileKey: '',
      fileUrl: '',
      editorId: uniqueId('onlyoffice-'),
      fileName: '',
      onDocAndPluginReady,
    });
    editorRef.current.onDocAndPluginReady = onDocAndPluginReady;
    const [editorConfig, setEditorConfig] = useState(null);
    const onReady = useMemo(() => {
      let count = 0;
      let timer = null;
      return () => {
        const { onDocAndPluginReady } = editorRef.current;
        if (++count === 2) {
          count = 0;
          clearTimeout(timer);
          setTimeout(() => {
            typeof onDocAndPluginReady === 'function' && onDocAndPluginReady(true);
          }, 300);
        } else {
          timer = setTimeout(() => {
            typeof onDocAndPluginReady === 'function' && onDocAndPluginReady(false);
          }, 2000);
        }
      };
    }, []);

    const getEditor = () => {
      return window?.DocEditor?.instances![editorRef.current.editorId];
    };

    const setFileUrl = async ({
      fileUrl,
      disableCache = true,
      mode = MODE_TYPE.EDIT,
    }: {
      fileUrl: string;
      disableCache?: boolean;
      mode?: MODE_TYPE;
    }) => {
      const { filename: fileName, key } = await onlyofficeAPI.uploadFile({
        fileUrl,
        uid,
        disableCache,
      });
      const { model } = await onlyofficeAPI.getConfig({
        fileName,
        ulang: 'zh',
        uid,
      });
      if (mode === MODE_TYPE.VIEW && model) {
        model.editorConfig.mode = 'view';
      }
      model.events = {
        ...(model.events || {}),
        onDocumentReady: () => {
          onReady();
        },
        onPluginsReady: () => {
          onReady();
        },
      };
      setEditorConfig(model);
      editorRef.current = { ...editorRef.current, fileKey: key, fileUrl, fileName };
    };

    const getFileUrl = (): Promise<string> => {
      return new Promise((resolve, reject) => {
        const saveKey = editorConfig?.document?.key;
        if (!saveKey) {
          reject(new Error('saveKey 不能为空'));
          return;
        }
        const { fileKey } = editorRef.current;
        if (!fileKey) {
          message.error('fileKey 不能为空');
          reject(new Error('fileKey 不能为空'));
          return;
        }
        (async () => {
          try {
            // 如果用户按【ctrl + s】保存，则ctrlSFileUrl有值
            const { fileUrl: ctrlSFileUrl } = await onlyofficeAPI.forceSave({ fileKey: saveKey, key: fileKey });
            // 触发轮询，根据key获取缓存url
            let fileUrl = await onlyofficeAPI.pollGetFileCacheUrl(fileKey, ctrlSFileUrl || editorRef.current.fileUrl);
            fileUrl = fileUrl || ctrlSFileUrl;
            editorRef.current.fileUrl = fileUrl;
            resolve(fileUrl);
          } catch (e) {
            const { desc } = e;
            reject(new Error(desc || '保存失败'));
          }
        })();
      });
    };

    // 更新编辑器，添加一个控件
    const updateDocEditor = (e, config, callback) => {
      const { clientX, clientY } = e;
      const _docEditory = getEditor();
      const controlTag = createOnlyofficeControlTag(config);
      const controlPara: any[] = [];
      controlPara.push(2); // 设定文本类型
      controlPara.push(controlTag);
      const postMessage: {
        controlType: string;
        x: any;
        y: any;
        controlPara: any[];
      } = {
        controlType: ControlType.TEXT,
        x: clientX, // 鼠标坐标
        y: clientY,
        controlPara, // 对应组件参数
      };
      // 更新文档内容
      _docEditory.serviceCommand('AddAnyControl', postMessage);
      // 抛出targetId 去重
      callback && callback(controlTag);
    };

    const updateDocText = (text) => {
      const _docEditory = getEditor();
      _docEditory.serviceCommand('executePluginMethod', {
        params: text,
        command: 'InputText',
      });
    };

    const reloadWord = () => {
      setFileUrl({ fileUrl: editorRef.current.fileUrl });
    };

    useImperativeHandle(
      ref,
      () => ({
        getEditor,
        setFileUrl,
        getFileUrl,
        updateDocEditor,
        updateDocText,
        reloadWord,
      }),
      [editorConfig, onReady],
    );

    console.log('OnlyofficeDocumentServerUrl', OnlyofficeDocumentServerUrl);

    return editorConfig ? (
      <div style={{ height: '100%', width: '100%', overflow: 'hidden' }}>
        {/* 容器节点高度 100% + 25px，解决编辑器内底部statusbar位置更改导致的留白 */}
        <div style={{ height: 'calc(100% + 25px)' }}>
          <DocumentEditor id={editorRef.current.editorId} documentServerUrl={OnlyofficeDocumentServerUrl as string} config={editorConfig} />
        </div>
      </div>
    ) : (
      <Empty style={{ marginTop: '50px' }} description="请先导入文件" image={Empty.PRESENTED_IMAGE_SIMPLE} />
    );
  }),
);

export default Editor as (props: PropsWithoutRef<IProps> & RefAttributes<IEditorRef>) => ReactElement | null;

export { MODE_TYPE };
