.container {
}

.configItem {
  position: relative;
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px dashed #c9cdd4;
  background-color: #fff;
  font-size: 12px;
  margin-bottom: 10px;

  &.active {
    border: 1px solid #4d7bf6;
  }

  .groupHeader {
    font-weight: bold;
    margin-bottom: 4px;
  }

  .elementItem {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 4px;

    .elementType {
      flex-shrink: 0;
    }

    .elementValue {
      flex-grow: 1;
      display: flex;
      align-items: center;
      gap: 10px;

      .elementLength {
        flex-shrink: 0;
      }
    }
  }

  .checkedFlag {
    position: absolute;
    right: 0;
    bottom: 0;
  }
}
