import React from 'react';
import styles from './index.less';

type Props = {
  title: string;
  desc?: React.ReactNode;
  rightExtra?: React.ReactNode;
};

const SectionHeader = ({ title, desc, rightExtra }: Props) => {
  return (
    <div className={styles.container}>
      <span className={styles.left}>
        <span className={styles.title}>{title}</span>
        <span className={styles.desc}>{desc}</span>
      </span>
      {rightExtra && <span className={styles.rightExtra}>{rightExtra}</span>}
    </div>
  );
};

export default SectionHeader;
