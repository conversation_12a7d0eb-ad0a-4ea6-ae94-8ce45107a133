.hasValue {
  color: #4d7bf6;
  border: 1px solid #4d7bf6;
  background-color: #e8f3ff;
}

.disabled {
  color: #999;
  border: 1px solid #d9d9d9;
  background-color: #f5f5f5;
}

.title {
  position: relative;
  display: inline-block;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.warnIcon {
  margin-left: 2px;
  vertical-align: 4px;
  color: #ef8332;
}

.modalWrapper {
  :global {
    .ant-modal-content {
      border-radius: 6px;
    }
  }

  .tabsHeader {
    border-radius: 6px 6px 0 0;
  }

  .container {
    width: 100%;
    height: calc(100% - 36px);
    overflow: auto;
    padding: 16px;
  }
}
