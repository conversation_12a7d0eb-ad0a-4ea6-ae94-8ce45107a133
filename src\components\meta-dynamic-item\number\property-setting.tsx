import { UseIn } from '@/components/metadata-dialog/metadata-config/const';
import NumberConfig from '@/components/metadata-dialog/metadata-config/number-config';
import { FormField } from '@/types/form-field';
import { MetaDataNumberDTO } from '@/types/metadata';
import { DataUnitTagVO } from '@/types/tag';
import { Form, InputNumber } from '@gwy/components-web';
import { useCallback, useEffect } from 'react';
import { validator } from './validator';

type Props = {
  field: FormField;
  tag: DataUnitTagVO;
};

const NumberPropertySetting = ({ field, tag }: Props) => {
  const form = Form.useFormInstance();
  const { metaDataNumberDTO } = tag.tagMetaDataConfig;

  useEffect(() => {
    form.setFieldsValue(field.config);
  }, []);

  const getValidatorRules = useCallback(() => {
    return [
      {
        validator: (rule, value) => validator(rule, value, metaDataNumberDTO as Required<MetaDataNumberDTO>),
      },
    ];
  }, [metaDataNumberDTO]);

  return (
    <div>
      <Form.Item
        label="小数位数"
        name={['decimalPlaces']}
        rules={[
          { required: true, message: '请输入' },
          { type: 'number', min: 0, max: 6, message: '请输入0-6的正整数' },
        ]}
      >
        <InputNumber precision={0} addonAfter="位" style={{ width: '100%' }} />
      </Form.Item>

      <NumberConfig form={form} getValidatorRules={getValidatorRules} useIn={UseIn.Designer} />
    </div>
  );
};

export default NumberPropertySetting;
