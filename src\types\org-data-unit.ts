import { DataUnitTagVO } from './tag';

/**
 * OrgDataUnitTagListVO
 */
export type OrgDataUnitTagListVO = {
  /**
   * 数据分类id
   */
  categoryId?: number;
  /**
   * 数据单元分类名称
   */
  categoryName?: string;
  /**
   * 数据单元id，版本生效后才有该值
   */
  dataUnitId?: number;
  /**
   * 数据单元版本id
   */
  dataUnitVersionId?: number;
  /**
   * 数据单元名称
   */
  name?: string;
  /**
   * 标签列表
   */
  tagList?: OrgDataUnitTagVO[];
  [property: string]: any;
};

/**
 * OrgDataUnitTagVO
 */
export type OrgDataUnitTagVO = DataUnitTagVO & {
  /**
   * 编码数元是否无配置项（数元类型-编码时有意义）
   */
  noMetaDataCodeConfigFlag?: boolean;

  $$dataUnit?: OrgDataUnitTagListVO;

  [property: string]: any;
};

/**
 * 操作类型，1=新增，2=修改
 */
export enum OperateType {
  Insert = 'INSERT',
  Update = 'UPDATE',
}
