import MetadataList, { DataSourceTypes } from '@/pages/platform/metadata-manage/components/metadata-list';
import { Spin } from '@gwy/components-web';
import { useState } from 'react';
import { Outlet } from 'umi';
import styles from './index.less';

export default () => {
  const [loading, setLoading] = useState(false);

  return (
    <Spin spinning={loading} wrapperClassName={styles.spin}>
      <div className={styles.metadataContainer}>
        <div className={styles.body}>
          <div className={styles.wrapper}>
            <MetadataList canEdit={false} dataSourceType={DataSourceTypes.GENERAL} loading={loading} setLoading={setLoading} />
          </div>
        </div>
        <Outlet />
      </div>
    </Spin>
  );
};
