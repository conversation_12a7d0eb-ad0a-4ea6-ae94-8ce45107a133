import { Form, FormInstance, Select } from '@gwy/components-web';
import { useMemo } from 'react';
import { FILTER_VALUE_TYPE } from '../../filter-const';

interface Iprops {
  form: FormInstance;
  disabled?: boolean;
  prefix: any[];
  parenntPrefix: any[];
}

const allOptions = [
  {
    label: '已处理',
    value: 'notnull',
  },
  {
    label: '未处理',
    value: 'isnull',
  },
  {
    label: '当前岗位未处理',
    value: 'post_undo',
  },
  {
    label: '当前岗位已处理',
    value: 'post_done',
  },
  {
    label: '部分处理',
    value: 'part_done',
  },
  {
    label: '当前岗位部分处理',
    value: 'post_part_done',
  },
];

const RenderSpecialItem = (props: Iprops) => {
  const { form, prefix, parenntPrefix, disabled = false } = props;
  const sameGroupQueryType = Form.useWatch('sameGroupQueryType', form);

  const options = useMemo(() => {
    if (sameGroupQueryType === 'all') {
      return allOptions;
    }
    return allOptions.filter((n) => !['part_done', 'post_part_done'].includes(n.value));
  }, [sameGroupQueryType]);

  return (
    <>
      <Form.Item name={[...prefix, 'valueType']} style={{ width: '100px' }}>
        <Select
          disabled={disabled}
          placeholder={'请选择'}
          options={[
            {
              label: '固定值',
              value: FILTER_VALUE_TYPE.CONST,
            },
          ]}
          onChange={() => {
            form.setFieldValue([...parenntPrefix, 'value'], undefined);
          }}
        />
      </Form.Item>
      <Form.Item
        style={{ flex: '1', marginRight: '8px', marginBottom: 0 }}
        rules={[{ required: true, message: '请选择条件' }]}
        name={[...prefix, 'value']}
      >
        <Select options={options} placeholder="请选择范围" style={{ width: '100%' }} disabled={disabled} />
      </Form.Item>
    </>
  );
};

export default RenderSpecialItem;
