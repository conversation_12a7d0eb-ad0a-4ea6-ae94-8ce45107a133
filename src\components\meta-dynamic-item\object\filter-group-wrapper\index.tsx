import DataQuerySetting from '@/components/data-query-setting';
import { EditOutlined } from '@ant-design/icons';
import { Button } from '@gwy/components-web';
import { useState } from 'react';

type Props = {
  tags?: any[];
  canRelateTags?: any[];
  value?: any;
  onChange?: (value) => void;
  calcFields?: any[];
};

const FilterGroupWrapper = ({ tags, canRelateTags, value, onChange, calcFields }: Props) => {
  const [openFilter, setOpenFilter] = useState({
    open: false,
  });

  return (
    <div>
      <Button
        style={{ padding: 0 }}
        type="link"
        icon={<EditOutlined />}
        onClick={() =>
          setOpenFilter((prev) => ({
            ...prev,
            open: true,
          }))
        }
      >
        {value?.length > 0 ? `${value?.length}个条件` : '配置条件'}
      </Button>

      {openFilter.open && (
        <DataQuerySetting
          onlyConditionGroups={true}
          conditionGroups={value}
          tags={tags}
          canRelateTags={canRelateTags}
          calcFields={calcFields}
          onOk={(values) => {
            setOpenFilter({
              open: false,
            });
            onChange?.(values?.conditionGroups);
          }}
          onCancel={() => {
            setOpenFilter({
              open: false,
            });
          }}
        />
      )}
    </div>
  );
};

export default FilterGroupWrapper;
