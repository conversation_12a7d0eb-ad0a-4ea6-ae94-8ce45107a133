import { MetaDataType, TextInputType } from '@/const/metadata';
import { FormField, InputStyle } from '@/types/form-field';
import { MetaDataNumberDTO } from '@/types/metadata';
import { DataUnitTagVO } from '@/types/tag';
import { genUuid } from '@/utils';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { validator as codeValidator } from '../code/validator';
import { validator as numberValidator } from '../number/validator';
import { validator as textValidator } from '../text/validator';
import { validator as unitValidator } from '../unit/validator';

export const getDynamicItemFormRules = (tag: DataUnitTagVO, field: FormField, formater?: (value) => any) => {
  const { type, metaDataNumberDTO: numberDTO, metaDataTextDTO: textDTO } = tag?.tagMetaDataConfig || {};
  const { required, config } = field || {};
  const { metaDataCodeDTO, metaDataNumberDTO, metaDataTextDTO, inputStyle, metaDataUnitDTO } = config || {};

  switch (type) {
    case MetaDataType.Text: {
      const { inputType: metaInputType } = textDTO || {};
      const { inputType, allowedCharacters } = metaDataTextDTO || {};
      return [
        {
          required: required,
          message: '请填写必填项',
        },
        metaInputType === TextInputType.manual &&
          (!inputStyle || inputStyle === InputStyle.Input) && {
            validator: (rule, value) => textValidator(rule, formater ? formater(value) : value, allowedCharacters),
          },
      ].filter(Boolean);
    }
    case MetaDataType.Number:
      return [
        {
          required: required,
          message: '请填写必填项',
        },
        {
          validator: async (rule, value) => {
            await numberValidator(rule, formater ? formater(value) : value, metaDataNumberDTO as Required<MetaDataNumberDTO>);
            await numberValidator(rule, formater ? formater(value) : value, numberDTO as Required<MetaDataNumberDTO>);
          },
        },
      ];
    case MetaDataType.Code:
      return [
        {
          validator: (rule, value) => codeValidator(rule, formater ? formater(value) : value, metaDataCodeDTO, required),
        },
      ];
    case MetaDataType.Unit: {
      const { min, max, unit, rangeType, type } = metaDataUnitDTO || {};
      return [
        {
          validator: (rule, value) => unitValidator(rule, formater ? formater(value) : value, required, { min, max, unit, type, rangeType }),
        },
      ];
    }
    default:
      return [
        {
          required: required,
          message: '请填写必填项',
        },
      ];
  }
};

/**
 * 处理表格数据和默认值的结合逻辑
 *
 * @param datas 源数据
 * @param configFields 标签的配置
 * @param defaultData 标签默认值
 */
export const handleTableData = (datas, configFields, defaultData?: any) => {
  return datas.map((item) =>
    Object.keys(item).reduce(
      (pre, key) => {
        const field = configFields.find((field) => getMetaTagUniqueId(field) === key);
        let value = item[key];
        if (field && !field.readonly && (!value || !value.data)) {
          value = defaultData?.[key];
        }
        pre[key] = value;
        return pre;
      },
      { $$id: item._id || genUuid() },
    ),
  );
};
