import AddressPropertySetting from '@/components/meta-dynamic-item/address/property-setting';
import CodePropertySetting from '@/components/meta-dynamic-item/code/property-setting';
import FilePropertySetting from '@/components/meta-dynamic-item/file/property-setting';
import NumberPropertySetting from '@/components/meta-dynamic-item/number/property-setting';
import ObjectPropertySetting from '@/components/meta-dynamic-item/object/property-setting';
import TextPropertySetting from '@/components/meta-dynamic-item/text/property-setting';
import TimePropertySetting from '@/components/meta-dynamic-item/time/property-setting';
import UnitPropertySetting from '@/components/meta-dynamic-item/unit/property-setting';
import { MetaDataType } from '@/const/metadata';
import SectionHeader from '@/pages/app-designer/components/section-header';
import { FormField, FormTagConfig } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Form } from '@gwy/components-web';
import { forwardRef, useImperativeHandle } from 'react';
import FormItemCommonConfig from './form-item-common-config';
import styles from './index.less';

export type FormItemConfigValues = Pick<FormField, 'readonly' | 'fieldName' | 'placeholder' | 'required' | 'widgetWith' | 'inputDefaultConfig'> &
  FormTagConfig;

export type FormItemConfigProps = {
  tag?: DataUnitTagVO;
  field?: FormField;
  onChange?: (values: FormItemConfigValues) => void;
};

export type FormItemConfigRef = {
  validateFields: () => Promise<any>;
};

const FormItemConfig = ({ tag, field, onChange }: FormItemConfigProps, ref) => {
  const [form] = Form.useForm();

  const readonly = Form.useWatch(['readonly'], form);

  useImperativeHandle(ref, () => ({
    validateFields: () => {
      return form.validateFields();
    },
  }));

  if (!field) {
    return <div className={styles.emptyContainer}>请在左侧画布中选中标签或字段</div>;
  }

  const { tagMetaDataConfig } = tag || {};
  const { type } = tagMetaDataConfig || {};

  return (
    <div className={styles.container}>
      <SectionHeader title="标签配置" desc={`来源：${tag?.$$dataUnit?.name}`} />

      <div className={styles.formWrapper}>
        <Form
          layout="vertical"
          scrollToFirstError={{ behavior: 'instant', block: 'end', focus: true }}
          form={form}
          onValuesChange={(changedValues, values) => {
            // console.log('changedValues, values', changedValues, values);
            // onChange?.(values);
            setTimeout(() => {
              const values = form.getFieldsValue();
              onChange?.(values);
            }, 0);
          }}
        >
          <FormItemCommonConfig tag={tag} field={field} />

          {!readonly && (
            <>
              {type === MetaDataType.Text && <TextPropertySetting tag={tag} field={field} />}
              {type === MetaDataType.Number && <NumberPropertySetting tag={tag} field={field} />}
              {type === MetaDataType.DateTime && <TimePropertySetting tag={tag} field={field} />}
              {type === MetaDataType.Object && <ObjectPropertySetting tag={tag} field={field} />}
              {type === MetaDataType.Unit && <UnitPropertySetting tag={tag} field={field} />}
              {type === MetaDataType.Code && <CodePropertySetting tag={tag} field={field} />}
              {type === MetaDataType.File && <FilePropertySetting tag={tag} field={field} />}
              {type === MetaDataType.Address && <AddressPropertySetting tag={tag} field={field} />}
            </>
          )}
        </Form>
      </div>
    </div>
  );
};

export default forwardRef(FormItemConfig);
