import { AppDesignerContext } from '@/pages/app-designer/context';
import { Form, Input } from 'antd';
import { useContext } from 'react';
import SectionHeader from '../../../section-header';
import DataUnitSelect from '../data-unit-select';
import styles from './index.less';

const BaseGroupSetting = () => {
  const { baseGroupName, setBaseGroupName } = useContext(AppDesignerContext);

  return (
    <div>
      <div>
        <SectionHeader title="表单数据单元配置" />
        <div className={styles.sectionContent}>
          <DataUnitSelect />
        </div>
      </div>
      <div>
        <SectionHeader title="基础分组配置" />
        <div className={styles.sectionContent}>
          <Form layout="vertical">
            <Form.Item label={'标题名称'}>
              <Input placeholder="请输入" value={baseGroupName} onChange={(e) => setBaseGroupName(e.target.value)} />
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default BaseGroupSetting;
