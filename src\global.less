body {
  overflow: hidden;
  font-family: 'PingFang SC-Medium', 'PingFang SC';
  font-size: 14px;
  color: #1d2129;
  line-height: 1.5;
}

#root {
  height: 100%;
  overflow: auto;
}

#root-master {
  height: 100%;
  overflow: auto;
}

/* 自定义整个滚动条 */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: transparent;
}

/* 自定义滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 自定义滚动条的滑块 */
::-webkit-scrollbar-thumb {
  background-color: #c9cdd4;
  border-radius: 4px;
}

/* 当滑块悬停或活动时的样式 */
::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.ant-pro-table-column-setting-list .ant-tree-treenode-draggable .ant-tree-switcher {
  display: none;
}

.ant-pro-table-column-setting-overlay > .ant-popover-content > .ant-popover-inner > .ant-popover-inner-content {
  max-height: 400px;
  overflow-y: auto;
}
