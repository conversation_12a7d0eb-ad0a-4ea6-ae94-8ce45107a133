import { codeDateTypeOptions, CodeNumberType, CodeType, CustomCode, customCodeOptions } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Input } from '@gwy/components-web';
import { memo } from 'react';
import { UseIn } from '../const';

export type CodeMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean; // 仅预览模式
};

const CodeMeta = memo(({ useIn, tag, field, value, onChange, isPreview }: CodeMetaProps) => {
  const { type } = tag?.tagMetaDataConfig?.metaDataCodeDTO || {};
  const { placeholder } = field || {};
  const { configList } = field?.config?.metaDataCodeDTO || {};

  const renderRules = () => {
    return configList?.[0]?.elementList?.map((element) => {
      switch (element.type) {
        case CustomCode.chinese:
        case CustomCode.symbol:
        case CustomCode.english_lower:
        case CustomCode.english_upper: {
          if (element.text) {
            return <Input disabled key={element.id} value={element.text} />;
          }
          return (
            <Input
              maxLength={element.maxLength}
              placeholder={`请输入${element.maxLength}位${customCodeOptions.find((item) => item.value === element.type)?.label}`}
              value={value?.raw_value?.[element.id]}
              onChange={(e) => {
                onChange({
                  raw_value: {
                    ...value?.raw_value,
                    [element.id]: e.target.value,
                  },
                });
              }}
            />
          );
        }
        case CustomCode.number: {
          if (element.numberForm) {
            return (
              <Input disabled key={element.id} value={`${element.maxLength}位${element.numberForm === CodeNumberType.increment ? '自增' : '随机'}`} />
            );
          }
          if (element.text) {
            return <Input disabled key={element.id} value={element.text} />;
          }
          return (
            <Input
              maxLength={element.maxLength}
              placeholder={`请输入${element.maxLength}位数字`}
              value={value?.raw_value?.[element.id]}
              onChange={(e) => {
                onChange({
                  raw_value: {
                    ...value?.raw_value,
                    [element.id]: e.target.value,
                  },
                });
              }}
            />
          );
        }
        case CustomCode.date: {
          return <Input disabled key={element.id} value={codeDateTypeOptions.find((item) => item.value === element.dateForm)?.label} />;
        }
        default: {
          return null;
        }
      }
    });
  };

  if (field?.readonly || isPreview) {
    if (useIn === UseIn.Designer) {
      return '回显数据';
    }
    const { value: tagValue, data: tagData } = value || {};
    return tagValue ?? '-';
  }

  if (type === CodeType.normal) {
    const { std_value } = value || {};
    return (
      <Input
        placeholder={placeholder || '请输入'}
        value={std_value}
        onChange={(e) => {
          const { value } = e.target;
          onChange({ std_value: value });
        }}
      />
    );
  }

  return <div style={{ display: 'grid', gridTemplateColumns: `repeat(${field.widgetWith === '100%' ? 4 : 2}, 1fr)`, gap: 8 }}>{renderRules()}</div>;
});

export default CodeMeta;
