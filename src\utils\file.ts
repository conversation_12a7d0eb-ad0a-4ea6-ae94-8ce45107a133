import { Base64 } from 'js-base64';

export const getPreviewUrl = (url) => {
  if (!url) return '';

  const ext = url.split('.').pop();
  // 以下几种格式，使用 v4.3.0 有问题，因此改成使用 v4.2.1
  if (['doc', 'docx'].includes(ext)) {
    return `https://fileview-word.gongwuyun.com/onlinePreview?url=${encodeURIComponent(Base64.encode(url))}&officePreviewType=pdf`;
  }

  return `https://fileview.gongwuyun.com/onlinePreview?url=${encodeURIComponent(Base64.encode(url))}&officePreviewType=pdf`;
};
