import { ImageTypeList, sizeUnitList } from '@/const/metadata';
import { MinusOutlined } from '@ant-design/icons';
import { Form, FormInstance, InputNumber, Select, Space } from '@gwy/components-web';

export const PictureDTOKey = 'metaDataPictureDTO';

type Props = {
  form?: FormInstance;
};

const PictureConfig = ({ form }: Props) => {
  const maxValidator = (min, value, callback: any) => {
    if (value <= min && value !== null) {
      callback('最大值必须大于最小值');
    } else {
      callback();
    }
  };
  return (
    <div>
      <Form.Item label="格式类型" name={[PictureDTOKey, 'formats']} rules={[{ required: true, message: '请选择' }]}>
        <Select placeholder="请选择" options={ImageTypeList} mode="multiple"></Select>
      </Form.Item>

      <Form.Item label="容量大小" required style={{ marginBottom: 0 }}>
        <Space align="start">
          <Form.Item label="" name={[PictureDTOKey, 'sizeMin']} rules={[{ required: true, message: '请填写' }]} style={{ maxWidth: '100px' }}>
            <InputNumber placeholder="请填写" min={0.01} precision={2} step={0.01} />
          </Form.Item>
          <Form.Item label="">
            <MinusOutlined />
          </Form.Item>

          <Form.Item
            style={{ width: '100px' }}
            label=""
            name={[PictureDTOKey, 'sizeMax']}
            rules={[
              {
                validator(rule, value) {
                  const min = form.getFieldValue([PictureDTOKey, 'sizeMin']);
                  if (Number(value) <= Number(min) && value !== null) {
                    return Promise.reject('最大值必须大于最小值');
                  }
                  return Promise.resolve();
                },
              },
              { required: true, message: '请输入' },
            ]}
          >
            <InputNumber placeholder="请填写" min={0.01} precision={2} step={0.01} />
          </Form.Item>
          <Form.Item label="" name={[PictureDTOKey, 'sizeUnit']} rules={[{ required: true, message: '请选择' }]}>
            <Select placeholder="请选择" options={sizeUnitList}></Select>
          </Form.Item>
        </Space>
      </Form.Item>
      <Form.Item label="图片宽度(px)" style={{ marginBottom: 0 }}>
        <Space align="start">
          <Form.Item label="" name={[PictureDTOKey, 'widthMin']} style={{ maxWidth: '100px' }}>
            <InputNumber placeholder="请填写" min={0.01} precision={2} step={0.01} />
          </Form.Item>
          <Form.Item label="">
            <MinusOutlined />
          </Form.Item>
          <Form.Item
            label=""
            name={[PictureDTOKey, 'widthMax']}
            rules={[
              {
                validator(rule, value) {
                  const min = form.getFieldValue([PictureDTOKey, 'widthMin']);
                  console.log(min, value, 'min ');
                  if (Number(value) <= Number(min) && value !== null && value !== null) {
                    return Promise.reject('最大值必须大于最小值');
                  }
                  return Promise.resolve();
                },
              },
            ]}
            style={{ width: '100px' }}
          >
            <InputNumber placeholder="请填写" min={0.01} precision={2} step={0.01} />
          </Form.Item>
        </Space>
      </Form.Item>
      <Form.Item label="图片高度(px)" style={{ marginBottom: 0 }}>
        {/* <Form.Item label="" name={[PictureDTOKey, 'width']} rules={[{ required: true, message: '请填写' }]}></Form.Item> */}
        <Space align="start">
          <Form.Item label="" name={[PictureDTOKey, 'heightMin']} style={{ maxWidth: '100px' }}>
            <InputNumber placeholder="请填写" min={0.01} precision={2} step={0.01} />
          </Form.Item>
          <Form.Item label="">
            <MinusOutlined />
          </Form.Item>
          <Form.Item
            label=""
            name={[PictureDTOKey, 'heightMax']}
            rules={[
              {
                validator(rule, value) {
                  const min = form.getFieldValue([PictureDTOKey, 'heightMin']);
                  console.log(min, value, 'min ');
                  if (Number(value) <= Number(min) && value !== null && value !== null) {
                    return Promise.reject('最大值必须大于最小值');
                  }
                  return Promise.resolve();
                },
              },
            ]}
            style={{ width: '100px' }}
          >
            <InputNumber placeholder="请填写" min={0.01} precision={2} step={0.01} />
          </Form.Item>
        </Space>
      </Form.Item>
      <Form.Item label="图片数量" required>
        <Space>
          <Form.Item name={[PictureDTOKey, 'pictureNum']} rules={[{ required: true, message: '请输入' }]} initialValue={1}>
            <InputNumber min={1} step={1} max={10} />
          </Form.Item>
        </Space>
      </Form.Item>
    </div>
  );
};

export default PictureConfig;
