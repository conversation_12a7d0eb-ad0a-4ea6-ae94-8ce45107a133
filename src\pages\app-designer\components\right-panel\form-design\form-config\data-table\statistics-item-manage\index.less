.container {
  :global {
    .ant-form-item {
      .ant-form-item-label {
        > label {
          width: 100%;

          &::after {
            display: none;
          }
        }
      }
    }
  }
}

.header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list {
  background: #f7f8fa;
  border-radius: 0;
  border: 1px dashed #c9cdd4;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 12px 16px;

  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;

    .name {
      flex-grow: 1;
      overflow: auto;
      color: #4d7bf6;
    }

    .delete {
      flex-shrink: 0;
      color: #c9cdd4;
    }
  }
}
