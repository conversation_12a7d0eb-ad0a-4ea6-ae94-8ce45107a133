import { EditOutlined } from '@ant-design/icons';
import { Button, Input, Popover, Tooltip } from '@gwy/components-web';
import { useState } from 'react';
import styles from './index.less';

interface IProps {
  value?: string;
  onChange?: (value: string) => void;
}

const ConditionGroupRemark = ({ value, onChange }: IProps) => {
  const [visible, setVisible] = useState(false);
  const [inputValue, setInputValue] = useState(value || '');

  const handleConfirm = () => {
    onChange?.(inputValue);
    setVisible(false);
  };

  const handleCancel = () => {
    setInputValue(value || '');
    setVisible(false);
  };

  return (
    <Popover
      placement="bottom"
      open={visible}
      onOpenChange={setVisible}
      trigger="click"
      content={
        <div className={styles.popoverContent}>
          <div className={styles.label}>备注：</div>
          <Input.TextArea
            placeholder="请输入备注消息"
            maxLength={200}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            className={styles.textArea}
          />
          <div className={styles.footer}>
            <div className={styles.actions}>
              <Button onClick={handleCancel}>取消</Button>
              <Button type="primary" onClick={handleConfirm}>
                确定
              </Button>
            </div>
          </div>
        </div>
      }
    >
      <Tooltip title="备注">
        <EditOutlined className={styles.iconRemarkGroup} />
      </Tooltip>
    </Popover>
  );
};

export default ConditionGroupRemark;
