import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Form } from '@gwy/components-web';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';
import TimeMeta from './index';

export type TimeMetaFormProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  label?: React.ReactNode;
  prefix?: any[];
  isPreview?: boolean;
  options?: any;
};

const TimeMetaForm = memo<TimeMetaFormProps>(({ useIn, tag, field, label, prefix, isPreview, options }) => {
  const { required, readonly } = field || {};

  const rules = useMemo(() => {
    return !readonly && [UseIn.App].includes(useIn)
      ? [
          required && {
            required: true,
            message: '请选择',
          },
        ].filter(Boolean)
      : undefined;
  }, [required, readonly]);

  return (
    <Form.Item label={label} name={[...(prefix || []), getMetaTagUniqueId(field), 'value']} rules={rules} required={required && !readonly}>
      <TimeMeta useIn={useIn} tag={tag} field={field} isPreview={isPreview} options={options} />
    </Form.Item>
  );
});

export default TimeMetaForm;
