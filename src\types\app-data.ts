import { DataRecord } from './datasource-data';

export type FormDataDraftDetailVO = {
  /**
   * 审批信息
   */
  approveMsg?: null | string;
  /**
   * 审批状态
   */
  approveStatus?: ApproveStatus;
  /**
   * 创建时间
   */
  createTime?: null | string;
  /**
   * 创建岗位
   */
  creatorPost?: null | string;
  /**
   * 创建岗位id
   */
  creatorPostId?: null | string;
  /**
   * 创建人
   */
  creatorUser?: null | string;
  /**
   * 创建人id
   */
  creatorUserId?: null | string;
  /**
   * 暂存数据
   */
  draftItems?: FormDataDraftItemVO[] | null;
  /**
   * 表单数据暂存id
   */
  formDataDraftId?: null | string;
  /**
   * 表单id
   */
  formId?: number | null;
  /**
   * 表单版本id
   */
  formVersionId?: number | null;
  /**
   * 表单名称
   */
  name?: null | string;
  [property: string]: any;
};

/**
 * 审批状态
 */
export enum ApproveStatus {
  Agree = 'AGREE',
  Refuse = 'REFUSE',
  Return = 'RETURN',
  WaitApprove = 'WAIT_APPROVE',
}

/**
 * FormDataDraftItemVO
 */
export type FormDataDraftItemVO = {
  /**
   * 数据单元id
   */
  dataUnitId?: number | null;
  /**
   * 字段数据
   */
  record?: DataRecord;
  [property: string]: any;
};

/**
 * 返回的数据
 *
 * FormDataDetailVO
 */
export type FormDataDetailVO = {
  /**
   * 表单id
   */
  formId?: number | null;
  /**
   * 表单版本id
   */
  formVersionId?: number | null;
  /**
   * 字段数据
   */
  items?: FormDataItemVO[] | null;
  /**
   * 表单名称
   */
  name?: null | string;
  /**
   * word文件url
   */
  wordFileUrl?: null | string;
  [property: string]: any;
};

/**
 * FormDataItemVO
 */
export type FormDataItemVO = {
  /**
   * 数据单元id
   */
  dataUnitId?: number | null;
  /**
   * 字段数据
   */
  record?: DataRecord;
  [property: string]: any;
};
