import { getHistoryData } from '@/services/datasource-data';
import { Modal, TabsProps } from '@gwy/components-web';
import { Tabs } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import DataList from '../app-table/data-list';
import styles from './index.less';
// import DataList from ''

interface Iprops {
  onCancel?: () => void;
  onOk?: (values?: any) => void;
  records: any;
  cFields: any;
  cTagsMap: any;
  formVersion: any;
  post: any;
  dataId: any;
}
const HandleDataHistory = (props: Iprops) => {
  const { onOk, onCancel, cFields, cTagsMap, formVersion, post, dataId, records: formReocrds } = props;
  const [activeKey, setActiveKey] = useState(null);
  const [record, setRecords] = useState([]);

  const unitToDataId = useMemo(() => {
    const record = formReocrds?.map((item) => {
      if (item?.dataUnitId && item?.record) {
        return item?.record;
      }
      return item;
    })?.[0];
    const map = {};
    formVersion?.dataUnits?.forEach((dataUnit) => {
      let codeKey = '_id';
      if (!dataUnit?.mainDataUnit) {
        codeKey = `${dataUnit.dataUnitId}_id`;
      }
      map[dataUnit.dataUnitId] = record?.[codeKey];
    });
    // console.log(map, record, 'map----------')
    return map;
  }, [formReocrds, formVersion]);
  const getRecords = async (defaultKey) => {
    if (!unitToDataId[defaultKey]) {
      return setRecords([]);
    }
    const data = await getHistoryData({
      postId: post?.postId,
      dataId: unitToDataId[defaultKey],
      dataUnitId: defaultKey,
      formVersionId: formVersion?.formVersionId,
    });
    setRecords(data || []);
  };

  useEffect(() => {
    const defaultKey = formVersion.dataUnits?.[0]?.dataUnitId;
    setActiveKey(defaultKey);
  }, [formVersion]);

  useEffect(() => {
    activeKey && getRecords(activeKey);
  }, [activeKey]);

  // const [cDataUnits]
  const dataunitTabs = useMemo(() => {
    // const tabs =
    const dataUnits = formVersion?.dataUnits || [];
    const tabs: TabsProps['items'] = dataUnits.map((item) => {
      const fields = formVersion?.extConfig.fields?.filter((field) => field.dataUnitId === item.dataUnitId) || [];
      return {
        key: item.dataUnitId,
        label: item.name,
        children: <DataList cTagsMap={cTagsMap} dataSource={record} formFields={fields} toolBarRender={false} dataUnits={dataUnits} />,
      };
    });
    return tabs;
  }, [formVersion, record, cFields, cTagsMap]);
  return (
    <Modal title="处理历史" onCancel={onCancel} onOk={onOk} open canDrag size="small">
      <div className={styles.historyWrap}>
        <Tabs size="small" activeKey={activeKey} onChange={(key) => setActiveKey(key)} type="card" items={dataunitTabs}></Tabs>
      </div>
    </Modal>
  );
};

export default HandleDataHistory;
