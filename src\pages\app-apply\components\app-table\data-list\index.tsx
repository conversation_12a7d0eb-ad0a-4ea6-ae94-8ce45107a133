import AddressMeta from '@/components/meta-dynamic-item/address';
import CodeMeta from '@/components/meta-dynamic-item/code';
import FileMeta from '@/components/meta-dynamic-item/file';
import NumberMeta from '@/components/meta-dynamic-item/number';
import ObjectMeta from '@/components/meta-dynamic-item/object';
import TextMeta from '@/components/meta-dynamic-item/text';
import TimeMeta from '@/components/meta-dynamic-item/time';
import UnitMeta from '@/components/meta-dynamic-item/unit';
import { MetaDataType } from '@/const/metadata';
import { DataRecord } from '@/types/datasource-data';
import { FormField, SourceType } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { TablePaginationConfig, TextEllipsisTooltip } from '@gwy/components-web';
import { useEffect, useMemo } from 'react';
import { DATA_TYPE_ENUMS } from '..';
import TagCustomIcon from './components/tag-custom-icon';
import styles from './index.less';

type Props = {
  isPreview?: boolean;
  formFields?: FormField[];
  cTagsMap?: Record<number, DataUnitTagVO>;
  dataSource?: any[];
  pagination?: TablePaginationConfig;
  readIds?: string[];
  headerTitle?: React.ReactNode;
  toolBarRender?: any['toolBarRender'] | false;
  renderColumns?: (columns: ProColumns[]) => ProColumns[];
  setTablehasColumns?: (columns: any[]) => void;
  dataUnits?: any[];
};

const DataList = ({
  isPreview,
  formFields,
  cTagsMap,
  dataSource,
  pagination,
  readIds,
  headerTitle,
  toolBarRender,
  renderColumns,
  setTablehasColumns,
  dataUnits,
}: Props) => {
  const columns = useMemo(() => {
    const mianDataUnit = (dataUnits || [])?.find((item) => item?.mainDataUnit);
    const cols = formFields?.map<ProColumns<DataRecord>>((_formField) => {
      const formField = { ..._formField };
      formField.readonly = true;
      const { sourceType, fieldConfig, config } = formField;
      if (sourceType === SourceType.Field) {
        console.log(formField, 'formField----------');
        return {
          title: fieldConfig?.fieldName,
          width: 'auto',
          dataIndex: fieldConfig.fieldId,
          render: (value, record, index) => {
            const renderCell = () => {
              return <TextEllipsisTooltip text={value ?? '-'} />;
            };

            return <div style={{ maxWidth: 300 }}>{renderCell()}</div>;
          },
        };
      }

      const { type } = config || {};
      const tag = cTagsMap[getMetaTagUniqueId(formField)];
      const isnotMain = mianDataUnit?.dataUnitId !== _formField?.dataUnitId;
      // 一个或者没传数据单元默认就时主数据单元
      const codeKey = isnotMain
        ? dataUnits?.length === 1 || dataUnits?.length === 0 || !dataUnits
          ? formField.code
          : `${_formField.dataUnitId}_${formField.code}`
        : formField.code;
      console.log(codeKey, _formField, isnotMain, mianDataUnit, 'codeKey-----------');
      return {
        title: formField.fieldName || tag?.name,
        width: 'auto',
        dataIndex: codeKey,
        render: (value, record, index) => {
          const renderCell = () => {
            switch (type) {
              case MetaDataType.Text: {
                return <TextMeta tag={tag} field={formField} value={value} />;
              }
              case MetaDataType.Number: {
                return <NumberMeta tag={tag} field={formField} value={value} />;
              }
              case MetaDataType.DateTime: {
                return <TimeMeta tag={tag} field={formField} value={value} />;
              }
              case MetaDataType.Object: {
                return <ObjectMeta tag={tag} field={formField} value={value} />;
              }
              case MetaDataType.Unit: {
                return <UnitMeta tag={tag} field={formField} value={value} />;
              }
              case MetaDataType.Code: {
                return <CodeMeta tag={tag} field={formField} value={value} />;
              }
              case MetaDataType.File: {
                return <FileMeta tag={tag} field={formField} value={value} />;
              }
              case MetaDataType.Address: {
                return <AddressMeta tag={tag} field={formField} value={value} />;
              }
              default: {
                return null;
              }
            }
          };

          return <div style={{ maxWidth: 300 }}>{renderCell()}</div>;
        },
      };
    });
    cols?.unshift({
      title: '序号',
      width: 85,
      dataIndex: 'idx',
      render: (value, r, index) => {
        const unRead = readIds && !readIds.includes(r._id);
        const isSameGroup = !!r.split_type; // 同组数据----组合标签，以及数据拆分
        const isSamePiece = !isSameGroup; // 同条数据 同条数据和同组数据互斥
        const isMainData = r?.data_type === DATA_TYPE_ENUMS.Main_Data;
        const ischildData = r?.data_type === DATA_TYPE_ENUMS.Child_Data;
        const hasChildren = r?.children?.length > 0;
        return (
          <span className={styles.seq}>
            {index + 1}
            {unRead && <span className={styles.unRead} />}
            {ischildData && <TagCustomIcon text="子" isSmall className={styles.icon} />}
            {isMainData && <TagCustomIcon text="主" isSmall className={styles.icon} />}
          </span>
        );
      },
    });

    if (typeof renderColumns === 'function') {
      return renderColumns(cols);
    }

    return cols;
  }, [formFields, cTagsMap, renderColumns, isPreview, setTablehasColumns, readIds, dataUnits]);

  // useEffect(() => {
  //   setTablehasColumns && setTablehasColumns(columns)
  // }, [columns]);
  // const setRenderColunms = useCallback((columns) => {
  //   setTablehasColumns(columns);
  // }, []);

  useEffect(() => {
    console.log('columns-------', columns);
    setTablehasColumns && setTablehasColumns(columns);
  }, [columns]);

  return (
    <div className={styles.container}>
      <ProTable
        columns={columns}
        dataSource={dataSource}
        rowKey="_id"
        pagination={pagination}
        search={false}
        headerTitle={headerTitle}
        toolBarRender={toolBarRender}
        options={{
          setting: {
            /**
             * 默认虚拟滚动，会莫名导致部分数据被切割，无法滚出（查不出原因，官网例子没问题）
             * github中的类似问题参考：https://github.com/ant-design/pro-components/issues/8841
             * 解决：给个超大高度用于禁止虚拟滚动，同时在global.less中设置样式
             * .ant-pro-table-column-setting-overlay > .ant-popover-content > .ant-popover-inner > .ant-popover-inner-content {
                  max-height: 400px;
                  overflow-y: auto;
                }
             */
            listsHeight: 99999,
          },
          fullScreen: false,
          density: false,
          reload: false,
          search: false,
        }}
        scroll={{ x: 'max-content' }}
        bordered
        size="small"
      />
    </div>
  );
};

export default DataList;
