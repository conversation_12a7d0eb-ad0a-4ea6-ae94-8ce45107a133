import { AppDesignerContext } from '@/pages/app-designer/context';
import { EditOutlined } from '@ant-design/icons';
import { Button, Form } from 'antd';
import { useContext, useEffect, useMemo, useState } from 'react';
import DataSyncSetting from '../../../data-sync-setting';
import { ExecutionEventType } from '../../constants';

type Props = {
  value?: any;
  onChange?: (value: any) => void;
  item?: { key?: ExecutionEventType; label?: string; disabled?: boolean };
  preName?: number;
  name?: number;
};

const DataSyncConfig = ({ value, onChange, item, preName, name }: Props) => {
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const {
    configuredFormFields,
    allConfiguredDataUnitsWithTags: configuredDataUnitsWithTags,
    tagsMap,
    post,
    tableList,
    configuredDataUnits,
  } = useContext(AppDesignerContext);
  console.log('DataSyncConfig-------', configuredFormFields, configuredDataUnitsWithTags);

  const basicGroupDataUnitWithTags = useMemo(() => {
    const basicUintIds = (configuredDataUnits || [])?.map((item) => item.dataUnitId);
    return configuredDataUnitsWithTags?.filter((item) => basicUintIds.includes(item.dataUnitId));
    // console.log(configuredDataUnits,configuredDataUnitsWithTags, 'basicGroupDataUnitWithTags-----------')
  }, [tagsMap, configuredDataUnitsWithTags, configuredDataUnits]);

  useEffect(() => {
    if (!open) return;
    form.setFieldsValue(value);
  }, [open]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onChange(values);
      setOpen(false);
    } catch (e) {}
  };

  return (
    <>
      <Button block icon={<EditOutlined />} onClick={() => setOpen(true)}>
        {item?.label}
      </Button>
      {open && (
        <DataSyncSetting
          // form={form}
          orgId={post?.orgId}
          onCancel={() => {
            setOpen(false);
          }}
          onOk={(values) => {
            onChange(values?.dataSyncConfigs);
            setOpen(false);
          }}
          dataUnitsOps={configuredDataUnitsWithTags}
          basicDataUnitOps={basicGroupDataUnitWithTags}
          cFiellds={configuredFormFields}
          detailData={value}
        />
      )}

      {/* <Modal destroyOnHidden title={item?.label} open={open} onOk={handleOk} onCancel={() => setOpen(false)}>
        <div>1111</div>
      </Modal> */}
    </>
  );
};

export default DataSyncConfig;
