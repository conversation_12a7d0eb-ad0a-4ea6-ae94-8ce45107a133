import { CodeType, commonCodeOptions, CustomCode, customCodeOptions } from '@/const/metadata';
import { MetaDataCodeDTO } from '@/types/metadata';
import { get, isEmpty } from 'lodash-es';

export const validator = (rule, value, config: MetaDataCodeDTO, required) => {
  const { type, configList, elementTypeList } = config || {};

  if (type === CodeType.custom && !isEmpty(configList)) {
    const { raw_value } = value || {};
    const { elementList } = configList[0] || {};
    if (!isEmpty(elementList)) {
      if (
        required &&
        elementList.some(
          (ele) =>
            !ele.text &&
            !ele.numberForm &&
            [CustomCode.chinese, CustomCode.symbol, CustomCode.english_lower, CustomCode.english_upper, CustomCode.number].includes(ele.type) &&
            !get(raw_value, ele.id),
        )
      ) {
        return Promise.reject('请输入必填项');
      }
      const errMsgList = elementList.reduce((list, ele) => {
        // 没有配置固定值时并且没有配置随机数、自增数
        if (!ele.text && !ele.numberForm) {
          let regExp = null;
          if ([CustomCode.chinese, CustomCode.symbol, CustomCode.english_lower, CustomCode.english_upper, CustomCode.number].includes(ele.type)) {
            switch (ele.type) {
              case CustomCode.chinese:
                regExp = new RegExp(`^[\\u4e00-\\u9fa5]{${ele.maxLength}}$`);
                break;
              case CustomCode.symbol:
                regExp = new RegExp(`^[\\p{P}\\p{S}]{${ele.maxLength}}$`, 'gu');
                break;
              case CustomCode.english_lower:
                regExp = new RegExp(`^[a-z]{${ele.maxLength}}$`);
                break;
              case CustomCode.english_upper:
                regExp = new RegExp(`^[A-Z]{${ele.maxLength}}$`);
                break;
              case CustomCode.number:
                regExp = new RegExp(`^[0-9]{${ele.maxLength}}$`);
                break;
            }
            let eleValue = get(raw_value, ele.id);
            if (eleValue && regExp && !regExp.test(eleValue)) {
              list.push(`${ele.maxLength}位${customCodeOptions.find((item) => item.value === ele.type)?.label || ''}`);
            }
          }
        }
        return list;
      }, []);
      if (!isEmpty(errMsgList)) {
        return Promise.reject(`请输入${errMsgList.join('、')}`);
      }
    }
  }
  if (type === CodeType.normal) {
    const { std_value } = value || {};
    if (required && (!value || !std_value)) {
      return Promise.reject('请输入必填项');
    }
    if (
      std_value &&
      !isEmpty(elementTypeList) &&
      !elementTypeList.some((ite) => std_value.match(commonCodeOptions.find((opt) => opt.value === ite).regExp))
    ) {
      return Promise.reject(`请输入${elementTypeList.map((ite) => commonCodeOptions.find((opt) => opt.value === ite).label).join('、')}`);
    }
  }
  return Promise.resolve();
};
