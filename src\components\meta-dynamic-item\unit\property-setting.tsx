import { UseIn } from '@/components/metadata-dialog/metadata-config/const';
import NumberRender from '@/components/metadata-dialog/metadata-config/number-config/number-render';
import { unitTypeOptions } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Checkbox, Form, Select, Space } from '@gwy/components-web';
import { useEffect, useMemo } from 'react';
import { validator } from './validator';

export const UnitDTOKey = 'metaDataUnitDTO';

export type UnitPropertySettingProps = {
  tag?: DataUnitTagVO;
  field?: FormField;
};

const UnitPropertySetting = ({ tag, field }: UnitPropertySettingProps) => {
  const form = Form.useFormInstance();

  const { metaDataUnitDTO } = tag?.tagMetaDataConfig || {};
  const { type, configList: configListLimit, min, max, rangeType } = metaDataUnitDTO || {};
  const configList = Form.useWatch([UnitDTOKey, 'configList'], form);
  const unit = Form.useWatch([UnitDTOKey, 'unit'], form);
  const unitTypeOption = unitTypeOptions.find((item) => item.value === type);
  const unitTypeOptionOptions = unitTypeOption?.options?.filter((item) => (configListLimit as string[])?.includes(item.value)) || [];

  const unitOptions = useMemo(() => {
    const unitOptions = unitTypeOptionOptions.filter((item) => (configList || []).includes(item.value));
    return unitOptions;
  }, [configList, unitTypeOptionOptions]);

  const rules = useMemo(
    () => [
      {
        validator: (rule, value) =>
          validator(
            rule,
            {
              raw_unit: unit,
              raw_value: value,
            },
            true,
            { min, max, rangeType, unit, type },
          ),
      },
    ],
    [metaDataUnitDTO, unit, min, max, rangeType],
  );

  useEffect(() => {
    form.setFieldsValue(field.config);
  }, []);

  return (
    <div>
      <Form.Item
        key={type}
        label={`配置${unitTypeOption?.label}单位`}
        name={[UnitDTOKey, 'configList']}
        rules={[{ required: true, message: '请选择' }]}
      >
        <Checkbox.Group
          options={unitTypeOptionOptions}
          onChange={(values) => {
            // 设置最大的单位
            form.setFieldValue([UnitDTOKey, 'unit'], Array.isArray(values) ? values[0] : undefined);
          }}
        />
      </Form.Item>
      <Form.Item label="数值范围" required style={{ marginBottom: 0 }}>
        <Space style={{ alignItems: 'baseline' }}>
          <NumberRender form={form} prefix={[UnitDTOKey]} rules={rules} useIn={UseIn.Designer} />
        </Space>
        <Form.Item name={[UnitDTOKey, 'unit']} rules={[{ required: true, message: '请选择' }]}>
          <Select placeholder="请选择单位" options={unitOptions} style={{ width: 100 }} disabled />
        </Form.Item>
      </Form.Item>
    </div>
  );
};

export default UnitPropertySetting;
