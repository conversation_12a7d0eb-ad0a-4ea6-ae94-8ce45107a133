.spin {
  height: 100%;

  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.metadataContainer {
  position: relative;
  height: 100%;
  font-size: 14px;
  display: flex;
  flex-direction: column;

  .body {
    flex: 1;
    position: relative;
    display: flex;
    height: 100%;

    :global {
      .ant-tabs-content-holder {
        overflow: auto;
      }

      .ant-tabs-content,
      .ant-tabs-tabpane {
        height: 100%;
      }
    }

    .wrapper {
      width: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
    }
  }

  .bottom {
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 50px;
    border-top: 1px solid #e5e6eb;
    padding: 0 20px;
    gap: 15px;
  }
}
