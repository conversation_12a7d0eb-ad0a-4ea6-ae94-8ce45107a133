import { FileSizeUnit, FileSizeUnitList, videoTypeList } from '@/const/metadata';
import { MinusOutlined } from '@ant-design/icons';
import { Form, FormInstance, InputNumber, Radio, Select, Space } from '@gwy/components-web';
import { useContext } from 'react';
import { MetaDataContext } from '../../context';

export const VideoDTOKey = 'metaDataVideoDTO';

type Props = {
  form?: FormInstance;
};

const VideoConfig = ({ form }: Props) => {
  const { disabled, hasApproved } = useContext(MetaDataContext);

  const ifLimitFormat = Form.useWatch([VideoDTOKey, 'ifLimitFormat'], form);

  return (
    <div>
      <Form.Item label="视频格式" name={[VideoDTOKey, 'ifLimitFormat']} rules={[{ required: true, message: '请选择' }]} initialValue={false}>
        <Radio.Group
          disabled={hasApproved || disabled}
          options={[
            { label: '不限制', value: false },
            { label: '限制', value: true },
          ]}
        />
      </Form.Item>

      {ifLimitFormat && (
        <div>
          <Form.Item label="格式类型" name={[VideoDTOKey, 'formats']} rules={[{ required: true, message: '请选择' }]}>
            <Select placeholder="请选择" options={videoTypeList} mode="multiple"></Select>
          </Form.Item>
        </div>
      )}

      <Form.Item label="单视频容量大小" required style={{ marginBottom: 0 }}>
        <Space align="start">
          <Form.Item
            label=""
            name={[VideoDTOKey, 'sizeMin']}
            rules={[{ required: true, message: '请填写' }]}
            style={{ maxWidth: '100px' }}
            initialValue={0}
          >
            <InputNumber placeholder="请填写" min={0} step={1} max={1000} />
          </Form.Item>
          <Form.Item label="">
            <MinusOutlined />
          </Form.Item>

          <Form.Item
            initialValue={200}
            style={{ width: '100px' }}
            label=""
            name={[VideoDTOKey, 'sizeMax']}
            rules={[
              {
                validator(rule, value) {
                  const min = form.getFieldValue([VideoDTOKey, 'sizeMin']);
                  if (Number(value) <= Number(min) && value !== null) {
                    return Promise.reject('最大值必须大于最小值');
                  }
                  return Promise.resolve();
                },
              },
              { required: true, message: '请输入' },
            ]}
          >
            <InputNumber placeholder="请填写" min={0} step={1} max={1000} />
          </Form.Item>
          <Form.Item label="" name={[VideoDTOKey, 'sizeUnit']} rules={[{ required: true, message: '请选择' }]} initialValue={FileSizeUnit.MB}>
            <Select placeholder="请选择" options={FileSizeUnitList}></Select>
          </Form.Item>
        </Space>
      </Form.Item>
      <Form.Item label="视频数量" required>
        <Space>
          <Form.Item name={[VideoDTOKey, 'videoNum']} rules={[{ required: true, message: '请输入' }]} initialValue={1}>
            <InputNumber min={1} step={1} max={10} />
          </Form.Item>
        </Space>
      </Form.Item>
      <Form.Item label="视频宽度(px)" style={{ marginBottom: 0 }}>
        <Space align="start">
          <Form.Item label="" name={[VideoDTOKey, 'widthMin']} style={{ maxWidth: '100px' }}>
            <InputNumber placeholder="请填写" min={0} precision={0} />
          </Form.Item>
          <Form.Item label="">
            <MinusOutlined />
          </Form.Item>
          <Form.Item
            label=""
            name={[VideoDTOKey, 'widthMax']}
            rules={[
              {
                validator(rule, value) {
                  const min = form.getFieldValue([VideoDTOKey, 'widthMin']);
                  console.log(min, value, 'min ');
                  if (Number(value) <= Number(min) && value !== null && value !== null) {
                    return Promise.reject('最大值必须大于最小值');
                  }
                  return Promise.resolve();
                },
              },
            ]}
            style={{ width: '100px' }}
          >
            <InputNumber placeholder="请填写" min={0} precision={0} />
          </Form.Item>
        </Space>
      </Form.Item>
      <Form.Item label="视频高度(px)" style={{ marginBottom: 0 }}>
        <Space align="start">
          <Form.Item label="" name={[VideoDTOKey, 'heightMin']} style={{ maxWidth: '100px' }}>
            <InputNumber placeholder="请填写" min={0} precision={0} />
          </Form.Item>
          <Form.Item label="">
            <MinusOutlined />
          </Form.Item>
          <Form.Item
            label=""
            name={[VideoDTOKey, 'heightMax']}
            rules={[
              {
                validator(rule, value) {
                  const min = form.getFieldValue([VideoDTOKey, 'heightMin']);
                  console.log(min, value, 'min ');
                  if (Number(value) <= Number(min) && value !== null && value !== null) {
                    return Promise.reject('最大值必须大于最小值');
                  }
                  return Promise.resolve();
                },
              },
            ]}
            style={{ width: '100px' }}
          >
            <InputNumber placeholder="请填写" min={0} precision={0} />
          </Form.Item>
        </Space>
      </Form.Item>
    </div>
  );
};

export default VideoConfig;
