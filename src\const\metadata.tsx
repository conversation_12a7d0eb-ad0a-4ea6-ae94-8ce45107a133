import {
  ClockCircleFilled,
  DatabaseFilled,
  EnvironmentFilled,
  FieldNumberOutlined,
  FileTextFilled,
  FontSizeOutlined,
  PictureFilled,
  VideoCameraFilled,
} from '@ant-design/icons';
import { FileAudioFilled } from '@gwy/components-web';

/**
 * 数据单元类型
 */
export enum MetaUnitType {
  General = 'GENERAL',
  Normal = 'NORMAL',
}

/**
 * 通用数据单元类型，如公司、部门、岗位、用户
 */
export enum MetaUnitGeneralType {
  Company = 'COMPANY',
  Department = 'DEPARTMENT',
  Post = 'POST',
  User = 'USER',
}

/**
 * 数据单元状态
 */
export enum MetaUnitStatus {
  // 审批通过
  Approved = 'APPROVED',
  // 失效
  Invalid = 'INVALID',
  // 审核中
  PendingApproval = 'PENDING_APPROVAL',
  // 审批拒绝
  Rejected = 'REJECTED',
  // 暂存
  Stash = 'STASH',
}

/**
 * 数据单元表单应用状态
 */
export enum MetaAppStatus {
  // 审批通过
  Approved = 'APPROVED',
  // 失效
  Invalid = 'INVALID',
  // 审核中
  PendingApproval = 'PENDING_APPROVAL',
  // 审批拒绝
  Rejected = 'REJECTED',
  // 暂存
  Stash = 'STASH',
}

// 标签类型
export enum MetaTagType {
  // 系统标签
  System = 'SYSTEM',
  // 自定义标签
  Normal = 'NORMAL',
  // 特殊标签
  Special = 'SPECIAL',
}

// 数元类型
export enum MetaDataType {
  Number = 'NUMBER', // 数字
  Text = 'TEXT', // 文本
  DateTime = 'DATETIME', // 时间
  Object = 'OBJECT', // 对象
  Unit = 'UNIT', // 单位
  Code = 'CODE', // 编码
  Picture = 'PICTURE', // 图片
  Address = 'ADDRESS', // 地址
  File = 'FILE', // 文件
  Video = 'VIDEO', // 视频
  Audio = 'AUDIO', // 音频
}

// 数元分组
export enum MetaDataGroup {
  Base = 'BASE', // 基础数元
  Business = 'BUSINESS', // 业务数元
}

// 数元分组列表
export const metaDataGroupList = [
  {
    value: MetaDataGroup.Base,
    label: '基础数元',
    desc: '是以基础的数据类型为集合的数元分类',
  },
  {
    value: MetaDataGroup.Business,
    label: '业务数元',
    desc: '带有一定业务场景的数元分类',
  },
];

// 数元列表
export const metaDataListOptions = [
  {
    dataType: MetaDataType.Text,
    name: '文本',
    icon: <FontSizeOutlined />,
    type: MetaDataGroup.Base,
  },
  {
    dataType: MetaDataType.Number,
    name: '数字',
    icon: <span style={{ fontSize: '1.2em' }}>8</span>,
    type: MetaDataGroup.Base,
  },
  {
    dataType: MetaDataType.DateTime,
    name: '时间',
    icon: <ClockCircleFilled />,
  },
  {
    dataType: MetaDataType.Object,
    name: '对象',
    icon: <DatabaseFilled />,
    type: MetaDataGroup.Base,
  },
  {
    dataType: MetaDataType.Unit,
    name: '单位',
    icon: <span style={{ fontSize: '0.8em' }}>Kg</span>,
    type: MetaDataGroup.Business,
  },
  {
    dataType: MetaDataType.Code,
    name: '编码',
    icon: <FieldNumberOutlined />,
    type: MetaDataGroup.Business,
  },
  {
    dataType: MetaDataType.Picture,
    name: '图片',
    icon: <PictureFilled />,
    type: MetaDataGroup.Base,
  },
  {
    dataType: MetaDataType.Address,
    name: '地址',
    icon: <EnvironmentFilled />,
    type: MetaDataGroup.Business,
  },
  {
    dataType: MetaDataType.File,
    name: '文件',
    icon: <FileTextFilled />,
    type: MetaDataGroup.Base,
  },
  {
    dataType: MetaDataType.Video,
    name: '视频',
    icon: <VideoCameraFilled />,
    type: MetaDataGroup.Base,
  },
  {
    dataType: MetaDataType.Audio,
    name: '音频',
    icon: <FileAudioFilled />,
    type: MetaDataGroup.Base,
  },
];

// 文本----------------------start
// 输入方式
export enum TextInputType {
  manual = 'MANUAL', // 手动输入
  enum = 'ENUM', // 枚举选择
}

// 输入方式选项
export const textInputTypeOptions = [
  { label: '手动输入', value: TextInputType.manual },
  { label: '枚举选择', value: TextInputType.enum },
];

// 枚举类型
export enum EnumType {
  general = 'GENERAL', // 枚举库
  custom = 'CUSTOM', // 自定义
}

// 枚举类型选项
export const enumTypeOptions = [
  { label: '枚举库', value: EnumType.general },
  { label: '自定义', value: EnumType.custom },
];

// 文本包含
export enum TextCharType {
  chinese = 'CHINESE', // 中文
  english_lower = 'ENGLISH_LOWERCASE', // 小写英文
  english_upper = 'ENGLISH_UPPERCASE', // 大写英文
  number = 'NUMBER', // 数字
  symbol = 'SYMBOL', // 符号
  link = 'LINK', // 链接
  emoji = 'EXPRESSION', // 表情
}

// 文本包含选项
export const textCharTypeOptions = [
  { label: '中文', value: TextCharType.chinese, regExp: /[\u4e00-\u9fa5]*/g },
  { label: '小写英文', value: TextCharType.english_lower, regExp: /[a-z]*/g },
  { label: '大写英文', value: TextCharType.english_upper, regExp: /[A-Z]*/g },
  { label: '数字', value: TextCharType.number, regExp: /\d*/g },
  { label: '符号', value: TextCharType.symbol, regExp: /[\p{P}\p{S}]/gu },
  { label: '链接', value: TextCharType.link, regExp: /https?:\/\/[^\s]+/g },
  { label: '表情', value: TextCharType.emoji, regExp: /[\u{1F300}-\u{1F5FF}\u{1F600}-\u{1F64F}\u{1F680}-\u{1F6FF}]/gu },
];
// 文本----------------------end

// 数字----------------------start
// 数值范围
export enum NumberRangeType {
  unlimited = 'UNLIMITED', // 不限
  lt = 'LT', // 小于
  lte = 'LTE', // 小于等于
  gt = 'GT', // 大于
  gte = 'GTE', // 大于等于
  between = 'BETWEEN', // 在范围内
  out = 'OUT', // 在范围外
}

// 数值范围选项
export const numberRangeTypeOptions = [
  { label: '不限', value: NumberRangeType.unlimited },
  { label: '小于', value: NumberRangeType.lt },
  { label: '小于等于', value: NumberRangeType.lte },
  { label: '大于', value: NumberRangeType.gt },
  { label: '大于等于', value: NumberRangeType.gte },
  { label: '在范围内', value: NumberRangeType.between },
  { label: '在范围外', value: NumberRangeType.out },
];
// 数字----------------------end

// 时间----------------------start
// 时间范围
export enum TimeRangeType {
  unlimited = 'UNLIMITED', // 不限
  before = 'BEFORE', // 早于
  after = 'AFTER', // 晚于
  between = 'BETWEEN', // 周期
}

// 时间范围选项
export const timeRangeTypeOptions = [
  { label: '不限', value: TimeRangeType.unlimited },
  { label: '早于', value: TimeRangeType.before },
  { label: '晚于', value: TimeRangeType.after },
  { label: '周期', value: TimeRangeType.between },
];
// 时间----------------------end

// 对象----------------------start
export enum ObjectDTOType {
  GENERAL = 'GENERAL', // 通用
  NORMAL = 'NORMAL', // 普通
}

export const objectDTOTypeOptions = [
  { label: '通用', value: ObjectDTOType.GENERAL },
  { label: '普通', value: ObjectDTOType.NORMAL },
];

// 企业范围类型
export enum OrgRangeType {
  PLATFORM_ALL_ORG = 'PLATFORM_ALL_ORG', // 平台全部企业
  TOP_BLOC_ALL_ORG = 'TOP_BLOC_ALL_ORG', // 所有顶级集团下的全部企业
  BLOC_ALL_ORG = 'BLOC_ALL_ORG', // 所有集团的全部企业
  CURRENT_TOP_BLOC_ALL_ORG = 'CURRENT_TOP_BLOC_ALL_ORG', // 当前顶级集团下的全部企业
  CURRENT_AND_SUB_BLOC_ALL_ORG = 'CURRENT_AND_SUB_BLOC_ALL_ORG', // 当前集团下的所有企业
  CURRENT_BLOC_ALL_ORG = 'CURRENT_BLOC_ALL_ORG', // 当前集团的所有企业
  CURRENT_ORG = 'CURRENT_ORG', // 当前企业
}

// 企业范围类型选项
export const orgRangeTypeOptions = [
  // { label: '平台全部企业', value: OrgRangeType.PLATFORM_ALL_ORG },
  // { label: '所有顶级集团下的全部企业', value: OrgRangeType.TOP_BLOC_ALL_ORG },
  // { label: '所有集团的全部企业', value: OrgRangeType.BLOC_ALL_ORG },
  // { label: '当前顶级集团下的全部企业', value: OrgRangeType.CURRENT_TOP_BLOC_ALL_ORG },
  // { label: '当前集团下的所有企业', value: OrgRangeType.CURRENT_AND_SUB_BLOC_ALL_ORG },
  // { label: '当前集团的所有企业', value: OrgRangeType.CURRENT_BLOC_ALL_ORG },
  { label: '当前企业', value: OrgRangeType.CURRENT_ORG },
];

// 部门范围类型
export enum DeptRangeType {
  PLATFORM_ALL_DEPT = 'PLATFORM_ALL_DEPT', // 平台全部部门
  CURRENT_BLOC_ALL_DEPT = 'CURRENT_BLOC_ALL_DEPT', // 当前集团的全部部门
  CURRENT_ORG_ALL_DEPT = 'CURRENT_ORG_ALL_DEPT', // 当前公司的全部部门
  CURRENT_AND_SUB_DEPT = 'CURRENT_AND_SUB_DEPT', // 当前部门及下属部门
  CURRENT_DEPT = 'CURRENT_DEPT', // 当前部门
}

// 部门范围类型选项
export const deptRangeTypeOptions = [
  // { label: '平台全部部门', value: DeptRangeType.PLATFORM_ALL_DEPT },
  // { label: '当前集团的全部部门', value: DeptRangeType.CURRENT_BLOC_ALL_DEPT },
  { label: '当前公司的全部部门', value: DeptRangeType.CURRENT_ORG_ALL_DEPT },
  { label: '当前部门及下属部门', value: DeptRangeType.CURRENT_AND_SUB_DEPT },
  { label: '当前部门', value: DeptRangeType.CURRENT_DEPT },
];

// 岗位范围类型
export enum PostRangeType {
  PLATFORM_ALL_POST = 'PLATFORM_ALL_POST', // 平台全部岗位
  BLOC_ALL_POST = 'BLOC_ALL_POST', // 当前集团的全部岗位
  BLOC_ALL_DISPLACE_POST = 'BLOC_ALL_DISPLACE_POST', // 当前集团的全部顶岗岗位
  ORG_ALL_POST = 'ORG_ALL_POST', // 当前公司的全部岗位
  ORG_ALL_DISPLACE_POST = 'ORG_ALL_DISPLACE_POST', // 当前公司的全部顶岗岗位
  CURRENT_AND_SUB_POST = 'CURRENT_AND_SUB_POST', // 当前岗位及下属岗位
  CURRENT_POST = 'CURRENT_POST', // 当前岗位
}

// 岗位范围类型选项
export const postRangeTypeOptions = [
  // { label: '平台全部岗位', value: PostRangeType.PLATFORM_ALL_POST },
  // { label: '当前集团的全部岗位', value: PostRangeType.BLOC_ALL_POST },
  // { label: '当前集团的全部顶岗岗位', value: PostRangeType.BLOC_ALL_DISPLACE_POST },
  { label: '当前公司的全部岗位', value: PostRangeType.ORG_ALL_POST },
  { label: '当前公司的全部顶岗岗位', value: PostRangeType.ORG_ALL_DISPLACE_POST },
  { label: '当前岗位及下属岗位', value: PostRangeType.CURRENT_AND_SUB_POST },
  { label: '当前岗位', value: PostRangeType.CURRENT_POST },
];

// 用户范围类型
export enum UserRangeType {
  PLATFORM_ALL_USER = 'PLATFORM_ALL_USER', // 平台全部用户
  CURRENT_BLOC_ALL_USER = 'CURRENT_BLOC_ALL_USER', // 当前集团全部用户
  CURRENT_ORG_ALL_USER = 'CURRENT_ORG_ALL_USER', // 当前公司全部用户
  CURRENT_AND_SUB_USER = 'CURRENT_AND_SUB_USER', // 当前用户及下属成员
  CURRENT_USER = 'CURRENT_USER', // 当前用户
}

// 用户范围类型选项
export const userRangeTypeOptions = [
  // { label: '平台全部用户', value: UserRangeType.PLATFORM_ALL_USER },
  // { label: '当前集团全部用户', value: UserRangeType.CURRENT_BLOC_ALL_USER },
  { label: '当前公司全部用户', value: UserRangeType.CURRENT_ORG_ALL_USER },
  // { label: '当前用户及下属成员', value: UserRangeType.CURRENT_AND_SUB_USER },
  { label: '当前用户', value: UserRangeType.CURRENT_USER },
];

export const ObjectRangeTypes = {
  [MetaUnitGeneralType.Company]: {
    label: '企业',
    options: orgRangeTypeOptions,
  },
  [MetaUnitGeneralType.Department]: {
    label: '部门',
    options: deptRangeTypeOptions,
  },
  [MetaUnitGeneralType.Post]: {
    label: '岗位',
    options: postRangeTypeOptions,
  },
  [MetaUnitGeneralType.User]: {
    label: '用户',
    options: userRangeTypeOptions,
  },
};

// 对象----------------------end

// 单位----------------------start
// 重量单位
export enum WeightUnit {
  ton = 'TON', // 吨
  kg = 'KILOGRAM', // 千克
  g = 'GRAM', // 克
  jin = 'JIN', // 斤
  liang = 'LIANG', // 斤两
}

// 重量单位选项（要按从大到小排列）
export const weightUnitOptions = [
  { label: '吨', value: WeightUnit.ton },
  { label: '千克', value: WeightUnit.kg },
  { label: '克', value: WeightUnit.g },
  { label: '斤', value: WeightUnit.jin },
  { label: '两', value: WeightUnit.liang },
];

// 长度单位
export enum LengthUnit {
  km = 'KILOMETER', // 千米
  mile = 'MILE', // 里
  m = 'METER', // 米
  dm = 'DECIMETER', // 分米
  cm = 'CENTIMETER', // 厘米
  mm = 'MILLIMETER', // 毫米
  inch = 'INCH', // 寸
}

// 长度单位选项（要按从大到小排列）
export const lengthUnitOptions = [
  { label: '千米', value: LengthUnit.km },
  { label: '里', value: LengthUnit.mile },
  { label: '米', value: LengthUnit.m },
  { label: '分米', value: LengthUnit.dm },
  { label: '寸', value: LengthUnit.inch },
  { label: '厘米', value: LengthUnit.cm },
  { label: '毫米', value: LengthUnit.mm },
];

// 面积单位
export enum AreaUnit {
  km2 = 'SQUARE_KILOMETERS', // 平方公里
  m2 = 'SQUARE_METERS', // 平方米
  acres = 'ACRES', // 亩
  hectares = 'HECTARES', // 公顷
}

// 面积单位选项（要按从大到小排列）
export const areaUnitOptions = [
  { label: '平方公里', value: AreaUnit.km2 },
  { label: '公顷', value: AreaUnit.hectares },
  { label: '亩', value: AreaUnit.acres },
  { label: '平方米', value: AreaUnit.m2 },
];

// 体积单位
export enum VolumeUnit {
  m3 = 'CUBIC_METER', // 立方米
  l = 'LITER', // 升
  ml = 'MILLILITER', // 毫升
}

// 体积单位选项（要按从大到小排列）
export const volumeUnitOptions = [
  { label: '立方米', value: VolumeUnit.m3 },
  { label: '升', value: VolumeUnit.l },
  { label: '毫升', value: VolumeUnit.ml },
];

// 金额单位
export enum AmountUnit {
  yuan = 'YUAN', // 元
  tenThousandYuan = 'TEN_THOUSAND_YUAN', // 万元
  millionYuan = 'MILLION_YUAN', // 百万元
}

// 金额单位选项（要按从大到小排列）
export const amountUnitOptions = [
  { label: '百万元', value: AmountUnit.millionYuan },
  { label: '万元', value: AmountUnit.tenThousandYuan },
  { label: '元', value: AmountUnit.yuan },
];

// 单位类型
export enum UnitType {
  weight = 'WEIGHT', // 重量
  length = 'LENGTH', // 长度
  area = 'AREA', // 面积
  volume = 'VOLUME', // 体积
  amount = 'AMOUNT', // 金额
}

// 单位类型选项
export const unitTypeOptions: { label: string; value: UnitType; options: { label: string; value: string }[] }[] = [
  { label: '重量', value: UnitType.weight, options: weightUnitOptions },
  { label: '长度', value: UnitType.length, options: lengthUnitOptions },
  { label: '面积', value: UnitType.area, options: areaUnitOptions },
  { label: '体积', value: UnitType.volume, options: volumeUnitOptions },
  { label: '金额', value: UnitType.amount, options: amountUnitOptions },
];
// 单位----------------------end

// 编码----------------------start
// 编码类型
export enum CodeType {
  custom = 'CUSTOM', // 自定义编码
  normal = 'NORMAL', // 常用编码
}

// 编码类型选项
export const codeTypeOptions = [
  { label: '自定义编码', value: CodeType.custom },
  { label: '常用编码', value: CodeType.normal },
];

// 常用编码
export enum CommonCode {
  idCard = 'ID_CARD', // 身份证
  phone = 'PHONE_NUMBER', // 手机号
  tel = 'LAND_LINE', // 座机
  enterprise_code = 'ENTERPRISE_CODE', // 社会统一信用代码
}

// 常用编码选项
export const commonCodeOptions = [
  { label: '身份证', value: CommonCode.idCard, regExp: /^[1-9]\d{5}(18|19|20|21|22)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|[Xx])$/ },
  { label: '手机号', value: CommonCode.phone, regExp: /^1[3-9]\d{9}$/ },
  { label: '座机', value: CommonCode.tel, regExp: /^(\d{3,4}-)?\d{5,8}$/ },
  { label: '社会统一信用代码', value: CommonCode.enterprise_code, regExp: /^[0-9A-Z]{18}$/ },
];

// 自定义编码选项
export enum CustomCode {
  chinese = 'CHINESE', // 中文
  number = 'NUMBER', // 数字
  symbol = 'SYMBOL', // 符号
  english_lower = 'ENGLISH_LOWERCASE', // 小写英文
  english_upper = 'ENGLISH_UPPERCASE', // 大写英文
  date = 'DATE', // 日期
}

// 自定义编码选项
export const customCodeOptions = [
  { label: '中文', value: CustomCode.chinese },
  { label: '数字', value: CustomCode.number },
  { label: '符号', value: CustomCode.symbol },
  { label: '小写英文', value: CustomCode.english_lower },
  { label: '大写英文', value: CustomCode.english_upper },
  { label: '日期', value: CustomCode.date },
];

// 数字生成
export enum CodeNumberType {
  increment = 'INCREMENT', // 自增
  random = 'RANDOM', // 随机
}

// 数字生成选项
export const codeNumberTypeOptions = [
  { label: '自增', value: CodeNumberType.increment },
  { label: '随机', value: CodeNumberType.random },
];

// 日期类型
export enum CodeDateType {
  unlimited = 'UNLIMITED', // 不限
  year = 'YEAR', // 年
  month = 'MONTH', // 月
  day = 'DAY', // 日
  year_month = 'YEAR_MONTH', // 年/月
  date = 'DATE', // 年/月/日
  date_hour = 'DATE_HOUR', // 年/月/日/时
  date_hour_minute = 'DATE_HOUR_MINUTE', // 年/月/日/时/分
  date_time = 'DATE_TIME', // 年/月/日/时/分/秒
  time = 'TIME', // 时/分/秒
}

// 日期类型选项
export const codeDateTypeOptions = [
  { label: '不限', value: CodeDateType.unlimited },
  { label: '年', value: CodeDateType.year, format: 'YYYY' },
  { label: '月', value: CodeDateType.month, format: 'MM' },
  { label: '日', value: CodeDateType.day, format: 'DD' },
  { label: '年-月', value: CodeDateType.year_month, format: 'YYYY-MM' },
  { label: '年-月-日', value: CodeDateType.date, format: 'YYYY-MM-DD' },
  { label: '年-月-日 时', value: CodeDateType.date_hour, format: 'YYYY-MM-DD HH' },
  { label: '年-月-日 时:分', value: CodeDateType.date_hour_minute, format: 'YYYY-MM-DD HH:mm' },
  { label: '年-月-日 时:分:秒', value: CodeDateType.date_time, format: 'YYYY-MM-DD HH:mm:ss' },
  { label: '时:分:秒', value: CodeDateType.time, format: 'HH:mm:ss' },
];

// 编码----------------------end

// 图片类型----------------------start
export enum ImageType {
  PNG = 'PNG',
  JPG = 'JPG',
  BMP = 'BMP',
  SVG = 'SVG',
  WEBP = 'WEBP',
  GIF = 'GIF',
}

export const ImageTypeList = [
  { label: 'PNG', value: ImageType.PNG },
  { label: 'JPG', value: ImageType.JPG },
  { label: 'BMP', value: ImageType.BMP },
  { label: 'SVG', value: ImageType.SVG },
  { label: 'WEBP', value: ImageType.WEBP },
  { label: 'GIF', value: ImageType.GIF },
];

export enum sizeUnit {
  KB = 'KB',
  MB = 'MB',
  // GB = 'GB',
}

export const sizeUnitList = [
  { label: 'KB', value: sizeUnit.KB },
  { label: 'MB', value: sizeUnit.MB },
  // { label: 'GB', value: sizeUnit.GB },
];

// 图片类型 ---------------end

// 地址----------------------start
// 地址类型
export enum AddressType {
  accurate = 'ACCURATE', // 精确地址
  area = 'AREA', // 行政区划
  detail = 'DETAIL', // 详细地址
}

// 编码类型选项
export const addressTypeOptions = [
  { label: '行政区划', value: AddressType.area },
  { label: '详细地址', value: AddressType.detail },
  { label: '精确地址', value: AddressType.accurate },
];

// 地址范围
export enum RangeType {
  unlimited = 'UNLIMITED', // 不限
  contain = 'CONTAIN', // 包含
  exclude = 'EXCLUDE', // 排除
}

// 编码类型选项
export const rangeTypeOptions = [
  { label: '不限制', value: RangeType.unlimited },
  { label: '包含', value: RangeType.contain },
  { label: '排除', value: RangeType.exclude },
];

// 地址 ---------------end

// 文件类型----------------------start

// 文件格式
export enum FileFormats {
  // 文档
  ZIP = 'ZIP',
  RAR = 'RAR',
  DOC = 'DOC',
  DOCX = 'DOCX',
  XLSX = 'XLSX',
  XLS = 'XLS',
  PDF = 'PDF',
  PPT = 'PPT',
  // 图片
  JPG = 'JPG',
  PNG = 'PNG',
  BMP = 'BMP',
  SVG = 'SVG',
  WEBP = 'WEBP',
  GIF = 'GIF',
  // 视频
  MP4 = 'MP4',
  AVI = 'AVI',
  MOV = 'MOV',
  WMV = 'WMV',
  FLV = 'FLV',
  GP3 = '3GP',
  MKV = 'MKV',
  ASF = 'ASF',
  // 音频
  MP3 = 'MP3',
  WAV = 'WAV',
  APE = 'APE',
  WMA = 'WMA',
  FLAC = 'FLAC',
  AAC = 'AAC',
  MIDI = 'MIDI',
  OGG = 'OGG',
}

// 文件类型
export enum FileTypes {
  // 图片
  IMAGE = 'IMAGE',
  // 文档
  FILE = 'FILE',
  // 音频
  AUDIO = 'AUDIO',
  // 视频
  VIDEO = 'VIDEO',
}
// 文件类型选项
export const FileFormatsOptions = [
  {
    label: '文档',
    value: FileTypes.FILE,
    formats: [
      FileFormats.ZIP,
      FileFormats.RAR,
      FileFormats.DOC,
      FileFormats.DOCX,
      FileFormats.XLSX,
      FileFormats.XLS,
      FileFormats.PDF,
      FileFormats.PPT,
    ],
  },
  {
    label: '图片',
    value: FileTypes.IMAGE,
    formats: [FileFormats.JPG, FileFormats.PNG, FileFormats.BMP, FileFormats.SVG, FileFormats.WEBP, FileFormats.GIF],
  },
  {
    label: '视频',
    value: FileTypes.VIDEO,
    formats: [FileFormats.MP4, FileFormats.AVI, FileFormats.MOV, FileFormats.WMV, FileFormats.FLV, FileFormats.GP3, FileFormats.MKV, FileFormats.ASF],
  },
  {
    label: '音频',
    value: FileTypes.AUDIO,
    formats: [FileFormats.MP3, FileFormats.WAV, FileFormats.APE, FileFormats.FLAC, FileFormats.AAC, FileFormats.MIDI, FileFormats.OGG],
  },
];

export enum FileSizeUnit {
  KB = 'KB',
  MB = 'MB',
  GB = 'GB',
}

export const FileSizeUnitList = [
  { label: 'KB', value: FileSizeUnit.KB },
  { label: 'MB', value: FileSizeUnit.MB },
  { label: 'GB', value: FileSizeUnit.GB },
];

// 文件类型 ---------------end

// 视频类型----------------------start
export enum VideoType {
  MP4 = 'MP4',
  AVI = 'AVI',
  MOV = 'MOV',
  WMV = 'WMV',
  FLV = 'FLV',
  GP3 = 'GP3',
  MKV = 'MKV',
  ASF = 'ASF',
}
// 视频格式选项
export const videoTypeList = [
  { label: 'MP4', value: VideoType.MP4 },
  { label: 'AVI', value: VideoType.AVI },
  { label: 'MOV', value: VideoType.MOV },
  { label: 'WMV', value: VideoType.WMV },
  { label: 'FLV', value: VideoType.FLV },
  { label: '3GP', value: VideoType.GP3 },
  { label: 'MKV', value: VideoType.MKV },
  { label: 'ASF', value: VideoType.ASF },
];

// 视频类型 ---------------end

// 音频类型----------------------start
export enum AudioType {
  MP3 = 'MP3',
  WAV = 'WAV',
  APE = 'APE',
  WMA = 'WMA',
  FLAC = 'FLAC',
  AAC = 'AAC',
  MIDI = 'MIDI',
  OGG = 'OGG',
}
// 音频格式选项
export const audioTypeList = [
  { label: 'MP3', value: AudioType.MP3 },
  { label: 'WAV', value: AudioType.WAV },
  { label: 'APE', value: AudioType.APE },
  { label: 'WMA', value: AudioType.WMA },
  { label: 'FLAC', value: AudioType.FLAC },
  { label: 'AAC', value: AudioType.AAC },
  { label: 'MIDI', value: AudioType.MIDI },
  { label: 'OGG', value: AudioType.OGG },
];

// 音频类型 ---------------end
