import { OperateType } from '@/const/common';
import React from 'react';

export const MetaDataContext = React.createContext<{
  metaData?: any; // 当前版本（例如：正在编辑的版本
  metaDataConfig?: any; // 数元配置
  metaDataConfigApproved?: any; // 数元配置-审批过的
  hasApproved?: boolean; // 是否存在审批通过的版本
  operateType?: OperateType; // 操作类型
  disabled?: boolean; // 是否禁用
  isOrgMeta?: boolean; // 是否是公司数据元
  tagInfo?: any;
}>({});
