import { Bridge, MicroRoute } from '@gwy/libs-web';
import dayjs from 'dayjs';

export * as metadataUtils from './metadata';

/**
 * 格式化时间
 * 格式: 年-月-日 时:分，示例：2023-01-01 01:01、01-01 01:01（如果是当年的话，不显示年份）
 */
export const formatDateTime = (dateTimeStr) => {
  if (dateTimeStr === null || dateTimeStr === '') {
    return '';
  }
  const dateTime = dayjs(dateTimeStr);
  let formatStr = 'YYYY-MM-DD HH:mm';

  // 当年时间，不展示年份
  if (dateTime.year() === dayjs().year()) {
    formatStr = 'MM-DD HH:mm';
  }

  return dateTime.format(formatStr);
};

/*
 * 生成唯一ID
 * params
 * len: 长度，值范围 0-32, 默认 32
 */
export const genUuid = (len = 32) => {
  const result: any = [];
  const hexDigits = '0123456789abcdefghijklmnopqrstuvwxyz';

  for (let i = 0; i < 36; i++) {
    result[i] = hexDigits[Math.floor(Math.random() * 0x24)];
  }

  result[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
  result[19] = hexDigits[(result[19] & 0x3) | 0x8]; // bits 6-7 of the clock_seq_hi_and_reserved to 01
  // eslint-disable-next-line no-multi-assign
  result[8] = result[13] = result[18] = result[23] = '';

  const uuid = result.join('');
  return uuid.slice(0, len);
};

export const deepEqual = (a, b) => {
  // 处理 null 和 undefined
  if (a === null || b === null) return a === b;

  // 处理基本类型（包括字符串与数字的转换）
  if (typeof a !== 'object' && typeof b !== 'object') {
    // 字符串和数字互转比较
    if (typeof a === 'string' && typeof b === 'number') {
      return a === b.toString() || Number(a) === b;
    }
    if (typeof a === 'number' && typeof b === 'string') {
      return a === Number(b) || a.toString() === b;
    }
    // 其他基本类型直接比较
    return a === b;
  }

  // 处理数组
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (!deepEqual(a[i], b[i])) return false;
    }
    return true;
  }

  // 处理对象
  if (typeof a === 'object' && typeof b === 'object') {
    const keysA = Object.keys(a),
      keysB = Object.keys(b);
    // if (keysA.length !== keysB.length) return false;
    for (const key of keysA) {
      // 只比较前后的交集键，这些是我们想要的，多出的键，可能为后端补充，可能是前端自行处理添加
      if (keysB.includes(key)) {
        if (!deepEqual(a[key], b[key])) return false;
      } else {
        console.log('keysA', keysA, 'keysB', keysB);
      }
    }
    return true;
  }

  // 其他情况（如一个为对象，一个为基本类型）
  return false;
};

// 水平布局表单label、content宽度
export const getFormLayoutCol = (labelWidth: number | string) => ({
  labelCol: {
    style: {
      width: labelWidth,
    },
  },
  wrapperCol: {
    style: {
      width: `calc(100% - ${labelWidth}${typeof labelWidth === 'number' ? 'px' : ''}`,
    },
  },
});

// 创建打开应用页面方法（为了打开页面时state传参类型）（使用方式可参考openMicroApp定义）
export function genOpenMicroApp<T = never>(path: string) {
  return (
    bridge: Bridge,
    config?: Omit<MicroRoute, 'state' | 'path'> & {
      state?: T;
      pathParams?: string;
    },
  ) => {
    const { pathParams, ...restConfig } = config || {};
    bridge.open({
      ...restConfig,
      path: (path.match(/^\/?datasource/) ? path : `/datasource${path.startsWith('/') ? '' : '/'}${path}`) + (pathParams || ''),
    });
  };
}
