// 运行时配置

import { ConfigProvider, message } from '@gwy/components-web';
import { getErrorMsg, requestUtil } from '@gwy/libs-web';
import { history } from '@umijs/max';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

requestUtil.setGlobalConfig({
  onError: (error, config, response) => {
    console.log(error, config, response);
    if (!config?.isCancelErrorAlert) {
      message.error(getErrorMsg(error) || '操作失败');
    }
  },
});

dayjs.locale('zh-cn');

export function rootContainer(container, args) {
  return <ConfigProvider locale={zhCN}>{container}</ConfigProvider>;
}

export const qiankun = {
  // 应用加载之前
  async bootstrap(props) {
    console.log('app1 bootstrap', props);
  },
  // 应用 render 之前触发
  async mount(props) {
    console.log(history, 'app1 mount', props);
  },
  // 应用 render 之后触发
  async afterMount(props) {
    console.log(history, 'app1 afterMount', props);
  },
  // 应用卸载之后触发
  async unmount(props) {
    console.log('app1 unmount', props);
  },
};
