// 岗位性质
export enum POST_PROPERTY {
  EXECUTE = 'EXECUTE', // 执行岗
  MANAGE_EXECUTE = 'MANAGE_EXECUTE', // 管理执行岗
  MANAGE = 'MANAGE', // 管理岗
  TOP = 'TOP', // 顶级岗位（法人岗）
  ADMIN1 = 'ADMIN1', // 管理员1
  ADMIN2 = 'ADMIN2', // 管理员2
  CHIEF_SUPERVISOR = 'CHIEF_SUPERVISOR', // 监事长岗
  SUPERVISOR_MEMBER = 'SUPERVISOR_MEMBER', // 监事岗
  BOARD_CHAIRMAN = 'BOARD_CHAIRMAN', // 董事长岗
  BOARD_MEMBER = 'BOARD_MEMBER', // 董事岗
}

export type Post = {
  postId: string;
  postName: string;
  departmentId?: string;
  departmentName?: string;
  orgId?: string;
  orgName?: string;
  orgShortName?: string;
  [property: string]: any;
};
