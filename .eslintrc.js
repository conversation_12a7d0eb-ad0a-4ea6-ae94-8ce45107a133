module.exports = {
  extends: require.resolve('@umijs/max/eslint'),
  rules: {
    '@typescript-eslint/no-unused-vars': 1,
    '@typescript-eslint/no-unused-expressions': 0,
    '@typescript-eslint/no-use-before-define': 0,
    'react/no-string-refs': 0,
    'no-param-reassign': 0,
    'no-var': 0,
    'array-callback-return': 0,
    'no-useless-escape': 0,
    'no-async-promise-executor': 0,
    'react-hooks/rules-of-hooks': 'warn',
    'react-hooks/exhaustive-deps': 'warn',
  },
};
