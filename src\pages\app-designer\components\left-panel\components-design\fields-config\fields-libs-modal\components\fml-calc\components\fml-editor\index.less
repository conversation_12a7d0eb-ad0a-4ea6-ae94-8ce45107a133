.disabledContainer {
  pointer-events: none;
}

.container {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.title {
  height: 32px;
  line-height: 32px;
  padding-left: 16px;
  background-color: #f4f6f7;
  flex-shrink: 0;
}

.content {
  height: 100px;
  padding: 16px 32px;
  flex-shrink: 0;
  overflow: auto;

  .tag,
  .field {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 8px;
    border-radius: 2px;
    border: 1px solid #e5e6eb;
    text-align: center;
  }

  .tag {
    cursor: pointer;
  }
}

.operatorWrap {
  padding: 16px 32px;
}

.operateContent {
  display: flex;
  flex-grow: 1;
  justify-content: flex-start;
  border-top: 1px solid #ccc;
  overflow: hidden;

  .operateBox {
    flex: 3;
    overflow: auto;
  }
}

.tagBox {
  display: flex;
  position: relative;
  width: 100%;
  padding: 6px 8px;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  line-height: 16px;
  color: #333;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  transition: all 0.24s;
  align-self: flex-start;

  &:hover {
    border-color: #40a9ff;
    color: #40a9ff;
  }

  &.configured {
    color: #52c41a;
    border-color: #52c41a;
  }

  &.error {
    color: #ff4d4f;
    border-color: #ff4d4f;
  }

  &.isSelect {
    border-color: #096dd9;
    color: #096dd9;
  }

  &.isGroup {
    border: none;
  }
}

.lightTag {
  display: inline-block;
  padding: 0 4px;
  color: #4a8ef2;
  border: 1px solid #4a8ef2;
  border-radius: 4px;
  background-color: rgba(74, 142, 242, 10%);
  text-align: center;
  cursor: pointer;
}

.tagsBox {
  height: 100%;
  flex-basis: 290px;
  flex-shrink: 0;
  border-right: 1px solid #ccc;
  min-width: 0;

  .tabsLabel {
    width: 64px;
    text-align: center;
  }

  .tabBox {
    height: 100%;
    padding: 12px 8px;

    :global {
      .ant-tabs-small > .ant-tabs-nav .ant-tabs-tab {
        padding: 5px 0 !important;
      }
    }

    // 定义ellipsis mixin (Less的mixin使用类选择器)
    .ellipsis() {
      width: 100%;
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
    }

    .tags {
      display: flex;
      flex-direction: column;
      column-gap: 12px;
      padding: 12px 0;
      height: calc(100% - 36px);
      overflow: auto;

      .tag {
        &.noAuth {
          color: red;
          font-style: italic;

          .tagName {
            margin-right: 2px;
          }

          i {
            color: red;
          }
        }

        .collapseOrg {
          .ellipseHeader {
            // 调用ellipsis mixin
            .ellipsis();

            font-size: 14px;
            font-weight: bold;
          }

          :global {
            .ant-collapse-header {
              padding: 0 !important;
            }

            .ant-collapse-content-box {
              display: flex;
              flex-wrap: wrap;
              justify-content: space-between;
              gap: 4%;
              margin-top: 6px;
              padding: 4px !important;
            }

            .ant-collapse-header-text {
              color: #1d2129 !important;
              width: 100%;
            }
          }
        }

        .collapse {
          width: 100%;

          .ellipseHeader {
            // 调用ellipsis mixin
            .ellipsis();
          }

          :global {
            .ant-collapse-header {
              padding: 0 !important;
            }

            .ant-collapse-content-box {
              display: flex;
              flex-wrap: wrap;
              justify-content: space-between;
              gap: 4%;
              margin-top: 6px;
              padding: 4px !important;
              padding-left: 23px !important;
            }

            .ant-collapse-header-text {
              color: #1d2129 !important;
              width: calc(100% - 24px);
            }

            .ant-collapse-item {
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }

    .collapseFields {
      width: 100%;
      margin-bottom: 8px;

      .ellipseHeader {
        // 调用ellipsis mixin
        .ellipsis();
      }

      :global {
        .ant-collapse-header {
          padding: 0 !important;
        }

        .ant-collapse-content-box {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          gap: 4%;
          margin-top: 6px;
          padding: 4px !important;
        }

        .ant-collapse-header-text {
          color: #1d2129 !important;
          width: 100%;
        }

        .ant-collapse-item {
          margin-bottom: 8px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .filterFieldsBox {
      margin-top: 8px;
      display: flex;
      flex-wrap: wrap;
      gap: 4%;
      max-height: calc(100% - 36px);
      overflow: auto;
    }
  }

  :global {
    .ant-tabs-content {
      height: 100% !important;
    }

    .ant-tabs-tabpane {
      height: 100% !important;
    }
  }
}
