import { dataWrapper, requestUtil } from '@gwy/libs-web';

/**
 * 获取有权限的数据单元及标签列表
 */
export const getDataUnitAndTag = ({ postId }) => dataWrapper(requestUtil.get(`gwy-datasource/api/data_units/${postId}/get_data_unit_and_tag`));

/**
 *
 * 获取数据单元分类列表
 */
export const getDatasourceCategoryList = (params = {}) => dataWrapper(requestUtil.get('gwy-datasource/api/data_unit_categories', { params }));

/**
 * 新增数据单元分类
 */

export const addDatasourceCategory = (params) => dataWrapper(requestUtil.post('gwy-datasource/api/data_unit_categories', params));

/**
 * 删除数据单元
 */
export const deleteDataUnit = (id) => dataWrapper(requestUtil.delete(`gwy-datasource/api/data_unit_versions/${id}`));

/**
 * 删除数据单元分类
 */
export const deleteDatasourceCategory = (categoryId) => dataWrapper(requestUtil.delete(`gwy-datasource/api/data_unit_categories/${categoryId}`));

/**
 * 修改数据单元分类
 */
export const updateDatasourceCategory = (categoryId, params) =>
  dataWrapper(requestUtil.put(`gwy-datasource/api/data_unit_categories/${categoryId}`, params));

/**
 * 排序数据单元分类
 */

export const sortDatasourceCategory = (params) => dataWrapper(requestUtil.put('gwy-datasource/api/data_unit_categories/sort', params));
/**
 * 获取数据单元列表
 */
export const getDatasourceList = (params = {}) => dataWrapper(requestUtil.get('gwy-datasource/api/data_units', { params }));

/**
 * 新版新增数据单元
 */
export const addDatasourceVersion = (params) => dataWrapper(requestUtil.post('gwy-datasource/api/data_unit_versions', params));

/**
 *提交数据单元审批
 */
export const submitDatasourceApprove = (id) => dataWrapper(requestUtil.put(`gwy-datasource/api/data_unit_versions/${id}/submit`));

/**
 * 获取某一数据单元版本的标签列表
 */

export const getVersionTagList = (versionId, params?: { systemFlag?: boolean }) =>
  dataWrapper(requestUtil.get(`gwy-datasource/api/data_unit_versions/${versionId}/tags`, { params }));

/**
 * 获取某一历史版本指定某些标签列表
 */

export const getHistoryVerTagListByIds = (preVersionId, params) =>
  dataWrapper(requestUtil.post(`gwy-datasource/api/data_unit_versions/${preVersionId}/tags/filter`, params));

/**
 * 查询数据单元指定版本基本信息
 */

export const getDataUnitVersionInfo = (id) => dataWrapper(requestUtil.get(`gwy-datasource/api/data_unit_versions/${id}`));

/**
 * 修改数据单元
 */

export const editDataUnit = (id, params) => dataWrapper(requestUtil.put(`gwy-datasource/api/data_unit_versions/${id}`, params));

/**
 * 取消暂存数据单元
 */

export const cancelStashDataUnit = (id) => dataWrapper(requestUtil.put(`gwy-datasource/api/data_unit_versions/${id}/stash/cancel`));
/**
 * 获取数据单元审批详情
 */
export const getDataUnitApproveDetail = (id) => dataWrapper(requestUtil.get(`gwy-datasource/api/approvals/data_units/tasks/${id}`));
/**
 * 审批通过
 */

export const approveDataUnitAgree = (id) => dataWrapper(requestUtil.put(`gwy-datasource/api/approvals/data_units/tasks/${id}/agree`));
/**
 * 审批拒绝
 */

export const approveDataUnitRefuse = (id, params) =>
  dataWrapper(requestUtil.put(`gwy-datasource/api/approvals/data_units/tasks/${id}/reject`, params));

/**
 * 查询历史版本
 */
export const getVeriosns = (id) => dataWrapper(requestUtil.get(`gwy-datasource/api/data_units/${id}/versions`));

/**
 * 获取用户指引列表 type:  0：未指引， 1：指引中， 2：指引成功
 */
export const getToursList = (type) => dataWrapper(requestUtil.get(`gwy-datasource/api/user_guidance?type=${type}`));

/**
 * 提交用户指引结果
 */
export const submitTourResult = (params) => dataWrapper(requestUtil.post(`gwy-datasource/api/user_guidance`, params));

/**
 *
 * 查询数据单元版本列表
 */
export const getDatasourceVersions = (params) => dataWrapper(requestUtil.get('gwy-datasource/datasource/getDatasourceVersionList', { params }));

/**
 *
 * 启用禁用数据单元
 */
export const openOrCloseDatasource = (params) => dataWrapper(requestUtil.post('gwy-datasource/datasource/openOrCloseDatasource', params));

/**
 * 创建数据单元副本
 */
export const createApprovePlan = (params = {}) => dataWrapper(requestUtil.get('gwy-datasource/datasource/create/datasource/approvePlan', { params }));

/**
 * 新增/修改数据单元
 */
export const addOrUpdateDatasource = (params = {}) =>
  dataWrapper(requestUtil.post('gwy-datasource/datasource/addOrUpdateDatasource', params, { isCancelErrorAlert: true }));

/**
 * 暂存数据单元
 */
export const tempSaveDatasource = (params = {}) =>
  dataWrapper(requestUtil.post('gwy-datasource/datasource/tempSave', params, { isCancelErrorAlert: true }));

/**
 * 获取数据单元详情
 */
export const getDatasourceDetail = (params) => dataWrapper(requestUtil.get('gwy-datasource/datasource/getDatasourceDetail', { params }));

/**
 * 获取可编辑数据单元详情
 */
export const getEditableDatasourceDetail = (params) => dataWrapper(requestUtil.get('gwy-datasource/datasource/editDatasource', { params }));

/**
 * 数据单元审批
 */
export const datasourceApprove = (params = {}) => dataWrapper(requestUtil.post('gwy-datasource/datasource/datasourceApprove', params));

/**
 * 数据单元审批 - 重新发起
 */
export const datasourceRestart = (params = {}) => dataWrapper(requestUtil.get('gwy-datasource/datasource/restart', { params }));

/**
 * 数据单元审批 - 重新发起
 */
export const datasourceCancel = (params = {}) => dataWrapper(requestUtil.get('gwy-datasource/datasource/cancelApply', { params }));

/**
 * 获取获取标签列表
 */
export const getTagList = (params) => dataWrapper(requestUtil.get('gwy-datasource/datasource/tag/plan/getTags', { params }));

/**
 * 启用/禁用 标签副本
 */
export const openOrCloseTagPlan = (params) => dataWrapper(requestUtil.post('gwy-datasource/datasource/tag/plan/openOrCloseTagPlan', params));

/**
 * 删除 标签副本
 */
export const delTagPlan = (params) => dataWrapper(requestUtil.post('gwy-datasource/datasource/tag/plan/delTagPlan', params));

/**
 *
 * 取消数据单元编辑
 */
export const cancelDatasourceEdit = (params = {}) => dataWrapper(requestUtil.get('gwy-datasource/datasource/cancelDatasourceEdit', { params }));

/**
 * 查询审批详情
 */
export const getApproveDetail = (params) =>
  dataWrapper(requestUtil.get('gwy-datasource/datasource/getApproveDetail', { params, isCancelErrorAlert: true }));

// 编辑数据单元标签副本
export const editTagPlan = (params) => dataWrapper(requestUtil.post('gwy-datasource/datasource/tag/plan/editTagPlan', params));

// 获取所有的唯一标签
export const getAllUniqueTag = (params) => dataWrapper(requestUtil.get('gwy-datasource/datasource/tag/getAllUniqueTag', { params }));

/**
 * 应用点击
 */
export const appClickStatistics = () => dataWrapper(requestUtil.get('ideal-new-datasource/api/datasource/v2/appClickStatistics'));

/**
 * 获取分组列表-表单管理-包含执行岗
 */

export const getAppListTreeIncludeExecute = (params) =>
  dataWrapper(requestUtil.get('ideal-new-datasource/api/datasource/v2/getAppListTreeIncludeExecute', { params }));

/**
 * 获取个人中心表单
 */

export const getPersonalCenterForm = (params) =>
  dataWrapper(requestUtil.get('ideal-new-datasource/api/datasource/v2/getPersonalCenterForm', { params }));

/**
 * 分页搜索字典类型列表
 */
export const getDictPage = (params = {}) => dataWrapper(requestUtil.get('gwy-datasource/api/dict/page', { params }));

/**
 * 获取启用的字典类型列表
 */
export const getDictList = (params = {}) => dataWrapper(requestUtil.get('gwy-datasource/api/dict/list', { params }));
/**
 * 获取字典详细信息(根据id)
 */
export const getDictById = (dictId) => dataWrapper(requestUtil.get(`gwy-datasource/api/dict/${dictId}`));
/**
 * 获取字典类型的具体字典项(树形结构)
 */
export const getDictTreeDataByCode = (dictCode) => dataWrapper(requestUtil.get(`gwy-datasource/api/dict/${dictCode}/items/tree`));
/**
 * 新增字典类型
 */
export const addDict = (params = {}) => dataWrapper(requestUtil.post('gwy-datasource/api/dict', params, { isCancelErrorAlert: true }));

/**
 * 编辑字典类型
 */
export const updateDict = (dictId, params) => dataWrapper(requestUtil.put(`gwy-datasource/api/dict/${dictId}`, params));

/**
 * 字典类型启用/禁用
 */
export const enableDict = (dictId, enable) => dataWrapper(requestUtil.put(`gwy-datasource/api/dict/${dictId}/enable?enable=${enable}`));

/**
 * 删除字典类型
 */
export const deleteDict = (dictId) => dataWrapper(requestUtil.delete(`gwy-datasource/api/dict/${dictId}`));

/**
 * 保存数据单元自定义排序
 */
export const dataUnitCustomSort = (params) => dataWrapper(requestUtil.post('gwy-datasource/api/data_unit_custom_sort', params));

/**
 * 重置（删除）数据单元自定义排序
 */
export const resetDataUnitCustomSort = (params) => dataWrapper(requestUtil.post('gwy-datasource/api/data_unit_custom_sort/reset', params));
