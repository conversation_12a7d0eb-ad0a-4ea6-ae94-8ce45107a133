import MetaDataDialog from '@/components/metadata-dialog';
import MetaDataSelect from '@/components/metadata-select';
import { OperateType } from '@/const/common';
import { APPROVE_ENUM, APPROVE_TYPE, OPEN_ENUM, OPEN_TYPE, TAG_OPT_ENUM, TAG_OPT_TYPE } from '@/const/datasource';
import { metaDataListOptions } from '@/const/metadata';
import { useGlobalState } from '@/hooks';
import { systemDatametaAPI } from '@/services';
import { getHistoryVerTagListByIds, getToursList, getVersionTagList, submitTourResult } from '@/services/datasource';
import { MetaDataConfigVO, MetaDataVO } from '@/types/metadata';
import { deepEqual, formatDateTime, genUuid } from '@/utils';
import { ExclamationCircleOutlined, ExclamationOutlined, PlusCircleOutlined, SearchOutlined, StarFilled } from '@ant-design/icons';
import { Button, Input, Modal, Space, Table, TableColumnsType, Tooltip, Tour } from '@gwy/components-web';
import { cloneDeep, isEmpty, uniqueId } from 'lodash-es';
import { forwardRef, ForwardRefRenderFunction, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import styles from './index.less';
const { Search } = Input;

export enum TagOperateType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
}

type IRef = object;
type Iprops = {
  initialValue?: any;
  operateType: 'add' | 'edit' | 'approve' | 'view';
  currentVersionId?: string | number;
  preVersionId?: string;
  fromPage?: string;
  currentStep?: number;
  isOrgMeta?: boolean;
  incompleteTag?: any;
  // 父类版本id
  parentDataUnitCurrentVersionId?: string;
  onCompleteTag?: () => void;
};
interface DataType {
  key: React.Key;
  tagId: string;
  name: string;
  // metaName: number;
  metaDataId: string; //数元类型id
  tagMetaDataConfig: any;
  enable: boolean | number; //是否启用
  identifierFlag?: boolean;
  status?: APPROVE_ENUM;
  optType?: TAG_OPT_ENUM;
  bizCreateTime?: string;
  bizUpdateTime?: string;
  [key: string]: any;
}
const AddTagConfig: ForwardRefRenderFunction<IRef, Iprops> = (props: Iprops, ref) => {
  const {
    initialValue,
    operateType,
    currentVersionId,
    preVersionId,
    fromPage,
    currentStep,
    isOrgMeta,
    incompleteTag,
    parentDataUnitCurrentVersionId,
    onCompleteTag,
  } = props;

  //   本地展示数据
  const [configTags, setConfigTags] = useState([]);
  //  上版本数据，目前只需要调接口，后端会进行判断
  const [preVersionTags, setPreVersionTags] = useState([]);

  const [showInput, setShowInput] = useState(false);

  const [changeTag, setChangeTag] = useState<DataType>(null);

  const [loading, setLoading] = useState(false);
  // 是否存在继承父级的标签
  const [hasParentTag, setHasParentTag] = useState(false);

  const [diffMap, setDiffMap] = useState<{
    [key: string]: {
      currentVersionTag: any; // 正式数据
      compareVersionTag: any; // 临时数据
    };
  }>({});
  const [tourOpen, setTourOpen] = useState(false);
  const [tourSteps, setTourSteps] = useState([
    {
      title: '点击这里可以新增标签',
    },
  ]);
  const tourStep1 = useRef(null);
  const [tourCurrent, setTourCurrent] = useState(0);

  const fetchNotTour = async () => {
    const res = await Promise.all([getToursList(3)]);
    if (res.length > 0) {
      let result = [];
      if (res[0] === '0') {
        result.push({
          title: '点击这里可以新增标签',
          target: tourStep1.current,
        });
      }
      setTourSteps(result);
      if (result.length > 0) {
        setTourOpen(true);
      }
    }
  };

  const saveTourData = async () => {
    const res = await submitTourResult({
      guideStatus: 2,
      type: 3,
    })
      .then((res) => true)
      .catch((res) => false);
    if (res) {
      // setTourCurrent(tourCurrent);
    }
  };

  useEffect(() => {
    console.log('currentStep', currentStep);
    if (fromPage && currentStep === 1) {
      fetchNotTour();
    }
  }, [fromPage, currentStep]);

  useImperativeHandle(ref, () => ({
    getValues: () => {
      return {
        configTags,
        preVersionTags,
        // curVersionTags,
      };
    },
  }));

  // 数元选择
  const [metaDataSelect, setMetaDataSelect] = useState<{
    open?: boolean;
    record?: DataType;
  }>({});

  // 数元配置
  const [metaDataDialog, setMetaDataDialog] = useState<{
    open?: boolean;
    operateType?: OperateType;
    record?: DataType;
    metaData?: MetaDataVO;
    metaDataConfig?: MetaDataConfigVO;
    metaDataConfigApproved?: MetaDataConfigVO;
  }>({});
  const orgId = useGlobalState()?.user?.orgIds?.[0] || '';

  const isModified = (currItem, prevItem) => {
    return (
      currItem?.name !== prevItem?.name ||
      !!currItem?.enable !== !!prevItem?.enable ||
      !deepEqual(currItem?.tagMetaDataConfig, prevItem?.tagMetaDataConfig)
    );
  };

  // 是否是临时新增
  const isTempAdd = (tag) => {
    // return !tag.preVersionId;
    return tag.operateType === TagOperateType.INSERT || !tag?.tagId;
  };

  const getTagList = async () => {
    let allTags = await getVersionTagList(currentVersionId || parentDataUnitCurrentVersionId);
    if (allTags) {
      if (parentDataUnitCurrentVersionId) {
        allTags = allTags.map((ite) => ({ ...ite, parentFlag: true }));
      }
      setHasParentTag(allTags.some((t) => t.parentFlag));
      let tagsModified = allTags.filter((tag) => tag.operateType === TagOperateType.UPDATE);
      let tagsRemote = [];
      if (tagsModified.length > 0) {
        // 获取后端之前版本的原始数据
        tagsRemote = await getHistoryVerTagListByIds(preVersionId, {
          ids: tagsModified.map((tag) => tag.preVersionId),
        });
      }
      const diffMap = {};
      const preVersionTags = [];
      allTags
        .filter((tag) => !isTempAdd(tag))
        .forEach((versionTag) => {
          const tagRemote = tagsRemote.find((tag) => tag.tagId === versionTag.tagId);
          if (!isEmpty(tagRemote)) {
            if (isModified(versionTag, tagRemote)) {
              diffMap[versionTag.tagId] = {
                compareVersionTag: versionTag,
                currentVersionTag: tagRemote,
              };
            }
            preVersionTags.push(tagRemote);
          } else {
            preVersionTags.push(versionTag);
          }
        });
      setPreVersionTags(cloneDeep(preVersionTags));
      setConfigTags(allTags.map((tag) => ({ ...tag, tempTagId: genUuid() })));
      setDiffMap(diffMap);
    }
  };

  const getOrgTag = async (id) => {
    setLoading(true);
    try {
      const data = await systemDatametaAPI.getOrgTagLists(id, {
        orgId,
      });
      setConfigTags(data.map((tag) => ({ ...tag, tempTagId: genUuid() })));
      setPreVersionTags(data.map((tag) => ({ ...tag, tempTagId: genUuid() })));
    } catch (e) {}
    setLoading(false);
  };

  const compareTagChange = (currTag: any) => {
    // 新增无需对比
    if (isTempAdd(currTag)) {
      return;
    }

    if (!isEmpty(currTag)) {
      const { tagId } = currTag;
      const preVersionTag = preVersionTags.find((item) => item.tagId === currTag.tagId);
      // console.log(currTag, 'currTag');
      // console.log(preVersionTag, 'preVersionTag');
      if (isModified(currTag, preVersionTag)) {
        setDiffMap((pre) => ({
          ...pre,
          [tagId]: {
            compareVersionTag: currTag,
            currentVersionTag: preVersionTag,
          },
        }));
      } else {
        delete diffMap[tagId];
      }
    }
    console.log(diffMap, 'diffMap');
  };

  const changetagName = (value, tempTagId) => {
    const currentTag = configTags.find((item) => item.tempTagId === tempTagId);
    currentTag['name'] = value;
    setConfigTags([...configTags]);

    compareTagChange({
      ...currentTag,
    });
  };

  const toggleEnable = (status, tempTagId) => {
    const currentTag = configTags.find((item) => item.tempTagId === tempTagId);
    currentTag['enable'] = status;
    setConfigTags([...configTags]);
    compareTagChange({
      ...currentTag,
    });
  };

  const compareMutiTagChange = (tags = []) => {
    // let changeTags = [];
    let tempMap = cloneDeep(diffMap);

    tags.forEach((tag) => {
      const tagId = tag.tagId;
      if (isTempAdd(tag)) {
        return;
      }
      const preVersionTag = preVersionTags.find((item) => item.tagId === tagId);
      if (isModified(preVersionTag, tag)) {
        tempMap = {
          ...tempMap,
          [tagId]: {
            compareVersionTag: tag,
            currentVersionTag: preVersionTag,
          },
        };
      } else {
        delete tempMap[tagId];
      }

      setDiffMap(tempMap);
    });
  };
  const setUniTagFn = (tempTagId) => {
    const currentTag = configTags.find((item) => item.tempTagId === tempTagId);
    const beforeUniTag = configTags.find((item) => item.identifierFlag);
    setConfigTags((prev) =>
      prev.map((item) => {
        if (item.tempTagId === tempTagId) {
          item['identifierFlag'] = true;
        }
        if (!isEmpty(beforeUniTag) && beforeUniTag.tempTagId === item.tempTagId) {
          item['identifierFlag'] = false;
        }
        return item;
      }),
    );

    compareMutiTagChange([currentTag, beforeUniTag].filter(Boolean));
  };
  const formalTagsHasUniqueTag = useMemo(() => {
    return preVersionTags.some((item) => item.identifierFlag);
  }, [preVersionTags]);

  const addConfigTag = () => {
    const tempTagId = uniqueId();
    const tempTag: DataType = {
      key: tempTagId,
      tagId: tempTagId,
      tempTagId,
      name: '',
      enable: true,
      bizCreateTime: '',
      bizUpdateTime: '',
      operateType: TagOperateType.INSERT,
      identifierFlag: false,
      metaDataId: null,
      tagMetaDataConfig: {},
    };
    setConfigTags((prev) => [tempTag, ...(prev || [])]);
    setShowInput(true);
    setChangeTag(tempTag);
  };

  const getTagConfigDetail = () => {
    if (initialValue?.configTags) {
      setPreVersionTags(initialValue?.preVersionTags || []);
      setConfigTags(initialValue?.configTags || []);
      return;
    }

    if (currentVersionId || parentDataUnitCurrentVersionId) {
      getTagList();
    } else {
      setPreVersionTags([]);
      setConfigTags([]);
      setDiffMap({});
      setHasParentTag(false);
    }
  };

  useEffect(() => {
    if (isOrgMeta && currentVersionId) {
      getOrgTag(currentVersionId);
      return;
    }
  }, [currentVersionId]);

  useEffect(() => {
    getTagConfigDetail();
  }, [currentVersionId, parentDataUnitCurrentVersionId]);

  useEffect(() => {
    switch (operateType) {
      case 'add':
        break;
      default:
        break;
    }
  }, [operateType]);

  // 制表过来的
  useEffect(() => {
    if (incompleteTag && configTags?.length > 0) {
      const operateTypes = {
        add: OperateType.edit,
        edit: OperateType.edit,
        approve: OperateType.view,
        view: OperateType.view,
      };
      const record = configTags?.find((tag) => tag.tagId === incompleteTag.tagId);
      const { tempTagId, tagId } = record;
      const tagMetaDataConfig = record?.tagMetaDataConfig;
      setMetaDataDialog({
        open: true,
        operateType: operateTypes[operateType],
        record,
        metaData: {
          metaDataId: tagMetaDataConfig.metaDataId,
          dataType: tagMetaDataConfig.type,
        },
        metaDataConfig: tagMetaDataConfig,
        metaDataConfigApproved: preVersionTags?.find((item) => item.tagId === tagId)?.tagMetaDataConfig,
      });
    }
  }, [incompleteTag, configTags]);

  const renderIff = (diffKey, compareKey, renderOps?) => {
    if (!isEmpty(diffMap[diffKey]?.currentVersionTag) && !isEmpty(diffMap[diffKey]?.compareVersionTag)) {
      const title = renderOps?.label ? renderOps?.label : `修改前：${diffMap[diffKey]?.currentVersionTag[compareKey]}`;
      console.log(diffMap[diffKey]?.currentVersionTag, compareKey, 'diffMap[diffKey]?.currentVersionTag[compareKey]');
      if (typeof diffMap[diffKey]?.currentVersionTag[compareKey] === 'object') {
        if (JSON.stringify(diffMap[diffKey]?.currentVersionTag[compareKey]) !== JSON.stringify(diffMap[diffKey]?.compareVersionTag[compareKey])) {
          return (
            <Tooltip title={title}>
              <ExclamationCircleOutlined className={styles.diffIcon} />
            </Tooltip>
          );
        }
      } else {
        if (diffMap[diffKey]?.currentVersionTag[compareKey] !== diffMap[diffKey]?.compareVersionTag[compareKey]) {
          return (
            <Tooltip title={title}>
              <ExclamationCircleOutlined className={styles.diffIcon} />
            </Tooltip>
          );
        }
      }
    }
    return null;
  };

  let columns: TableColumnsType<DataType> = [
    {
      title: '序号',
      width: 60,
      dataIndex: 'idx',
      render: (value, record, index) => {
        let optType;
        if (isTempAdd(record)) {
          optType = TAG_OPT_ENUM.ADD;
        } else if (diffMap[record.tagId]) {
          optType = TAG_OPT_ENUM.UPDATE;
        }
        const curOptType = TAG_OPT_TYPE[optType];
        // console.log(curOptType, record.optType, 'curOptType---');
        return (
          <div className={styles.clipTriangleWrapper}>
            {curOptType && (
              <div className={styles.clipTriangle} style={{ backgroundColor: curOptType.backgroundColor }}>
                <span className={styles.clipLabel}>{curOptType.label}</span>
              </div>
            )}
            {index + 1}
          </div>
        );
      },
    },
    {
      title: '标签名称',
      dataIndex: 'name',
      render: (value, record, index) => {
        const { identifierFlag, tagId, tempTagId, tagMetaDataConfig, parentFlag } = record;
        const isSameTag = record.tempTagId === changeTag?.tempTagId;
        const { metaDataCodeDTO } = tagMetaDataConfig || {};
        const { configList } = metaDataCodeDTO || {};
        const hasNoCodeGroups =
          metaDataCodeDTO && !isEmpty(metaDataCodeDTO) && (!configList || !configList?.length) && metaDataCodeDTO?.type !== 'NORMAL';
        return showInput && isSameTag ? (
          <>
            <Input
              defaultValue={value}
              style={{ width: 160 }}
              autoFocus
              onBlur={(e) => {
                // 调用接口
                if (value !== (e?.target?.value || '').trim()) {
                  changetagName(e?.target?.value, tempTagId);
                }
                setShowInput(false);
                setChangeTag(null);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  if (value !== ((e?.target as HTMLInputElement)?.value || '').trim()) {
                    changetagName((e?.target as HTMLInputElement)?.value, tempTagId);
                  }
                  setShowInput(false);
                  setChangeTag(null);
                }
              }}
            />
          </>
        ) : (
          <span
            className={styles.uniquetagWrapper}
            onDoubleClick={() => {
              if (!['edit', 'add'].includes(operateType) || parentFlag) return;
              setShowInput(true);
              setChangeTag(record);
            }}
            style={{
              color: configTags.filter((tag) => !tag?.deletedFlag).some((item, idx) => value && index !== idx && value === item.name)
                ? '#FF7D00'
                : 'inherit',
            }}
          >
            {value || '-'}
            {record.identifierFlag && <StarFilled className={styles.uniquetagIcon} />}
            {renderIff(tagId, 'name')}
            {hasNoCodeGroups && isOrgMeta && (
              <Tooltip title="含有编码标签未配置编码组">
                <ExclamationOutlined style={{ color: '#ffb452' }} />
              </Tooltip>
            )}
          </span>
        );
      },
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
        <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
          <Input
            placeholder="请输入标签名称"
            value={selectedKeys[0]}
            onChange={(e) => {
              setSelectedKeys(e.target.value ? [e.target.value] : []);
            }}
            onPressEnter={() => {
              confirm();
            }}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => {
                confirm();
              }}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              搜索
            </Button>
            <Button
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size="small"
              style={{ width: 90 }}
            >
              重置
            </Button>
          </Space>
        </div>
      ),
      onFilter: (value, record) =>
        (record?.name || '')
          .toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()),
      // ...getColumnSearchProps,
    },
    {
      title: '数元类型',
      width: 100,
      dataIndex: 'tagMetaDataConfig',
      filters: metaDataListOptions?.map((item) => ({ ...item, text: item?.name, value: item.dataType })),
      onFilter: (value, record) => {
        if (isEmpty(record?.tagMetaDataConfig)) {
          return false;
        }
        return value === record?.tagMetaDataConfig?.type;
      },
      render: (value, record) => {
        const { tempTagId, tagId, parentFlag } = record;
        let tagMetaDataConfig: MetaDataConfigVO;
        const originDetail =
          typeof diffMap[tagId]?.currentVersionTag === 'string' ? JSON.parse(diffMap[tagId]?.currentVersionTag) : diffMap[tagId]?.currentVersionTag;
        console.log(originDetail, 'originDetail---');
        let originLabel =
          !isEmpty(originDetail) && originDetail?.tagMetaDataConfig
            ? metaDataListOptions.find((item) => item.dataType === (originDetail?.tagMetaDataConfig || {}).type)?.name
            : '无';

        try {
          if (typeof record.tagMetaDataConfig === 'string') {
            tagMetaDataConfig = JSON.parse(record.tagMetaDataConfig);
          } else {
            tagMetaDataConfig = record.tagMetaDataConfig;
          }
        } catch {}
        return (
          <div>
            <span
              style={{ color: '#4D7BF6' }}
              onClick={() => {
                if (!isEmpty(tagMetaDataConfig)) {
                  const operateTypes = {
                    add: OperateType.edit,
                    edit: OperateType.edit,
                    approve: OperateType.view,
                    view: OperateType.view,
                  };
                  setMetaDataDialog({
                    open: true,
                    operateType: parentFlag ? OperateType.view : operateTypes[operateType],
                    record,
                    metaData: {
                      metaDataId: tagMetaDataConfig.metaDataId,
                      dataType: tagMetaDataConfig.type,
                    },
                    metaDataConfig: tagMetaDataConfig,
                    metaDataConfigApproved: preVersionTags?.find((item) => item.tagId === tagId)?.tagMetaDataConfig,
                  });
                } else {
                  if (['view'].includes(operateType)) return;
                  setMetaDataSelect({ open: true, record });
                }
              }}
            >
              {!isEmpty(tagMetaDataConfig)
                ? metaDataListOptions.find((item) => item.dataType === tagMetaDataConfig.type)?.name
                : ['view'].includes(operateType)
                ? '-'
                : '选择数元'}
            </span>
            {renderIff(tagId, 'tagMetaDataConfig', {
              label: originLabel,
            })}
          </div>
        );
      },
    },
    {
      title: '标签状态',
      width: 100,
      dataIndex: 'enable',
      filters: [
        {
          text: '启用',
          value: true,
        },
        {
          text: '禁用',
          value: false,
        },
      ],
      filterMultiple: true,
      onFilter: (value, record) => {
        if (record?.enable !== null) {
          return record?.enable === value;
        } else {
          record.enable === null;
        }
        console.log(value, record, 'record');
      },
      render: (value, record) => {
        const { tempTagId, tagId } = record;
        const originDetail = diffMap[tagId]?.currentVersionTag;
        return (
          <div className={styles.tagBox}>
            {record.status === APPROVE_ENUM.ING ? (
              <div
                className={styles.tag}
                style={{
                  color: APPROVE_TYPE?.[record.status]?.color,
                  borderColor: APPROVE_TYPE?.[record.status]?.color,
                  backgroundColor: APPROVE_TYPE?.[record.status]?.backgroundColor,
                }}
              >
                {APPROVE_TYPE?.[record.status]?.label}
              </div>
            ) : (
              <div
                className={styles.tag}
                style={{
                  color: OPEN_TYPE?.[(value || 'false').toString()]?.color,
                  borderColor: OPEN_TYPE?.[(value || 'false').toString()]?.color,
                  backgroundColor: OPEN_TYPE?.[(value || 'false').toString()]?.backgroundColor,
                }}
              >
                {OPEN_TYPE?.[(value || 'false').toString()]?.label}
              </div>
            )}
            {renderIff(tagId, 'enable', {
              label: originDetail?.enable ? '修改前：开启' : '修改前：禁用',
            })}
          </div>
        );
      },
    },
    { title: '标签类型', dataIndex: 'parentFlag', width: 100, render: (val) => (val ? '属性标签' : '普通标签') },
    {
      title: '创建时间',
      width: 150,
      dataIndex: 'bizCreateTime',
      sorter: (a, b) => {
        const aTime = new Date(a.bizCreateTime).getTime();
        const bTime = new Date(b.bizCreateTime).getTime();
        return aTime - bTime;
      },
      render: (value) => {
        return <div>{value ? formatDateTime(value) : '-'}</div>;
      },
    },
    {
      title: '更新时间',
      width: 150,
      dataIndex: 'bizUpdateTime',
      sorter: (a, b) => {
        const aTime = new Date(a.bizUpdateTime).getTime();
        const bTime = new Date(b.bizUpdateTime).getTime();
        return aTime - bTime;
      },
      render: (value) => {
        return <div>{value ? formatDateTime(value) : '-'}</div>;
      },
    },
    ['edit', 'add'].includes(operateType) && {
      title: '操作',
      width: 100,
      dataIndex: 'action',
      // fixed: 'right',
      render: (value, record) => {
        const { optType, enable, identifierFlag, tagId, tagMetaDataConfig, tempTagId, parentFlag } = record;
        const DISABLE_STATUS_TEXT = (enable || 'false').toString() === OPEN_ENUM.ENABLE ? '禁用' : '启用';
        let tagMetaDataCfg = typeof tagMetaDataConfig === 'string' ? JSON.parse(tagMetaDataConfig) : tagMetaDataConfig;
        // const isUniDisabled = typ
        return (
          <div className={styles.btnWrapper}>
            {/* isLatest && !isCommonMeta && !isView && optType !== TAG_OPT_ENUM.REMOVE ?  */}
            <>
              <Button
                onClick={() => setUniTagFn(tempTagId)}
                color="primary"
                size="small"
                variant="link"
                disabled={!tagMetaDataCfg?.type || formalTagsHasUniqueTag || identifierFlag || hasParentTag}
              >
                设为唯一标识
              </Button>
              {isTempAdd(record) ? (
                <Button
                  onClick={() => {
                    if (!currentVersionId || record.deletedFlag === undefined) {
                      setConfigTags(configTags.filter((item) => item.tempTagId !== tempTagId));
                    } else {
                      setConfigTags((prev) =>
                        prev.map((item) => {
                          if (tempTagId === item.tempTagId) {
                            item['deletedFlag'] = true;
                          }
                          return item;
                        }),
                      );
                    }
                  }}
                  disabled={parentFlag}
                  color="primary"
                  size="small"
                  variant="link"
                >
                  删除
                </Button>
              ) : (
                <Button
                  disabled={(enable && identifierFlag) || parentFlag}
                  onClick={() =>
                    Modal.confirm({
                      title: `确定${enable ? '禁用' : '启用'}该标签吗？`,
                      onOk: () => {
                        toggleEnable(!enable, tempTagId);
                      },
                    })
                  }
                  color="primary"
                  size="small"
                  variant="link"
                >
                  {DISABLE_STATUS_TEXT}
                </Button>
              )}
            </>
          </div>
        );
      },
    },
  ];
  columns = columns.filter(Boolean);

  return (
    <>
      <div className={styles.tagList}>
        <div className={styles.filterWrapper}>
          <div className={styles.left}>
            <span style={{ fontWeight: 'bold' }}>标签列表</span>
          </div>
          <Tour
            open={tourOpen}
            onClose={() => setTourOpen(false)}
            steps={tourSteps}
            onFinish={() => {
              saveTourData();
              setTourOpen(false);
            }}
            current={tourCurrent}
            // onChange={(current) => saveTourData(current)}
            actionsRender={(originNode, { current, total }) => (
              <>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    setTourCurrent(3);
                    saveTourData();
                  }}
                >
                  知道了
                </Button>
                {/* {originNode} */}
              </>
            )}
          />
          <div className={styles.right}>
            <div className={styles.sortBar} onClick={(event) => event.stopPropagation()}>
              {['edit', 'add'].includes(operateType) && (
                <div className={styles.addBtn} ref={tourStep1}>
                  <Button type="link" icon={<PlusCircleOutlined />} onClick={() => addConfigTag()} ghost>
                    新建标签
                  </Button>
                </div>
              )}
              {/* {isLatest && !isCommonMeta && !isView && (
                
              )} */}
            </div>
          </div>
        </div>
        <div className={styles.tableWrapper}>
          <Table<DataType>
            bordered
            size="small"
            columns={columns}
            // rowKey="tagId"
            dataSource={configTags.filter((tag) => !tag?.deletedFlag)}
            scroll={{ x: 'max-content' }}
            loading={loading}
            pagination={false}
          />
        </div>
      </div>

      {metaDataSelect.open && (
        <MetaDataSelect
          open
          onSelect={(metaData) => {
            setMetaDataDialog({
              open: true,
              record: metaDataSelect.record,
              metaData,
            });
          }}
          onClose={() => {
            setMetaDataSelect({ open: false });
            setMetaDataDialog({ open: false });
          }}
        />
      )}

      {metaDataDialog.open && (
        <MetaDataDialog
          open
          operateType={metaDataDialog.operateType}
          metaDataId={metaDataDialog.metaData.metaDataId}
          metaDataConfig={metaDataDialog.metaDataConfig}
          metaDataConfigApproved={metaDataDialog.metaDataConfigApproved}
          isOrgMeta={isOrgMeta}
          tagInfo={configTags.find((item) => item.tempTagId === metaDataDialog.record.tempTagId)}
          onClose={() => {
            setMetaDataDialog({ open: false });
          }}
          onOk={(values) => {
            const currentTag = configTags.find((item) => item.tempTagId === metaDataDialog.record.tempTagId);
            currentTag['tagMetaDataConfig'] = values;
            setConfigTags([...configTags]);
            // 公司数据单元已经是正式版本，无需对比
            if (isOrgMeta) {
              getOrgTag(currentVersionId);
            } else {
              compareTagChange({
                ...currentTag,
              });
            }
            setMetaDataSelect({ open: false });
            setMetaDataDialog({ open: false });

            onCompleteTag?.();
          }}
        />
      )}
    </>
  );
};
export default forwardRef(AddTagConfig);
