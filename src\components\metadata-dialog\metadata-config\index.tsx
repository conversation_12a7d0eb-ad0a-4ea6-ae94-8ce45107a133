import { MetaDataType } from '@/const/metadata';
import { useGlobalState } from '@/hooks';
import { systemDatametaAPI } from '@/services';
import { MetaDataVO } from '@/types/metadata';
import { Button, Form, message } from '@gwy/components-web';
import { isEmpty } from 'lodash-es';
import { useContext, useEffect, useState } from 'react';
import { MetaDataContext } from '../context';
import AddressConfig from './address-config';
import AudioConfig from './audio-config';
import CodeConfig from './code-config';
import FileConfig from './file-config';
import styles from './index.less';
import NumberConfig from './number-config';
import ObjectConfig from './object-config';
import PictureConfig from './picture-config';
import TextConfig from './text-config';
import TimeConfig from './time-config';
import UnitConfig from './unit-config';
import VideoConfig from './video-config';

type Props = {
  metaData?: MetaDataVO;
  metaDataConfig?: any;
  onCancel?: () => void;
  onOk?: (values) => void;
  hideFooter?: boolean;
};

const MetaDataConfig = ({ metaData, metaDataConfig, onCancel, onOk, hideFooter = false }: Props) => {
  const { disabled, isOrgMeta, metaDataConfigApproved, tagInfo } = useContext(MetaDataContext);
  const orgId = useGlobalState()?.user?.orgIds?.[0] || '';

  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();

  const handleSubmit = async () => {
    const values = await form.validateFields();
    if (!isOrgMeta) {
      onOk?.(values);
    } else {
      const orignalCodeConfigLists = metaDataConfigApproved?.['metaDataCodeDTO']?.configList?.map((item) => item.id);
      const currentAddConfigLists = (values?.metaDataCodeDTO?.configList || []).filter((item) => !orignalCodeConfigLists?.includes(item.id));
      setLoading(true);
      try {
        await systemDatametaAPI.addOrgTagConfig({
          dataUnitId: tagInfo?.dataUnitId,
          tagId: tagInfo.tagId,
          orgId,
          configList: currentAddConfigLists,
        });
        message.success('操作成功');
        onOk?.(values);
      } catch (e) {}
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!isEmpty(metaDataConfig)) {
      form.setFieldsValue({
        ...metaDataConfig,
      });
    }
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.formWrapper}>
        <Form disabled={disabled} layout="vertical" colon={true} form={form}>
          <Form.Item hidden name={'type'} initialValue={metaData?.dataType} />
          <Form.Item hidden name={'metaDataId'} initialValue={metaData?.metaDataId} />

          {metaData?.dataType === MetaDataType.Number && <NumberConfig form={form} />}
          {metaData?.dataType === MetaDataType.Text && <TextConfig form={form} metaDataConfig={metaDataConfig} />}
          {metaData?.dataType === MetaDataType.DateTime && <TimeConfig form={form} />}
          {metaData?.dataType === MetaDataType.Object && <ObjectConfig form={form} />}
          {metaData?.dataType === MetaDataType.Unit && <UnitConfig form={form} />}
          {metaData?.dataType === MetaDataType.Code && <CodeConfig form={form} />}
          {metaData?.dataType === MetaDataType.Picture && <PictureConfig form={form} />}
          {metaData?.dataType === MetaDataType.File && <FileConfig form={form} />}
          {metaData?.dataType === MetaDataType.Address && <AddressConfig form={form} />}
          {metaData?.dataType === MetaDataType.Video && <VideoConfig form={form} />}
          {metaData?.dataType === MetaDataType.Audio && <AudioConfig form={form} />}
        </Form>
      </div>

      {((!disabled && !hideFooter) || (isOrgMeta && metaData?.dataType === MetaDataType.Code)) && (
        <div className={styles.bottom}>
          <Button
            onClick={() => {
              onCancel?.();
            }}
            loading={loading}
          >
            取消
          </Button>
          <Button type="primary" onClick={handleSubmit} loading={loading}>
            确定
          </Button>
        </div>
      )}
    </div>
  );
};

export default MetaDataConfig;
