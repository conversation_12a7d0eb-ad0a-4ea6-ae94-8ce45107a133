import { Col, Empty, Form, Row } from '@gwy/components-web';
import classNames from 'classnames';
import { useMemo } from 'react';
import styles from '../../index.less';
import FilterItemPreview from '../filter-item-view';

interface Iprops {
  form: any;
  formAppDetail: any;
}
const logicObj = {
  AND: '且',
  OR: '或',
};
const Formtrigger = (props: Iprops) => {
  const { form, formAppDetail } = props;
  const getRenderFilters = (ops) => {
    return (ops || []).map((item, index) => {
      const { conditionItems, groupLogic } = item || {};
      return (
        <div className={styles.filterContainer} key={index}>
          <div className={styles.filterLabel}>条件组{index + 1}</div>
          {(conditionItems || []).map((conditionItem, itIdx) => {
            const { itemLogic, valueType, value, operator, tagName } = conditionItem || {};
            return (
              <div className={styles.filterItem} key={itIdx}>
                <div>{itIdx === 0 ? '当' : logicObj[itemLogic] || '且'}</div>
                <div className={classNames(styles.commomWrap, styles.tagWrap)}>标签名</div>
                <div className={styles.commomWrap}>{operator}</div>
                <div className={styles.valueWrap}>{value}</div>
              </div>
            );
          })}
          {index === ops.length - 1 ? null : (
            <div className={styles.groupLogic}>
              <div>{logicObj[groupLogic]}</div>
            </div>
          )}
        </div>
      );
    });
  };

  const renderFilterItem = useMemo(() => {
    return (
      <div>
        {(formAppDetail.dataUnits || []).map((dataUnit, index) => {
          const { conditionGroups } =
            dataUnit ||
            {
              // conditionGroups: [
              //   {
              //     conditionItems: [
              //       {
              //         tagId: 1653,
              //         type: 'CODE',
              //         code: 'zidingyibianma',
              //         value: '数的范围',
              //         operator: 'eq',
              //         valueType: 'fixed_value',
              //       },
              //       {
              //         tagId: 1570,
              //         type: 'NUMBER',
              //         code: 'xingbie',
              //         value: 1,
              //         operator: 'eq',
              //         valueType: 'fixed_value',
              //       },
              //     ],
              //     order: 0,
              //     groupLogic: 'AND',
              //   },
              //   {
              //     conditionItems: [
              //       {
              //         tagId: 1567,
              //         type: 'DATETIME',
              //         code: 'shengri',
              //         value: '2025-07-19',
              //         operator: 'eq',
              //         valueType: 'fixed_value',
              //       },
              //       {
              //         tagId: 1650,
              //         type: 'TEXT',
              //         code: 'xingbie2',
              //         value: 'lv7uvr3o',
              //         operator: 'eq',
              //         valueType: 'fixed_value',
              //       },
              //     ],
              //     order: 1,
              //   },
              // ],
            };
          return (
            <div key={index}>
              <div className={styles.dataUnitTitle}>
                <span>{dataUnit?.mainDataUnit ? '主数据单元:' : '副数据单元:'}</span>
                <span style={{ marginLeft: '5px' }}>{dataUnit?.name}</span>
              </div>
              <div>
                <FilterItemPreview conditionGroups={dataUnit?.conditionGroups} theme="blue" />
              </div>
            </div>
          );
        })}
      </div>
    );
  }, [formAppDetail.dataUnits]);
  return (
    <>
      <div>
        <Form form={form}>
          <Row gutter={24}>
            <Col span={24}>{formAppDetail.dataUnits?.length > 0 ? renderFilterItem : <Empty description="暂无数据" />}</Col>
          </Row>
        </Form>
      </div>
    </>
  );
};

export default Formtrigger;
