import { dataWrapper, requestUtil } from '@gwy/libs-web';

/**
 * 创建表单
 */
export const createFormVersion = (params) => dataWrapper(requestUtil.post(`gwy-datasource/api/form_versions`, params));

/**
 * 修改表单
 */
export const updateFormVersion = (versionId, params) => dataWrapper(requestUtil.put(`gwy-datasource/api/form_versions/${versionId}`, params));

/**
 * 启用表单
 */
export const enableFormVersion = (versionId, params) => dataWrapper(requestUtil.put(`gwy-datasource/api/form_versions/${versionId}/enable`, params));

/**
 * 获取表单信息
 */
export const getFormVersion = (versionId) => dataWrapper(requestUtil.get(`gwy-datasource/api/form_versions/${versionId}`));

/**
 * 获取审核表单列表-详细数据
 */
export const getFormDataDetail = (dataId, params: { formVersionId?: number }) =>
  dataWrapper(requestUtil.get(`gwy-datasource-data/api/form_datas/${dataId}/getDetail`, { params }));

/**
 * TODO
 * 获取数据表格、组合标签数据
 */
export const getDataTableDatas = (params: { formVersionId?: number; tableId?: number }) =>
  dataWrapper(requestUtil.get(`gwy-datasource-data/api/form_datas/getDetail`, { params }));

/**
 * TODO
 * 获取表单标签的默认数据
 */
export const getAppTagDefaultDatas = (params: { formVersionId?: number; tableId?: number }) =>
  dataWrapper(requestUtil.get(`gwy-datasource-data/api/form_datas/getDetail`, { params }));
