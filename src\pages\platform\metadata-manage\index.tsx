import { useRoute } from '@/hooks';
import { Spin } from '@gwy/components-web';
import { useState } from 'react';
import { Outlet } from 'umi';
import MetadataList, { DataSourceTypes } from './components/metadata-list';
import styles from './index.less';

interface RouteProps {
  canEdit?: boolean;
}
// 普通数据元
export default () => {
  const { state } = useRoute<RouteProps>();
  const [loading, setLoading] = useState(false);

  return (
    <Spin spinning={loading} wrapperClassName={styles.spin}>
      <div className={styles.metadataContainer}>
        <div className={styles.body}>
          <div className={styles.wrapper}>
            <MetadataList canEdit={state?.canEdit} dataSourceType={DataSourceTypes.NORMAL} loading={loading} setLoading={setLoading} />
          </div>
        </div>
        <Outlet />
      </div>
    </Spin>
  );
};
