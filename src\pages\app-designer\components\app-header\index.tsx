import { DownloadOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Header, TreeSelect, Upload } from '@gwy/components-web';
import { useMemo } from 'react';
import styles from './index.less';

type Props = {
  postList: any[];
  postId: string;
  onCancel: () => void;
  onUploadSucceed: (url: string) => void;
};

const AppHeader = ({ postList, postId, onCancel, onUploadSucceed }: Props) => {
  // 导入文件 配置对象
  const uploadConfig = useMemo(
    () => ({
      accept: ['docx', 'wps'],
      fileSize: 20,
      onChange: (url) => {
        onUploadSucceed(url);
      },
    }),
    [],
  );

  return (
    <div className={styles.container}>
      <Header
        title="制表工场"
        closable
        onClose={() => onCancel()}
        leftExtra={
          <div className={styles.leftExtra}>
            <span>当前岗位：</span>
            <TreeSelect variant="borderless" disabled allowClear={false} size="small" style={{ width: 260 }} treeData={postList} value={postId} />
          </div>
        }
        rightExtra={
          <Upload {...uploadConfig}>
            <Button style={{ color: '#fff' }} type="text" icon={<DownloadOutlined />} className={styles.btn}>
              导入文件
            </Button>
          </Upload>
        }
      />
    </div>
  );
};

export default AppHeader;
