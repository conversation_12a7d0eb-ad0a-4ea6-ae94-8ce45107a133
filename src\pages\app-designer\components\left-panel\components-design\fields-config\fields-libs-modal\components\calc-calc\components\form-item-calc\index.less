.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 9px 0;
  background-color: #fff;
  overflow: hidden;

  .itemContent {
    .separator {
      vertical-align: 10px;
    }
  }

  .deleteBtn {
    span {
      color: #c9cdd4;
      cursor: pointer;
    }

    &:hover {
      span {
        color: #aaa;
      }
    }
  }
}

.addBtnWrapper {
  margin-top: -10px;
  color: #4d7bf6;

  button {
    padding-left: 0;
  }

  :global {
    .ant-btn-icon {
      display: inline-block;
      height: 20px;
    }
  }
}
