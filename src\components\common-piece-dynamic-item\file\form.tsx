import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Form } from '@gwy/components-web';
import { memo } from 'react';
import { UseIn } from '../const';
import FileMeta from './index';
// import { validator } from './validator';

export type FileMetaFormProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  label?: React.ReactNode;
  prefix?: any[];
  isPreview?: boolean;
  options?: any;
};

const FileMetaForm = memo<FileMetaFormProps>(({ useIn, tag, field, label, prefix, isPreview, options }) => {
  const { required, readonly, config } = field || {};
  const { metaDataNumberDTO: appConfig } = config || {};
  const { metaDataNumberDTO: metaConfig } = tag?.tagMetaDataConfig || {};

  //   const rules = useMemo(
  //     () =>
  //       !readonly && [UseIn.App].includes(useIn)
  //         ? [
  //             {
  //               required: required,
  //               message: '请填写必填项',
  //             },
  //             {
  //               validator: async (rule, value) => {
  //                 await validator(rule, value, appConfig as Required<MetaDataNumberDTO>);
  //                 await validator(rule, value, metaConfig as Required<MetaDataNumberDTO>);
  //               },
  //             },
  //           ]
  //         : undefined,
  //     [field, required],
  //   );

  return (
    <Form.Item
      label={label}
      name={[...(prefix || []), getMetaTagUniqueId(field), 'value']}
      rules={[
        {
          required: true,
        },
      ]}
    >
      <FileMeta useIn={useIn} tag={tag} field={field} isPreview={isPreview} options={options} />
    </Form.Item>
  );
});

export default FileMetaForm;
