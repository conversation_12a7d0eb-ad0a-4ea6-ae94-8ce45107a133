import { queryMutData } from '@/services/datasource-data';
import { isEmpty } from 'lodash-es';
import { useEffect, useMemo, useState } from 'react';
import DataList from '../app-table/data-list';
interface Iprops {
  records?: any;
  combineConfig?: any;
  tableConfig?: any;
  cTagsMap: any;
  mainDataId?: any;
  formVersion?: any;
  post?: any;
}
const TableAndCombine = (props: Iprops) => {
  const { records, combineConfig, tableConfig, cTagsMap, mainDataId, formVersion, post } = props;
  const [datasource, setDataSource] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  const [configFields] = useMemo(() => {
    let cfgFields = [];
    if (!isEmpty(tableConfig)) {
      cfgFields = tableConfig?.fields;
    }
    if (!isEmpty(combineConfig)) {
      cfgFields = combineConfig?.fields;
    }
    return [cfgFields];
  }, [combineConfig, tableConfig]);

  const findRecords = async () => {
    const DataUnitMain = tableConfig?.dataUnits?.find((dataUnit) => dataUnit?.mainDataUnit) || {};
    const queryParams = {
      formVersionId: formVersion?.formVersionId,
      postId: post?.postId,
      queryConfig: {
        dataUnits: tableConfig?.dataUnits,
        queryFormType: 1, // 1是基础数据，2是数据表格
        tableConFig: {
          dataUnitId: DataUnitMain?.dataUnitId,
          dataId: mainDataId,
          tableId: tableConfig?.id,
        },
      },
    };
    const data = await queryMutData(queryParams);
    const { records } = data || {};
    setDataSource(records);
  };

  useEffect(() => {
    if (!isEmpty(combineConfig)) {
      setDataSource(records?.map((item) => item?.record)?.filter((record) => record.data_type === 2));
    }
    if (!isEmpty(tableConfig) && mainDataId && !isEmpty(formVersion)) {
      findRecords();
    }
  }, [records, combineConfig, tableConfig, formVersion]);

  const configDataUnits = useMemo(() => {
    const tableDataUnits = tableConfig?.dataUnits;
    return isEmpty(tableConfig) ? formVersion?.dataUnits : tableDataUnits;
  }, [formVersion, tableConfig]);
  return (
    <div>
      <DataList
        isPreview
        formFields={configFields}
        cTagsMap={cTagsMap}
        dataSource={datasource}
        dataUnits={configDataUnits}
        pagination={{
          total: datasource?.length,
          current: pagination.current,
          pageSize: pagination.pageSize,
          onChange: (page, pageSize) => {
            setPagination((pre) => ({ ...pre, current: page, pageSize }));
          },
        }}
        toolBarRender={false}
      />
    </div>
  );
};

export default TableAndCombine;
