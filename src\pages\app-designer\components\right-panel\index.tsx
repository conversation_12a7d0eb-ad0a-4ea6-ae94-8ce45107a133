import Editor, { IEditorRef } from '@/components/onlyoffice-editor';
import { Tabs } from '@gwy/components-web';
import { useContext } from 'react';
import { AppDesignerContext } from '../../context';
import FormDesign from './form-design';
import styles from './index.less';

export enum RightPanelTabType {
  FormDesign = 'FormDesign',
  WordDesign = 'WordDesign',
}

type Props = {
  editorRef: React.RefObject<IEditorRef>;
};

const RightPanel = ({ editorRef }: Props) => {
  const { rightPanelTabType, setRightPanelTabType } = useContext(AppDesignerContext);

  return (
    <div className={styles.container}>
      <Tabs
        activeKey={rightPanelTabType}
        onChange={(val) => setRightPanelTabType(val as RightPanelTabType)}
        destroyOnHidden
        items={[
          {
            key: RightPanelTabType.FormDesign,
            // forceRender: true,
            label: '表单设计',
            children: <FormDesign />,
          },
          {
            key: RightPanelTabType.WordDesign,
            forceRender: true,
            label: 'Word设计',
            children: <Editor ref={editorRef} />,
          },
        ]}
      />
    </div>
  );
};

export default RightPanel;
