import citeIcon from '@/assets/cite.png';
import { getFieldListTab, queryFieldList } from '@/services/calc-field';
import { CalcFieldVO } from '@/types/calc-field';
import { CalculatorOutlined, PlusOutlined, ProfileOutlined } from '@ant-design/icons';
import { Button, Checkbox, Col, Empty, Input, Row, Table, TextEllipsisTooltip, Tooltip } from '@gwy/components-web';
import { ColumnsType } from 'antd/lib/table';
import { TableRowSelection } from 'antd/lib/table/interface';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { calcTypeMap, fieldTypeMap } from '../../index';
import AuthDeptModal from '../auth-dept-modal';
import FieldCreateModal from '../field-create-modal';
import styles from './index.less';

export enum TabType {
  create = 1,
  auth = 2,
}

export type MyCreateWrapperProps = {
  postId?: string;
  type?: TabType; // 类型：1-我创建的 2-被授权的
  checkList?: any[];
  setCheckList?: (list) => void;
  canDelete?: boolean;
  setIsPreivew?: (obj) => void;
  handleDel?: (field, refresh) => void;
  configuredDataUnits?: any[];
  allDataUnitsWithTags?: any[];
};

const MyCreateWrapper = (
  {
    postId,
    checkList,
    setCheckList,
    canDelete = false,
    type,
    setIsPreivew,
    handleDel,
    configuredDataUnits,
    allDataUnitsWithTags,
  }: MyCreateWrapperProps,
  ref,
) => {
  const [isOrgAuth, setIsOrgAuth] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [fieldCreateModalOpen, setFieldCreateModalOpen] = useState(false);

  // 分页相关状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [loading, setLoading] = useState(false);

  // 字段列表
  const [fieldList, setFieldList] = useState<CalcFieldVO[]>([]);
  // 有权限的所有字段列表
  const [authFieldList, setAuthFieldList] = useState<CalcFieldVO[]>([]);
  // 初始化数据
  const getFieldList = async (page = pagination.current, pageSize = pagination.pageSize, fieldName = searchValue) => {
    try {
      setLoading(true);
      const response = await getFieldListTab(postId, {
        type,
        page,
        pageSize,
        fieldName,
      });

      setFieldList(response.records || []);
      setPagination((prev) => ({
        ...prev,
        current: page,
        pageSize,
        total: response.total || 0,
      }));
    } catch (e) {
      console.error('获取字段列表失败:', e);
    } finally {
      setLoading(false);
    }
  };
  const getAuthFieldList = async () => {
    const data = await queryFieldList({
      postId,
      ifQueryRefField: false,
      fieldQueryType: 'GET_HAVE_AUTH_QUERY',
    });

    setAuthFieldList(data || []);
  };
  useEffect(() => {
    getFieldList();
    getAuthFieldList();
  }, [type, postId]); // eslint-disable-line react-hooks/exhaustive-deps

  useImperativeHandle(ref, () => ({
    refresh: () => {
      getFieldList();
    },
  }));

  // 是否有岗位授权按钮权限
  // const hasAuth = useMemo(() => {
  //   const { postProperty } = selectedPost || {};
  //   return [1, 3, 5, 6].includes(postProperty);
  // }, [selectedPost]);
  const hasAuth = true;

  useEffect(() => {
    setCheckList([]);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps
  const handleAuthSearch = (value: string) => {
    setSearchValue(value);
    // 搜索时重置到第一页
    getFieldList(1, pagination.pageSize, value);
  };

  // 分页数据处理 - 现在直接使用服务端分页，不需要前端过滤
  const handleList = fieldList;

  const openAuthModal = () => {
    setIsOrgAuth(true);
  };
  const closeAuthModal = () => {
    setIsOrgAuth(false);
  };
  const openFieldCreateModal = () => {
    setFieldCreateModalOpen(true);
  };
  const closeFieldCreateModal = () => {
    setFieldCreateModalOpen(false);
  };
  const handleCheck = (list: any[]) => {
    setCheckList(list);
  };

  // 分页变化处理
  const handleTableChange = (page: number, pageSize?: number) => {
    getFieldList(page, pageSize || pagination.pageSize, searchValue);
  };

  const columns: ColumnsType<CalcFieldVO> = [
    {
      title: '字段名称',
      dataIndex: 'fieldName',
      ellipsis: true,
      width: '40%',
      render: (t, r) => (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Tooltip title={r.remark}>
            <CalculatorOutlined style={{ marginRight: 6 }} />
          </Tooltip>
          <TextEllipsisTooltip style={{ display: 'inline' }} text={t} />
          &nbsp;
          {r.ifCite && (
            <Tooltip title={r.citeFieldName}>
              <img src={citeIcon} width={16} height={16} />
            </Tooltip>
          )}
        </div>
      ),
    },
    {
      title: '字段类型',
      dataIndex: 'fieldType',
      width: '30%',
      render: (value) => {
        return <>{fieldTypeMap[value] || '计算'}</>;
      },
    },
    {
      title: '计算方式',
      dataIndex: 'calcType',
      width: '30%',
      render: (value) => {
        return <>{calcTypeMap[value] || '计算'}</>;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: '30%',

      render: (_text, record) => (
        <div style={{ display: 'flex', alignItems: 'center', flexWrap: 'nowrap' }}>
          <span
            className={styles.btnText}
            style={{ marginRight: '15px' }}
            onClick={() =>
              setIsPreivew({
                field: record,
                open: true,
              })
            }
          >
            查看
          </span>
          {canDelete && (
            <span className={styles.btnText} onClick={() => handleDel(record, getFieldList)}>
              删除
            </span>
          )}
        </div>
      ),
    },
  ];
  const rowSelection: TableRowSelection<CalcFieldVO> = {
    selectedRowKeys: checkList,
    onSelect: (_record, _selected, selectedRows) => {
      setCheckList(selectedRows.map((item) => item.fieldId));
    },
    onSelectAll: (_selected, selectedRows, _changeRows) => {
      setCheckList(selectedRows.map((item) => item.fieldId));
    },
  };
  return (
    <div className={styles.container}>
      <Row>
        <Col span={12}>
          <Input.Search style={{ width: 248 }} placeholder="请输入关键字进行搜索" onSearch={handleAuthSearch} allowClear />
        </Col>
        <Col span={12} style={{ textAlign: 'right' }}>
          {hasAuth && (
            <Button onClick={openAuthModal} style={{ color: '#4E5969' }} icon={<ProfileOutlined />}>
              字段授权
            </Button>
          )}
          {type === TabType.create && (
            <Button type="primary" icon={<PlusOutlined />} style={{ marginLeft: 10 }} onClick={openFieldCreateModal}>
              新增字段
            </Button>
          )}
        </Col>
      </Row>
      {!!handleList.length && (
        <Checkbox.Group style={{ width: '100%' }} onChange={handleCheck} value={checkList}>
          <div className={styles.fieldList}>
            <Table
              dataSource={handleList}
              columns={columns}
              loading={loading}
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showTotal: (total, range) => `共 ${total} 条`,
                pageSizeOptions: ['10', '20', '50', '100'],
                onChange: handleTableChange,
                onShowSizeChange: handleTableChange,
              }}
              bordered
              size="small"
              rowKey={(record) => record.fieldId}
              rowSelection={{
                ...rowSelection,
              }}
            />
          </div>
        </Checkbox.Group>
      )}
      {!handleList.length && (
        <div style={{ height: '350px' }}>
          <Empty />
        </div>
      )}
      {isOrgAuth && <AuthDeptModal onCancle={closeAuthModal} onOk={closeAuthModal} type={type} postId={postId} />}
      {fieldCreateModalOpen && (
        <FieldCreateModal
          postId={postId}
          fieldList={authFieldList}
          configuredDataUnits={configuredDataUnits}
          allDataUnitsWithTags={allDataUnitsWithTags}
          onCancel={closeFieldCreateModal}
          onOk={() => {
            closeFieldCreateModal();
            getFieldList();
          }}
        />
      )}
    </div>
  );
};
export default forwardRef(MyCreateWrapper);
