export const getBeloneDataUnitTags = (dataUnits, allDataUnitsWithTags) => {
  let tags = [];

  dataUnits.forEach((dataUnit) => {
    let { tagList, dataUnitId, name } = dataUnit;

    if (!tagList) {
      tagList = allDataUnitsWithTags.find((item) => item.dataUnitId === dataUnitId)?.tagList || [];
    }
    tagList.forEach((tag) => {
      tags.push({
        ...tag,
        belongDataUnitId: dataUnitId,
        belongDataUnitName: name,
      });
    });
  });
  return tags;
};
