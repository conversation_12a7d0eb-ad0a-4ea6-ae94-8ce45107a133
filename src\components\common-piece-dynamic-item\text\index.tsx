import { TextInputType } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { dispatchDynamicParamChange } from '@/utils/event-emitter';
import { InputProps, Select } from '@gwy/components-web';
import { memo, useCallback, useMemo } from 'react';
import { UseIn } from '../const';
import { WidgetType } from './const';
import TextEnumMeta from './text-enum';

const TextMeta = memo<
  {
    useIn?: UseIn;
    // 展示类型
    displayType?: WidgetType;
    tag?: DataUnitTagVO;
    field?: FormField;
    value?: any;
    onChange?: (value) => void;
    isPreview?: boolean;
    options?: any;
  } & Pick<InputProps, 'maxLength' | 'placeholder'>
>((props) => {
  const { useIn, tag, field, value, onChange, isPreview, options } = props;

  const { inputType: metaInputType } = tag.tagMetaDataConfig?.metaDataTextDTO || {};
  let { inputType, enumList } = field?.config?.metaDataTextDTO || {};
  console.log(options, 'options----------inselect');
  const handleChange = useCallback(
    (value) => {
      typeof onChange === 'function' && onChange(value);
      dispatchDynamicParamChange({ dataUnitId: tag.dataUnitId, tagId: tag.tagId, value });
    },
    [onChange, tag],
  );
  const opts = useMemo(() => {
    let opts = [];
    if (enumList?.length > 0) {
      opts = enumList.filter((item) => !!options.find((option) => option === item));
    } else {
      opts = options;
    }
    return opts;
  }, [enumList, options]);
  const render = () => {
    if (metaInputType === TextInputType.enum) {
      return <TextEnumMeta useIn={useIn} tag={tag} field={field} value={value} onChange={handleChange} isPreview={isPreview} options={options} />;
    }
    return <Select options={(opts as any).map((ite) => ({ value: ite?.value, label: ite?.value }))} value={value} onChange={handleChange} />;
    // if (metaInputType === TextInputType.manual) {
    //   if (inputType === TextInputType.enum) {
    //     <Select options={(enumList as any).map((ite) => ({ value: ite, label: ite }))} value={value} onChange={handleChange} />;
    //   }
    //   return <TextManualMeta useIn={useIn} tag={tag} field={field} value={value} onChange={handleChange} isPreview={isPreview} />;
    // }
  };

  return render();
});

export default TextMeta;
