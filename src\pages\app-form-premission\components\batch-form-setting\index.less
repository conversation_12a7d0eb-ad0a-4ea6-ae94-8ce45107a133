.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.batchSetting {
  // .container {
  height: 100%;
  padding: 5px;
  overflow: hidden;

  :global {
    .ant-tabs {
      height: 100%;
    }

    .ant-tabs-nav {
      flex-shrink: 0;
      margin-bottom: 0 !important;
    }

    .ant-tabs-content-holder {
      padding: 5px;
      flex-grow: 1;
      background-color: #fff;
      overflow: auto;
    }
  }

  .tabChildren {
    height: 100%;

    .headerWrapper {
      padding: 12px 20px 0;

      .searchWrapper {
        margin-bottom: 8px;
      }

      .checkBoxAll {
        // margin: 5px 0 10px 0;
      }
    }

    .bodyWrapper {
      height: calc(100% - 90px);
      overflow: auto;

      .appListCheck {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        padding: 12px 20px 0;

        .cardWrapper {
          animation-name: opacitys;
          animation-duration: 0.24s;
          animation-iteration-count: 1;

          @keyframes opacitys {
            0% {
              transform: scale(0.985);
              opacity: 0.5;
            }

            100% {
              transform: scale(1);
              opacity: 1;
            }
          }

          width: 100%;
          height: 100%;
          border: 1px solid #e5e6eb;
          border-radius: 6px;
          transition: all 0.24s;
          font-size: 12px;
          color: #4e5969;
          line-height: 14px;

          &:hover {
            border-color: #4d7bf6;
            box-shadow: 0 2px 15px 2px rgba(0, 0, 0, 10%);
          }

          .headerBox {
            display: flex;
            align-items: center;
            padding: 8px 12px;

            img {
              margin-right: 8px;
            }

            .headerInfo {
              width: 100%;
              overflow: hidden;

              .tooltip {
                .ellipsis();
              }

              .titleBox {
                display: flex;
                align-items: center;
                justify-content: space-between;
                overflow: hidden;
                width: 100%;

                .title {
                  flex-grow: 1;
                  overflow: hidden;
                  font-weight: bold;
                  font-size: 14px;
                  color: #1d2129;
                  line-height: 20px;
                  margin-bottom: 6px;
                }

                .tag {
                  flex-shrink: 0;
                  margin-right: 0;
                  margin-left: 6px;
                  padding: 0 4px;
                  line-height: 18px;
                }

                .tagBox {
                  flex-shrink: 0;
                  display: inline-flex;
                  align-items: center;

                  .tag {
                    width: 32px;
                    height: 18px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 0;
                    font-size: 10px;
                    border-radius: 2px;

                    &.w46 {
                      width: 46px;
                    }
                  }
                }
              }

              input {
                margin-bottom: 2px;
              }
            }

            .timeLabel {
              color: #9fa5ae;
              font-size: 12px;

              > span {
                margin-right: 4px;
              }
            }
          }

          .contentBox {
            display: flex;
            width: 100%;
            padding: 9px 16px 12px;

            .infoBox {
              width: 100%;
              // display: flex;
              // align-items: center;
              margin-bottom: 12px;
              font-size: 12px;
              overflow: hidden;

              .tooltip {
                .ellipsis();

                margin-bottom: 6px;
                font-weight: 400;
                color: #4e5969;
              }

              .authInfo {
                display: flex;
                align-items: center;
                line-height: 14px;

                .authBox {
                  .ellipsis();

                  font-weight: 400;
                  color: #4e5969;
                }
              }
            }

            .footerBox {
              display: flex;
              align-items: center;
              justify-content: space-between;
              height: fit-content;
              // margin-top: 20px;
              width: 100%;

              .timeBox {
                color: #9fa5ae;
                font-size: 12px;

                > span {
                  margin-right: 4px;
                }
              }

              .tagBox {
                display: flex;
                align-items: center;

                .tag {
                  width: 52px;
                  height: 22px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 12px;
                  border-radius: 2px;
                }
              }
            }
          }
        }
      }
    }
  }
}
