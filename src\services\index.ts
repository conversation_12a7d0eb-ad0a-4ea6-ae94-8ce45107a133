import { dataWrapper, requestUtil } from '@gwy/libs-web';

export * as appAPI from './app';
export * as commonAPI from './common';
export * as dataMetaAPI from './data-meta';
export * as datasourceAPI from './datasource';
export * as datasourceDataAPI from './datasource-data';
export * as datasourceLightAPI from './datasource-light';
export * as formManageApi from './form-manage';
export * as metaDataAPI from './metadata';
export * as onlyofficeAPI from './onlyoffice';
export * as orgAPI from './org';
export * as orgDataUnitAPI from './org-data-unit';
export * as pendingAPI from './pending';
export * as systemDatametaAPI from './system-datameta';

/**
 * 示例接口
 */
export const getAllUsers = (params) => dataWrapper(requestUtil.post('datasource/test', params));
