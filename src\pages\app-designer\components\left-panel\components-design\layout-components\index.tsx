import { SelectedStateType } from '@/pages/app-designer/const';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { genUuid } from '@/utils';
import { InsertRowAboveOutlined, LayoutOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import classNames from 'classnames';
import { useContext } from 'react';
import Draggable, { DropResultName } from '../tags-config/draggable';
import styles from './index.less';

const LayoutComponents = () => {
  const { groupFlag, combineTagConfig, setCombineTagConfig, setTableList, setSelectedState } = useContext(AppDesignerContext);

  // 添加表格
  const addTable = (item, dropResult) => {
    if (dropResult.name === DropResultName.BaseGroup) {
      // 组合标签
      if (combineTagConfig) {
        return;
      }
      setCombineTagConfig({ id: genUuid() });
      setSelectedState({
        type: SelectedStateType.combineTag,
      });
    } else if (dropResult.name === DropResultName.Container) {
      // 数据表格
      const tableId = genUuid();
      setTableList((pre) => [...pre, { id: tableId, dataUnits: [{}] }]);
      setSelectedState({
        type: SelectedStateType.table,
        tableId,
      });
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.item}>
        <Button
          className={classNames(styles.item, {
            [styles.active]: true,
          })}
          type="primary"
          ghost
          block
          icon={<LayoutOutlined />}
        >
          基础分组
        </Button>
      </div>
      <Draggable
        className={styles.item}
        type="table"
        item={{}}
        onDragEnd={(item, dropResult) => {
          if (groupFlag) {
            return;
          }
          addTable(item, dropResult);
        }}
      >
        <Button disabled={groupFlag} className={styles.item} block icon={<InsertRowAboveOutlined />}>
          数据表格
        </Button>
      </Draggable>
    </div>
  );
};

export default LayoutComponents;
