import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Select } from '@gwy/components-web';
import { isEmpty } from 'lodash-es';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';

export type FileMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  options?: any;
  isPreview?: boolean;
};

const FileMeta = memo(({ useIn, tag, field, value, onChange, options }: FileMetaProps) => {
  // const { value: raw_value } = value || {};
  console.log(options, 'options------------');
  const renderValue = useMemo(() => {
    if (Array.isArray(value)) {
      return value.map((item) => item?.url)?.join(',');
    }
    return value;
  }, [value]);
  console.log(value, renderValue, 'renderValue------------');
  const ops = useMemo(() => {
    let result = [];
    options?.map((item) => {
      if (Array.isArray(item?.data)) {
        const label = item?.data?.map((item) => item?.name).join(',');
        const value = item?.data?.map((item) => item?.url).join(',');
        if (isEmpty(result?.find((res) => res.value === value))) {
          result.push({
            label,
            value,
            data: item?.data,
          });
        }
        return item;
      }
    });
    return result;
  }, [options]);
  return (
    <Select
      value={renderValue}
      options={ops}
      onChange={(e) => {
        onChange(ops.find((option) => option.value === e)?.data);
      }}
    />
  );
});

export default FileMeta;
