import { defineConfig } from '@umijs/max';

export default defineConfig({
  hash: true,
  styleLoader: {},
  jsMinifierOptions: {
    target: ['chrome80', 'es2020'],
  },
  antd: {
    theme: {
      token: {
        borderRadius: 4,
        colorText: '#1d2129',
      },
      components: {
        Form: {
          itemMarginBottom: 10,
          verticalLabelPadding: 2,
        },
      },
    },
  },
  // 需求用数据流才可使用useModel与主应用通信
  model: {},
  base: '/datasource/',
  // 配置publicPath时，runtimePublicPath为必配项
  runtimePublicPath: {},
  // 入口文件加载路径添加前缀
  publicPath: process.env.NODE_ENV === 'production' ? '/datasource/' : '/',
  // publicPath: '/datasource/',
  qiankun: {
    // master: {},
    slave: {},
  },
  proxy: {
    // '/api/gwy-datasource-data': {
    //   // target: 'https://tmp-www.gongwuyun.net',
    //   target: 'http://**********:11008', // 世超 ip
    //   changeOrigin: true,
    //   pathRewrite: {
    //     '^/api/gwy-datasource-data': '',
    //   },
    // },
    // '/api/gwy-datasource': {
    //   // target: 'https://tmp-www.gongwuyun.net',
    //   target: 'http://**********:11005', // 世超 ip
    //   changeOrigin: true,
    //   pathRewrite: {
    //     '^/api/gwy-datasource': '',
    //   },
    // },
    '/api': {
      target: 'https://tmp-www.gongwuyun.net',
      // target: 'http://*********:11003', // 祖威 ip
      changeOrigin: true,
      // pathRewrite: {
      // '^/api': '',
      // },
    },
  },
  npmClient: 'yarn',
  conventionRoutes: {
    exclude: [/\/components/],
  },
  headScripts: [
    {
      type: 'text/javascript',
      src: `https://api.map.baidu.com/api?type=webgl&v=1.0&ak=4ZQKYAaGU1N3tCPRUAu8wKSE7aBkGOgf`,
    },
  ],
});
