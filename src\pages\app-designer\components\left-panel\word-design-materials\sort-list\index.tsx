import { SortableList } from '@/components/SortableList';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { FormField, SourceType } from '@/types/form-field';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Button } from '@gwy/components-web';
import { useContext, useEffect, useState } from 'react';
import styles from './index.less';

export type SortListProps = {
  onOk?: () => void;
};

const SortList = ({ onOk }: SortListProps) => {
  const { configuredFields, setConfiguredFields, tagsMap } = useContext(AppDesignerContext);

  const [_items, _setItems] = useState<(FormField & { id: any })[]>([]);

  const init = () => {
    _setItems(configuredFields?.map((item) => ({ ...item, id: getMetaTagUniqueId(item) })));
  };

  useEffect(() => {
    init();
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.list}>
        <SortableList
          items={_items}
          onChange={(items) => _setItems(items)}
          renderItem={(item) => {
            let name;
            const uniqueId = getMetaTagUniqueId(item);
            if (item.sourceType === SourceType.Tag) {
              name = tagsMap[uniqueId]?.name;
            } else if (item.sourceType === SourceType.Field) {
              name = item.combineConfig?.name;
            }
            return (
              <SortableList.Item id={uniqueId}>
                {name}
                <SortableList.DragHandle />
              </SortableList.Item>
            );
          }}
        />
      </div>
      <div className={styles.bottom}>
        <Button
          className={styles.btn}
          onClick={() => {
            init();
          }}
        >
          重置
        </Button>
        <Button
          className={styles.btn}
          type="primary"
          onClick={() => {
            setConfiguredFields(_items);
            onOk?.();
          }}
        >
          确定
        </Button>
      </div>
    </div>
  );
};

export default SortList;
