import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Select } from 'antd';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';

export type CodeMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean; // 仅预览模式
  options?: any;
};

const CodeMeta = memo(({ useIn, tag, field, value, onChange, isPreview, options }: CodeMetaProps) => {
  const { std_value: raw_value } = value || {};
  console.log(options, 'options code------------');
  const ops = useMemo(() => {
    if (!Array.isArray(options)) return [];
    return (options || [])?.map((opt) => ({
      lebel: opt?.value,
      value: opt?.value,
      data: opt.data,
    }));
  }, [options]);
  return (
    <Select
      options={ops}
      placeholder="请选择"
      value={raw_value}
      onChange={(e) => {
        const opt = ops?.find((item) => item.value === e);
        onChange?.(opt?.data);
      }}
    />
  );
});

export default CodeMeta;
