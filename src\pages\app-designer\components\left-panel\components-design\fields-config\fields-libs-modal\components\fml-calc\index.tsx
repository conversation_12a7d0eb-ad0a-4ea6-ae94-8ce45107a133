import { ExpressionType, FuncType } from '@/types/calc-field';
import { message } from '@gwy/components-web';
import { memo, useContext, useState } from 'react';
import { FieldCreateContext } from '../field-create-modal/context';
import FmlEditor from './components/fml-editor';
import { getFuncShowValue } from './components/fml-editor/utils';
import styles from './index.less';

interface IProps {
  disabled?: boolean;
  value?: any;
  onChange?: (value: any) => void;
}

const FmlCalc = memo<IProps>(({ disabled, value, onChange }) => {
  const { allDataUnitsWithTags, configuredDataUnits, fieldList } = useContext(FieldCreateContext);
  const [currentFunc, setCurrentFunc] = useState<FuncType>(null);

  value = value ?? []; // 防空处理

  /**
   * 函数内追加标签/数值
   */
  const appendFuncArg = (arg) => {
    const last = value[value.length - 1];
    if (last.expressionType !== ExpressionType.FUNC) return;

    // 对函数参数的限制
    // 针对不同函数的处理：AVERAGE | MAX | MIN | ROUND | DATEDIF;
    // ROUND 是 [tag/field, number] 的格式，
    // DATEDIF 是 [tag/field | YYYY-MM-DD, tag/field | YYYY-MM-DD, D|M|Y]
    // 其他的都是只能是 tag/field
    const { arguments: args, functionType } = last.refFunction || {};
    if (functionType === FuncType.ROUND) {
      if (args.length === 0 && ![ExpressionType.TAG, ExpressionType.FUNC_CALC].includes(arg.expressionType)) {
        message.warning('表达式非法');
        return;
      }
      if (args.length === 1 && ![ExpressionType.NUMBER].includes(arg.expressionType)) {
        message.warning('表达式非法');
        return;
      }
      if (args.length === 2) {
        message.warning('表达式非法');
        return;
      }
    } else if ([FuncType.AVERAGE, FuncType.MAX, FuncType.MIN, FuncType.COUNT, FuncType.SUM].includes(functionType)) {
      if (![ExpressionType.TAG, ExpressionType.FUNC_CALC].includes(arg.expressionType)) {
        message.warning('表达式非法');
        return;
      }
    }

    const newLast = {
      ...last,
      refFunction: {
        ...last.refFunction,
        arguments: [...args, arg],
      },
    };
    newLast.showValue = getFuncShowValue(newLast);
    // 选中【系统日期】，calType设置默认值1
    (newLast.functionFormulaV2?.arguments || []).forEach((obj) => {
      if (obj.showValue === '系统日期') obj.refFunctionCalV2.jsonParam = JSON.stringify({ calType: 1 });
    });
    const newValue = [...value.splice(0, value.length - 1), newLast];
    onChange(newValue);
  };

  // 选中标签或字段回调
  const handleSelect = (selectedData) => {
    if (!selectedData) return;

    // 函数处理
    // 如果当前选中了函数，则将标签放入函数内部
    if (currentFunc) {
      appendFuncArg(selectedData);
      return;
    }

    // 标签或字段前面只能是操作符
    const lastType = value[value.length - 1]?.expressionType;
    if (lastType && lastType !== ExpressionType.SYMBOL) {
      message.warning('表达式非法');
      return;
    }

    // 选中【系统日期】，calType设置默认值1
    if (selectedData.showValue === '系统日期') {
      selectedData.refFunctionCalV2.jsonParam = JSON.stringify({ calType: 1 });
    }
    onChange([...value, selectedData]);
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <FmlEditor
          value={value}
          onChange={onChange}
          currentFunc={currentFunc}
          setCurrentFunc={setCurrentFunc}
          appendFuncArg={appendFuncArg}
          onSelectTag={handleSelect}
          allDataUnitsWithTags={allDataUnitsWithTags}
          fieldList={fieldList}
          configuredDataUnits={configuredDataUnits}
          disabled={disabled}
        />
      </div>
    </div>
  );
});

export default FmlCalc;
