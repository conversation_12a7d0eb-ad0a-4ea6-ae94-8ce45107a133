import { ButtonOperateType } from '@/const/button-config';

export enum ENTRY_POST_TYPE {
  EXIST_POST = 1, // 已有岗位
  AUTO_SET = 2, // 自动生成
}

export const ENTRY_POST_TYPE_LABEL = {
  [ENTRY_POST_TYPE.EXIST_POST]: '已有岗位',
  [ENTRY_POST_TYPE.AUTO_SET]: '自动生成',
};

export enum ENTRY_POST_PROPERTY {
  /** 人事岗 */
  PERSONNEL_POST = 5,
  /** 全职岗 */
  FULLTIME_POST = 6,
  /** 副职岗 */
  DEPUTY_POST = 7,
}

export const ENTRY_POST_PROPERTY_LABEL = {
  [ENTRY_POST_PROPERTY.PERSONNEL_POST]: '人事岗',
  [ENTRY_POST_PROPERTY.FULLTIME_POST]: '全职岗',
  [ENTRY_POST_PROPERTY.DEPUTY_POST]: '副职岗',
};

export interface ConfigButton {
  buttonId: string;
  buttonType: ButtonOperateType;
  buttonName: string;
  buttonStyle: any;
  ignoreCondition: boolean;
  events: ConfigEvent[];
}

export interface ConfigEvent {
  optType: ExecutionEventType;
  ifAutoSet: boolean;
  entryPostTypes: ENTRY_POST_TYPE[];
  params: Array<{ tagId: string; datasourceId: string; ruleId: string }>;
}

export enum ExecutionEventType {
  /** 入职 */
  ENTRY_POST = '10',
  /** 离职 */
  LEAVE_POST = '13',
  DATA_SYNC = 'DATA_SYNC', // 数据同步
}

export interface EventParamsConfig {
  label: string;
  type: number;
  toolType?: number;
  ifAutoSet?: boolean;
  dataSave?: number;
}

export const EventParamsMap: Record<string, EventParamsConfig[]> = {
  [ExecutionEventType.ENTRY_POST]: [
    { label: '岗位ID', type: 1, ifAutoSet: true },
    { label: '用户ID', type: 2 },
    { label: '到岗时间', type: 3 },
    { label: '用工类型', type: 4 },
  ],
  [ExecutionEventType.LEAVE_POST]: [
    { label: '岗位ID', type: 1 },
    { label: '用户ID', type: 2 },
    { label: '离岗时间', type: 3 },
  ],
};

/** 入职互斥事件 */
export const entryExclusionEvents = [ExecutionEventType.ENTRY_POST, ExecutionEventType.LEAVE_POST];

export const findNodeByValue = (tree, value) => {
  if (tree.value === value) {
    return tree;
  }
  if (tree.children && tree.children.length > 0) {
    for (const child of tree.children) {
      const foundNode = findNodeByValue(child, value);
      if (foundNode) {
        return foundNode;
      }
    }
  }
  return null;
};
