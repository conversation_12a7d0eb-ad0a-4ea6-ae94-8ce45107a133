import { EditOutlined } from '@ant-design/icons';
import { Input } from '@gwy/components-web';
import { useState } from 'react';

interface Iprops {
  value?: string;
  defaultName?: string;
  onChange?: (val: string) => void;
}
const EditGroupName = (props: Iprops) => {
  const { value, onChange, defaultName } = props;
  // const [name, setName] = useState(value);
  const [showInput, setShowInput] = useState(false);

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
      }}
    >
      {!showInput ? (
        <>
          <span>{value || defaultName}</span>
          <EditOutlined style={{ color: '#4881fa', marginLeft: 10 }} onClick={() => setShowInput(true)} />
        </>
      ) : (
        <Input
          defaultValue={value || defaultName}
          maxLength={20}
          onBlur={(e) => {
            if (e?.target?.value.trim() && value !== e?.target?.value.trim()) {
              onChange?.(e?.target?.value);
            }
            setShowInput(false);
          }}
        />
      )}
    </div>
  );
};
export default EditGroupName;
