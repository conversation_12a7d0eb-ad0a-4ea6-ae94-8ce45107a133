import { datasourceDataAPI } from '@/services';
import { DoubleRightOutlined } from '@ant-design/icons';
import { Col, Form, Row } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import DataListModal from './data-list-modal';
import styles from './index.less';
import OrgListModal from './org-list-modal';

type Props = {
  dataUnit?: any;
};

const DataStatistics = ({ dataUnit }: Props) => {
  const { dataUnitId, lightDataUnitId, dataUnitVersionId } = dataUnit || {};

  const [orgListOpen, setOrgListOpen] = useState(false);
  const [dataListOpen, setDataListOpen] = useState(false);
  const [data, setData] = useState({
    orgNum: 0,
    dataNum: 0,
    lastTime: '',
  });

  const fetchStatistics = async () => {
    const data = await datasourceDataAPI.getDataStatistics(dataUnitId);
    setData(data);
  };

  useEffect(() => {
    fetchStatistics();
  }, [dataUnitId]);

  return (
    <div className={styles.container}>
      <span
        className={styles.more}
        onClick={() => {
          if (data.orgNum > 0) {
            setOrgListOpen(true);
            return;
          }
          if (data.dataNum > 0) {
            setDataListOpen(true);
            return;
          }
        }}
      >
        查看
        <DoubleRightOutlined />
      </span>

      <Row gutter={24}>
        <Col span={24}>
          <Form.Item label="数据统计"></Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="使用公司总量">{data.orgNum}</Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="总数据量">{data.dataNum}</Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="最后存储时间">{data.lastTime ? dayjs(data.lastTime).format('YYYY-MM-DD HH:mm:ss') : ''}</Form.Item>
        </Col>
      </Row>

      {orgListOpen && (
        <OrgListModal
          title={dataUnit?.name}
          dataUnitId={dataUnitId}
          lightDataUnitId={lightDataUnitId}
          dataUnitVersionId={dataUnitVersionId}
          onClose={() => setOrgListOpen(false)}
        />
      )}

      {dataListOpen && (
        <DataListModal
          title={dataUnit?.name}
          dataUnitId={dataUnitId}
          lightDataUnitId={lightDataUnitId}
          dataUnitVersionId={dataUnitVersionId}
          onClose={() => setDataListOpen(false)}
        />
      )}
    </div>
  );
};

export default DataStatistics;
