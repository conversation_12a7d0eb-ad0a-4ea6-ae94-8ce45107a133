import { CodeNumberType, codeNumberTypeOptions } from '@/const/metadata';
import { Checkbox } from '@gwy/components-web';

type Props = {
  disabled?: boolean;
  value?: CodeNumberType;
  onChange?: (value: CodeNumberType) => void;
};

const NumberForm = ({ disabled, value, onChange }: Props) => {
  return (
    <Checkbox.Group
      disabled={disabled}
      options={codeNumberTypeOptions}
      value={[value]}
      onChange={(values) => onChange(Array.isArray(values) ? values.filter((item) => value !== item)[0] : undefined)}
    />
  );
};

export default NumberForm;
