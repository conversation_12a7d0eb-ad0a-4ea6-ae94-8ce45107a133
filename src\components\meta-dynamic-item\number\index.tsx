import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { InputNumber } from '@gwy/components-web';
import { memo } from 'react';
import { UseIn } from '../const';

export type NumberMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean;
};

const NumberMeta = memo(({ useIn, tag, field, value, onChange, isPreview }: NumberMetaProps) => {
  const { placeholder } = field;
  const { decimalPlaces: precision, metaDataNumberDTO } = field?.config || {};
  const { rangeType, min, max } = metaDataNumberDTO || {};

  if (field?.readonly || isPreview) {
    if (useIn === UseIn.Designer) {
      return '回显数据';
    }
    console.log(value, 'value-------------');
    const { value: tagValue, data: tagData } = value || {};
    return tagValue ?? '-';
  }

  return <InputNumber style={{ width: '100%' }} placeholder={placeholder || '请输入'} precision={precision} value={value} onChange={onChange} />;
});

export default NumberMeta;
