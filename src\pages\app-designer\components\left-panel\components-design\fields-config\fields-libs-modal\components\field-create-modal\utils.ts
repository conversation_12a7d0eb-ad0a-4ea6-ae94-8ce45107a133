import { CalcType, ExpressionType, FieldType } from '@/types/calc-field';

/**
 * 校验条件组是否有有效的条件项
 * @param conditionGroups 条件组数组
 * @param operatorField 操作符字段名，默认为 'operator'，refFunctionCalV2 中使用 'relation'
 * @returns 是否有有效的条件项
 */
export const validateConditionGroups = (conditionGroups: any[], operatorField: string = 'operator'): boolean => {
  if (!conditionGroups || conditionGroups.length === 0) return false;

  for (const group of conditionGroups) {
    if (group.conditionItems && group.conditionItems.length > 0) {
      // 检查条件项是否有效（至少有一个非空的条件项）
      const validItems = group.conditionItems.filter((item: any) => item && item.tagId && item[operatorField]);
      if (validItems.length > 0) {
        return true;
      }
    }
  }
  return false;
};

/**
 * 校验标签的筛选条件
 * @param tagConfig 标签配置对象
 * @param tagName 标签名称，用于错误提示
 */
export const validateTagConditions = (tagConfig: any, tagName: string): void => {
  const hasValidConditions = validateConditionGroups(tagConfig.refTag.conditionGroups, 'operator');
  if (!hasValidConditions) {
    throw new Error(`独立字段的公式配置中，标签"${tagName}"的筛选条件不能为空，请配置有效的筛选条件`);
  }
};

/**
 * 校验独立字段的公式配置
 * 当字段类型为独立字段且计算方式为公式计算时，需要保证公式配置中配置的标签的筛选条件中 conditionGroups 中的 conditionItems 不为空
 * @param values 表单值对象
 */
export const validateIndependentFieldFormulas = (values: any): void => {
  if (values.fieldType === FieldType.IndependentField && values.calcType === CalcType.FormulasCalc) {
    const { formulasCalcs = [] } = values;

    for (const formula of formulasCalcs) {
      // 检查直接的标签配置（refTag）
      if (formula.expressionType === ExpressionType.TAG) {
        const tagName = formula.showValue || formula.refTag?.name || '未知标签';

        validateTagConditions(formula, tagName);
      }

      // 检查公式中的标签配置（refFunction.arguments）
      if (formula.refFunction?.arguments) {
        for (const arg of formula.refFunction.arguments) {
          if (arg.expressionType === ExpressionType.TAG) {
            const tagName = arg.showValue || arg.refTag?.name || '未知标签';
            validateTagConditions(arg, tagName);
          }
        }
      }
    }
  }
};
