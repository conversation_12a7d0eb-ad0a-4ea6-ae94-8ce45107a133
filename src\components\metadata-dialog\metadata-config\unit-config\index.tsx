import { UnitType, unitTypeOptions } from '@/const/metadata';
import { Checkbox, Form, FormInstance, Radio, Select, Space } from '@gwy/components-web';
import { useContext, useEffect, useMemo } from 'react';
import { MetaDataContext } from '../../context';
import NumberRender from '../number-config/number-render';

export const UnitDTOKey = 'metaDataUnitDTO';

type Props = {
  form?: FormInstance;
};

const UnitConfig = ({ form }: Props) => {
  const type = Form.useWatch([UnitDTOKey, 'type'], form);
  const configList = Form.useWatch([UnitDTOKey, 'configList'], form);
  const unitSelect = Form.useWatch([UnitDTOKey, 'unit'], form);
  const unitTypeOption = unitTypeOptions.find((item) => item.value === type);

  const { hasApproved, disabled } = useContext(MetaDataContext);
  const unitOptions = useMemo(() => {
    const unitOptions = (unitTypeOption?.options || []).filter((item) => (configList || []).includes(item.value));
    return unitOptions;
  }, [configList, unitTypeOption]);

  useEffect(() => {
    if (!unitOptions.some((item) => item.value === unitSelect)) {
      form.setFieldValue([UnitDTOKey, 'unit'], null);
    }
  }, [unitOptions]);

  return (
    <div>
      <Form.Item label="单位包含" name={[UnitDTOKey, 'type']} rules={[{ required: true, message: '请选择' }]} initialValue={UnitType.weight}>
        <Radio.Group
          disabled={hasApproved || disabled}
          options={unitTypeOptions}
          onChange={() => {
            form.setFieldValue([UnitDTOKey, 'configList'], []);
          }}
        />
      </Form.Item>

      <Form.Item
        key={type}
        label={`配置${unitTypeOption?.label}单位`}
        name={[UnitDTOKey, 'configList']}
        rules={[{ required: true, message: '请选择' }]}
      >
        <Checkbox.Group
          options={unitTypeOption?.options}
          onChange={(values) => {
            // 设置最大的单位
            form.setFieldValue([UnitDTOKey, 'unit'], Array.isArray(values) ? values[0] : undefined);
          }}
        />
      </Form.Item>
      <Form.Item label="数值范围" required style={{ marginBottom: 0 }}>
        <Space style={{ alignItems: 'baseline' }}>
          <NumberRender form={form} prefix={[UnitDTOKey]} />
          <Form.Item name={[UnitDTOKey, 'unit']} rules={[{ required: true, message: '请选择' }]}>
            <Select placeholder="请选择单位" options={unitOptions} style={{ width: 100 }} disabled />
          </Form.Item>
        </Space>
      </Form.Item>
    </div>
  );
};

export default UnitConfig;
