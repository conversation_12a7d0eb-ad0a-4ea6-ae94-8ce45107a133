import CodeMetaForm from '@/components/meta-dynamic-item/code/form';
import NumberMetaForm from '@/components/meta-dynamic-item/number/form';
import ObjectMetaForm from '@/components/meta-dynamic-item/object/form';
import TimeMetaForm from '@/components/meta-dynamic-item/time/form';
import UnitMetaForm from '@/components/meta-dynamic-item/unit/form';
import { MetaDataType } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Tag, Tooltip } from '@gwy/components-web';
import AddressMetaForm from './address/form';
import { UseIn } from './const';
import FileMetaForm from './file/form';
import styles from './index.less';
import TextMetaForm from './text/form';

type Props = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  prefix?: string[]; // form前缀路径
  isPreview?: boolean; // 仅预览模式
  hiddenLabel?: boolean;
};

const MetaDynamicItem = ({ useIn, tag, field, hiddenLabel, ...otherProps }: Props) => {
  const renderItem = () => {
    const type = field?.config?.type;
    let label = (
      <div className={styles.labelWrapper}>
        <span>
          {field.fieldName || tag.name}
          {field.fieldName && (
            <Tooltip title={`原标签名称：${tag.name}`}>
              <InfoCircleOutlined style={{ marginLeft: 5, color: '#4D7BF6' }} />
            </Tooltip>
          )}
        </span>
        {useIn === UseIn.Designer && (
          <>
            {!field.readonly ? (
              <Tag color="blue" style={{ margin: 0 }}>
                输入
              </Tag>
            ) : (
              <Tag color="orange" style={{ margin: 0 }}>
                输出
              </Tag>
            )}
          </>
        )}
      </div>
    );
    if (hiddenLabel) {
      label = null;
    }

    switch (type) {
      case MetaDataType.Text: {
        return <TextMetaForm useIn={useIn} label={label} tag={tag} field={field} {...(otherProps || {})} />;
      }
      case MetaDataType.Number: {
        return <NumberMetaForm useIn={useIn} label={label} tag={tag} field={field} {...(otherProps || {})} />;
      }
      case MetaDataType.DateTime: {
        return <TimeMetaForm useIn={useIn} label={label} tag={tag} field={field} {...(otherProps || {})} />;
      }
      case MetaDataType.Object: {
        return <ObjectMetaForm useIn={useIn} label={label} tag={tag} field={field} {...(otherProps || {})} />;
      }
      case MetaDataType.Unit: {
        return <UnitMetaForm useIn={useIn} label={label} tag={tag} field={field} {...(otherProps || {})} />;
      }
      case MetaDataType.Code: {
        return <CodeMetaForm useIn={useIn} label={label} tag={tag} field={field} {...(otherProps || {})} />;
      }
      case MetaDataType.File: {
        return <FileMetaForm useIn={useIn} label={label} tag={tag} field={field} {...(otherProps || {})} />;
      }
      case MetaDataType.Address: {
        return <AddressMetaForm useIn={useIn} label={label} tag={tag} field={field} {...(otherProps || {})} />;
      }
      default: {
        return null;
      }
    }
  };

  return renderItem();
};

export default MetaDynamicItem;
