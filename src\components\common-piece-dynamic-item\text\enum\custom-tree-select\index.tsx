import { EnumType } from '@/const/metadata';
import { MetaDataTextEnumDTO } from '@/types/metadata';
import { Empty, Select, Spin, TreeSelect, TreeSelectProps } from '@gwy/components-web';
import { useMemo } from 'react';

type Props = {
  enumType?: EnumType;
  treeData?: any[];
  value?: MetaDataTextEnumDTO[];
  onChange?: (value?: MetaDataTextEnumDTO[]) => void;
  multiple?: boolean;
} & Omit<TreeSelectProps, 'value' | 'onChange' | 'treeData'>;

export const fieldNames = {
  [EnumType.custom]: { value: 'id', label: 'item', children: 'subEnumList' },
  [EnumType.general]: { value: 'itemCode', label: 'itemLabel', children: 'subItemList' },
};
const CustomTreeSelect = ({ enumType, treeData, value, onChange, multiple, loading, ...rest }: Props) => {
  let val: any = value;
  if (value) {
    if (multiple) {
      if (!Array.isArray(value)) {
        val = [value];
      }
    } else {
      if (Array.isArray(value)) {
        val = value[0];
      }
    }
  }

  const isTree = useMemo(
    () => treeData.some((d) => Array.isArray(d[fieldNames[enumType].children]) && d[fieldNames[enumType].children].length > 0),
    [treeData, enumType],
  );

  if (isTree) {
    return (
      <div>
        <TreeSelect
          {...rest}
          multiple={multiple}
          treeData={treeData}
          fieldNames={fieldNames[enumType]}
          value={val}
          notFoundContent={loading ? <Spin spinning /> : <Empty />}
          onChange={(values) => {
            if (!values) {
              return onChange?.(undefined);
            }
            return onChange?.(Array.isArray(values) ? values : [values]);
          }}
        />
      </div>
    );
  }

  return (
    <Select
      {...rest}
      mode="multiple"
      options={treeData}
      fieldNames={fieldNames[enumType]}
      value={val}
      notFoundContent={loading ? <Spin spinning /> : <Empty />}
      onChange={(values) => {
        if (!values) {
          return onChange?.(undefined);
        }
        return onChange?.(Array.isArray(values) ? values : [values]);
      }}
    />
  );
};

export default CustomTreeSelect;
