import { OperateType } from '@/const/common';
import { metaDataListOptions } from '@/const/metadata';
import { metaDataAPI } from '@/services';
import { MetaDataConfigVO, MetaDataVO } from '@/types/metadata';
import { Modal, Tabs } from '@gwy/components-web';
import { isEmpty } from 'lodash-es';
import { useEffect, useState } from 'react';
import { MetaDataContext } from './context';
import styles from './index.less';
import MetaDataConfig from './metadata-config';
import MetaDataConfigAdvanced from './metadata-config-advanced';
import MetadataHistory from './metadata-history';
import DatametaInfo from './metadata-info';

type Props = {
  operateType?: OperateType;
  open?: boolean;
  onClose?: () => void;
  onOk?: (values) => void;
  metaDataId?: number;
  metaDataConfig?: MetaDataConfigVO;
  metaDataConfigApproved?: MetaDataConfigVO; // 审批过的版本
  defaultActiveTab?: string;
  hideFooter?: boolean;
  metaData?: any;
  isOrgMeta?: boolean;
  tagInfo?: any;
};

const MetaDataDialog = ({
  open,
  onClose,
  onOk,
  operateType = OperateType.edit,
  metaDataId,
  metaDataConfig,
  metaDataConfigApproved,
  defaultActiveTab = '2',
  hideFooter = false,
  isOrgMeta = false,
  tagInfo,
}: Props) => {
  const [activeKey, setActiveKey] = useState(defaultActiveTab);
  const [metaData, setMetaData] = useState<MetaDataVO>(null);

  // 是否禁用
  const disabled = operateType === OperateType.view;
  // 存在审批通过的版本（业务需要：有些字段，审批通过后，就不能修改
  const hasApproved = !isEmpty(metaDataConfigApproved);

  const fetchMetaData = async (metaDataId) => {
    const data = await metaDataAPI.getMetadataDetail(metaDataId);
    setMetaData(data);
  };

  useEffect(() => {
    if (!metaDataId) return;
    fetchMetaData(metaDataId);
  }, [metaDataId]);

  const handleCancel = () => {
    onClose?.();
  };

  const handleOk = (values) => {
    onOk?.(values);
  };

  if (!metaData) {
    return;
  }

  return (
    <MetaDataContext.Provider
      value={{
        metaData,
        metaDataConfig,
        metaDataConfigApproved,
        hasApproved,
        operateType,
        disabled,
        isOrgMeta,
        tagInfo,
      }}
    >
      <Modal
        title={metaDataListOptions.find((item) => item.dataType === metaData?.dataType)?.name}
        size="small"
        footer={null}
        open={open}
        onCancel={onClose}
      >
        <div className={styles.container}>
          <div>
            <Tabs
              size="small"
              type="card"
              activeKey={activeKey}
              onChange={(key) => setActiveKey(key)}
              items={[
                { key: '1', label: '属性' },
                { key: '2', label: '通用配置' },
                { key: '3', label: '高级配置' },
                !isOrgMeta && { key: '4', label: '历史版本' },
              ].filter(Boolean)}
            />
          </div>

          <div className={styles.content}>
            {activeKey === '1' && <DatametaInfo metaData={metaData} />}
            {activeKey === '2' && (
              <MetaDataConfig
                metaData={metaData}
                metaDataConfig={metaDataConfig}
                onCancel={() => {
                  handleCancel();
                }}
                onOk={(values) => {
                  handleOk(values);
                }}
                hideFooter={hideFooter}
              />
            )}
            {activeKey === '3' && <MetaDataConfigAdvanced />}
            {activeKey === '4' && <MetadataHistory metaDataId={metaData?.metaDataId} />}
          </div>
        </div>
      </Modal>
    </MetaDataContext.Provider>
  );
};

export default MetaDataDialog;
