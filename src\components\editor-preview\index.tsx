import { Modal } from '@gwy/components-web';
import { useEffect, useRef } from 'react';
import Editor, { IEditorRef, MODE_TYPE } from '../onlyoffice-editor';

type Props = {
  fileUrl: string;
  onClose?: () => void;
};

const EditorPreview = ({ fileUrl, onClose }: Props) => {
  // 编辑器ref
  const editorRef = useRef<IEditorRef>();

  useEffect(() => {
    editorRef.current?.setFileUrl({
      fileUrl,
      mode: MODE_TYPE.VIEW,
    });
  }, []);

  return (
    <Modal title="预览" open footer={null} onCancel={onClose} size="large" destroyOnHidden>
      <Editor ref={editorRef} />
    </Modal>
  );
};

export default EditorPreview;
