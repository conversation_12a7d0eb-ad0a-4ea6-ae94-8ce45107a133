import groupIcon from '@/assets/app-form-manage/fold_icon_new.png';
// import { dataSourceV3API } from '@/services';
import { formatDateTime } from '@/utils/misc';
import { ClearOutlined } from '@ant-design/icons';
import { Button, Divider, Dropdown, Input, MenuProps, message, Modal } from '@gwy/components-web';
import { memo, useState } from 'react';
import styles from './index.less';

type Props = {
  group: Record<string, any>;
  fetchFolderAppList: () => void;
  onOpenGroup: () => void;
  showInput?: boolean;
  setShowInput?: (value) => void;
  postId?: string;
};

const FolderCard = memo<Props>(({ group, postId, fetchFolderAppList, onOpenGroup, showInput, setShowInput }) => {
  const { folderId, folderName, appList = [], appListCount = 0, createTime } = group;
  const [focusForlderId, setFocusForlderId] = useState(null);
  // const [showInput, setShowInput] = useState(false);
  const handleReleaseGroup = async () => {
    // await dataSourceV3API.releaseFolder({ folderId });
    fetchFolderAppList();
  };
  const reNameFolder = async (folderId, newFolderName) => {
    const params = {
      folderId,
      newFolderName,
      postId,
    };
    try {
      // await dataSourceV3API.updateFolderName(params);
      message.success('修改成功');
      setShowInput(false);
      fetchFolderAppList();
      // refreshAppList();
    } catch (e) {
      setShowInput(false);
      message.error(e.desc);
    }
  };
  const items = (): MenuProps['items'] => {
    return [
      {
        key: '1',
        label: (
          <a
            onClick={(e) => {
              e.stopPropagation();
              Modal.confirm({
                centered: true,
                title: '解散分组后，应用将被释放到上一层级，是否确定解散?',
                onOk() {
                  handleReleaseGroup();
                },
              });
            }}
          >
            解散分组
          </a>
        ),
        icon: <ClearOutlined />,
      },
    ];
  };

  return (
    <div className={styles.cardWrapper} onDoubleClick={onOpenGroup}>
      <header className={styles.headerBox}>
        <img src={groupIcon} width={55} height={48} />
        <article className={styles.headerInfo}>
          <div className={styles.titleBox}>
            {showInput && focusForlderId === folderId ? (
              <Input
                defaultValue={folderName}
                autoFocus
                onBlur={(e) => {
                  if (e?.target?.value.trim() && folderName !== e?.target?.value.trim()) {
                    reNameFolder(folderId, e?.target?.value);
                  }
                  setFocusForlderId(null);
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    if ((e?.target as HTMLInputElement)?.value.trim() && folderName !== (e?.target as HTMLInputElement)?.value.trim()) {
                      reNameFolder(folderId, (e?.target as HTMLInputElement)?.value);
                    }
                    setFocusForlderId(null);
                    setShowInput(false);
                  }
                }}
              />
            ) : (
              <section
                className={styles.title}
                title={folderName}
                onClick={() => {
                  setFocusForlderId(folderId);
                  setShowInput(true);
                }}
              >
                {folderName}
              </section>
            )}
          </div>
          <span className={styles.dateInfo}>{createTime ? formatDateTime(createTime) : '-'}</span>
        </article>
      </header>
      <Divider style={{ margin: 0 }} />
      <aside className={styles.contentBox}>
        <div className={styles.infoBox}>
          <div className={styles.tooltip}>包含表单：{appListCount}</div>
        </div>
        <div className={styles.footerBox}>
          <Dropdown menu={{ items: items() }} placement="bottomCenter">
            <Button
              type="text"
              size="small"
              style={{ height: 18, fontSize: '12px', lineHeight: '0', padding: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              ···
            </Button>
          </Dropdown>
        </div>
      </aside>
    </div>
  );
});

export default FolderCard;
