import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Select } from '@gwy/components-web';
import { useMemo } from 'react';
import { UseIn } from '../../const';

interface Iprops {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean; // 仅预览模式
  options?: any;
}
const MapItem = (props: Iprops) => {
  const { options, value, onChange } = props;
  console.log(options, value, props, 'map item--------');
  const [ops, initValue] = useMemo(() => {
    const { detail, latitude, longitude } = value?.[0] || {};
    const opt = options?.find((item) => {
      const { detail: dataDetail, longitude: dataLongitude, latitude: dataLatitude } = item?.data?.[0];
      if (dataDetail === detail && dataLongitude === longitude && dataLatitude === latitude) {
        return true;
      }
      return false;
    });

    const initValue = opt?.value || '';
    const ops = options?.map((opt) => ({
      lebel: opt?.value,
      value: opt?.value,
      data: opt.data,
    }));
    return [ops, initValue];
  }, [options, value]);

  return (
    <Select
      value={initValue}
      onChange={(e) => {
        onChange?.(options.find((option) => option.value === e)?.data);
      }}
      options={ops}
    />
  );
};

export default MapItem;
