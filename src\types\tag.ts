import { MetaTagType } from '@/const/metadata';
import { MetaDataConfigVO } from './metadata';

/**
 * DataUnitTagVO
 */
export type DataUnitTagVO = {
  /**
   * 业务创建时间
   */
  bizCreateTime?: string;
  /**
   * 业务修改时间
   */
  bizUpdateTime?: string;
  /**
   * 标签编码
   */
  code?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 数据单元id
   */
  dataUnitId?: number;
  /**
   * 是否删除
   */
  deletedFlag?: boolean;
  /**
   * 是否启用
   */
  enable?: boolean;
  /**
   * 是否唯一标识
   */
  identifierFlag?: boolean;
  /**
   * 数元类型id
   */
  metaDataId?: number;
  /**
   * 标签名称
   */
  name?: string;
  /**
   * 操作类型，1=新增，2=修改
   */
  operateType?: OperateType;
  /**
   * 上一个版本id
   */
  preVersionId?: number;
  /**
   * 标签id
   */
  tagId?: number;
  /**
   * 数元配置
   */
  tagMetaDataConfig?: MetaDataConfigVO;
  /**
   * 标签版本id
   */
  tagVersionId?: number;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 标签类型
   */
  type?: MetaTagType;
  [property: string]: any;
};

/**
 * 操作类型，1=新增，2=修改
 */
export enum OperateType {
  Insert = 'INSERT',
  Update = 'UPDATE',
}
export enum DataSave {
  INPUT = 'INPUT',
  OUTPUT = 'OUTPUT',
}
