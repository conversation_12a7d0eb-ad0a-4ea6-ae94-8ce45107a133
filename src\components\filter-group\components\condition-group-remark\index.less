.iconRemarkGroup {
  position: relative;
}

.popoverContent {
  width: 260px;
  display: flex;
  flex-direction: column;
  padding: 8px;

  .label {
    margin-bottom: 4px;
    color: #1d2129;
    font-weight: 500;
  }

  .textArea {
    resize: none;
  }

  .footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 8px;

    .actions {
      display: flex;
      gap: 8px;
    }
  }
}
