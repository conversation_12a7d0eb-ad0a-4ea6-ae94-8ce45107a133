import { dataWrapper, requestUtil } from '@gwy/libs-web';

// 在线编辑器接口

// 获取编辑器配置
export const getConfig = (params) => dataWrapper(requestUtil.get('gwy-datasource/api/office/editor', { params }));

// 上传编辑器所需文件，获取oo服务中的文件key、filename等信息
export const uploadFile = (params) => dataWrapper(requestUtil.post('gwy-datasource/api/office/upload', params));

// 编辑器保存
export const forceSave = (params: { fileKey: string; key: string }) => {
  return dataWrapper(requestUtil.post('gwy-datasource/api/office/forceSave', params));
};

// 获取保存后的fileUrl
export const getFileCacheUrl = (key: string) => {
  return dataWrapper(requestUtil.get(`gwy-datasource/api/office/getFileCacheUrl?key=${key}`));
};

// 轮询获取保存后的fileUrl
export const pollGetFileCacheUrl = (key: string, preUrl?: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    let count = 3;
    const fn = async () => {
      if (count <= 0) {
        if (preUrl) {
          resolve(preUrl);
        } else {
          reject({ desc: '保存失败，请编辑后重新提交' });
        }
        return;
      }
      count--;
      try {
        const { fileUrl } = (await getFileCacheUrl(key)) || {};
        if (fileUrl) {
          resolve(fileUrl);
        } else {
          setTimeout(fn, 1000);
        }
      } catch (err) {
        reject(err);
      }
    };
    fn();
  });
};

// 轮询获取保存后的fileUrl 文件审批
export const pollGetFileCacheUrlForSubmission = (key: string, preUrl?: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    let count = 3;
    const fn = async () => {
      if (count <= 0) {
        if (preUrl) {
          resolve(preUrl);
        } else {
          reject({ desc: '保存失败，请编辑后重新提交' });
        }
        return;
      }
      count--;
      try {
        const { fileUrl } = (await getFileCacheUrl(key)) || {};
        if (fileUrl) {
          resolve(fileUrl);
        } else {
          setTimeout(fn, 1000);
        }
      } catch (err) {
        reject(err);
      }
    };
    fn();
  });
};

// 将临时文件URL转换永久文件URL
export const transformFileUrl = (params) => dataWrapper(requestUtil.get('ideal-new-datasource/api/datasource/app/transformFileUrl', { params }));
