import { FormField } from '@/types/form-field';
import { dynamicParamsValidator } from '@/utils/calc-field/validators';
import { Form, Input, InputNumber, Select, Switch } from '@gwy/components-web';
import { forwardRef, useEffect, useImperativeHandle } from 'react';
import { PropertyConfigRef } from '..';
import FieldCreateModalWrapper from './field-create-modal-wrapper';
import styles from './index.less';

export type CalcFieldConfigProps = {
  field?: FormField;
  postId?: string;
  configuredDataUnits?: any[];
  allDataUnitsWithTags?: any[];
  calcFields?: any[];
  onChange?: (values: any) => void;
};

const CalcFieldConfig = forwardRef<PropertyConfigRef, CalcFieldConfigProps>(
  ({ field, postId, configuredDataUnits, allDataUnitsWithTags, calcFields, onChange }, ref) => {
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
      validateFields: () => form.validateFields(),
    }));

    useEffect(() => {
      const { formFieldConfig, ...calcField } = field?.fieldConfig || {};
      form.setFieldsValue({
        ...(formFieldConfig || {}),
        calcField,
      });
    }, [field]);

    return (
      <div className={styles.container}>
        <Form
          layout="vertical"
          form={form}
          onValuesChange={(changedValues) => {
            setTimeout(() => {
              const { calcField, ...formFieldConfig } = form.getFieldsValue();
              onChange?.({
                ...calcField,
                formFieldConfig,
              });

              // 如果 calcField 发生变化，触发校验
              if (changedValues && changedValues.calcField) {
                setTimeout(() => {
                  form.validateFields(['calcField']).catch(() => {
                    // 忽略校验错误，只是为了显示错误信息
                  });
                }, 50);
              }
            }, 0);
          }}
        >
          <Form.Item
            label={'计算字段'}
            name="calcField"
            rules={[
              {
                validator: dynamicParamsValidator,
              },
            ]}
          >
            <FieldCreateModalWrapper
              postId={postId}
              configuredDataUnits={configuredDataUnits}
              allDataUnitsWithTags={allDataUnitsWithTags}
              fieldList={calcFields}
            />
          </Form.Item>
          <Form.Item
            label="字段别名"
            name="fieldOtherName"
            tooltip="表单实际应用时看到的字段名称"
            rules={[{ validator: async (_rule, value) => (value && !value.trim() ? Promise.reject('禁止输入空白符') : Promise.resolve()) }]}
          >
            <Input placeholder="请输入" maxLength={20} showCount />
          </Form.Item>
          <Form.Item wrapperCol={{ style: { height: 0 } }} label="四舍五入" name="ifRound">
            <Switch style={{ position: 'absolute', top: -24, right: 0 }} />
          </Form.Item>
          <Form.Item wrapperCol={{ style: { height: 0 } }} label="百分比显示" name="ifPercent">
            <Switch style={{ position: 'absolute', top: -24, right: 0 }} />
          </Form.Item>
          <Form.Item label="小数位数" name="decimalNum" rules={[{ required: true, message: '请输入' }]} help={'最高为6位，且只能输入正整数'}>
            <InputNumber style={{ width: 126 }} min={0} max={6} addonAfter="位" />
          </Form.Item>
          <Form.Item label="组件宽度" name="widgetWith">
            <Select
              options={[
                { label: '100%', value: '100%' },
                { label: '50%', value: '50%' },
              ]}
            />
          </Form.Item>
        </Form>
      </div>
    );
  },
);

export default CalcFieldConfig;
