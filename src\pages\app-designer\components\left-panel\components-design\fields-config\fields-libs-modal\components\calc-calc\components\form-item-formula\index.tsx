import { EditOutlined } from '@ant-design/icons';
import { Button, Modal } from '@gwy/components-web';
import classNames from 'classnames';
import { useMemo, useState } from 'react';
import FmlCalc from '../../../fml-calc';
import styles from './index.less';

interface IProps {
  disabled?: boolean;
  value?: any[];
  onChange?: (value: any[]) => void;
}

const FormItemFormula = (props: IProps) => {
  const { disabled, value, onChange } = props;
  const [visible, setVisible] = useState(false);
  const [funcsVal, setFuncsVal] = useState<any[]>(value);

  const hasValue = useMemo(() => {
    if (value && value.length) {
      return true;
    }
    return false;
  }, [value]);
  // 公式设置按钮展示文案
  const buttonText = useMemo(() => {
    if (hasValue) {
      return '已配置';
    }
    return '配置公式';
  }, [hasValue]);

  // 点击确定
  const handleOk = () => {
    onChange(funcsVal);
    setVisible(false);
  };

  return (
    <>
      <Button
        className={classNames({ [styles.hasValue]: hasValue, [styles.disabled]: disabled })}
        icon={<EditOutlined />}
        onClick={() => {
          setVisible(true);
        }}
      >
        <div title={buttonText} className={styles.title}>
          {buttonText}
        </div>
      </Button>
      <Modal
        open={visible}
        title="配置公式"
        okText="确定"
        destroyOnHidden
        width={810}
        className={styles.modalWrapper}
        closable={false}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={handleOk}
      >
        <div className={styles.container}>
          <FmlCalc
            disabled={disabled}
            value={funcsVal}
            onChange={(val) => {
              setFuncsVal(val);
            }}
          />
        </div>
      </Modal>
    </>
  );
};

export default FormItemFormula;
