.container {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  border: 1px solid #e5e6eb;
  border-radius: 2px;
  gap: 2px;
  cursor: pointer;
  background-color: #fff;

  &:hover {
    border-color: #4d7bf6;
  }

  &.active {
    border-color: #4d7bf6;
  }

  .name {
    flex-grow: 1;
    overflow: auto;
    display: flex;
    align-items: center;
  }
}
