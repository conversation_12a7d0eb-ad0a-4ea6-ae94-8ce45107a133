import { SourceType } from './form-field';

/**
 * com.ideal.gwy.datasource.model.request.form.FormUpdateRequest.StatisticalItem
 *
 * StatisticalItem
 */
export type StatisticalItem = {
  /**
   * 公式列表
   */
  formulas?: Formula[];
  /**
   * 统计项在某列下展示（标签是Long  字段是String）
   */
  location?: Location;
  /**
   * 统计项id
   */
  statisticId?: string;
  /**
   * 统计项名称
   */
  statisticName?: string;
  /**
   * 计算后的结果
   */
  statValue?: number;
  [property: string]: any;
};

/**
 * com.ideal.gwy.datasource.model.request.form.FormUpdateRequest.Formula
 *
 * Formula
 */
export type Formula = {
  /**
   * 数据单元id
   */
  dataUnitId?: number;
  /**
   * 表达式描述类型: 2->运算符 4->函数
   */
  expressionType?: ExpressionType;
  /**
   * 公式 (1-平均值,2-最大值,3-最小值,5-求和,6-计数,7-去重计数)
   */
  functionType?: FunctionType;
  /**
   * 标签id(Long)  或者 字段id（String）
   */
  sourceId?: any;
  /**
   * 类型：1- 标签 3-字段
   */
  sourceType?: SourceType;
  /**
   * 符号 + - * /
   */
  value?: string;
  [property: string]: any;
};

/**
 * 公式 (1-平均值,2-最大值,3-最小值,4-四舍五入,5-求和,6-计数,7-去重计数,8-days函数)
 *
 * 公式 (1-平均值,2-最大值,3-最小值,5-求和,6-计数,7-去重计数)
 */
export enum FunctionType {
  Average = 'AVERAGE',
  Ceil = 'CEIL',
  Count = 'COUNT',
  DaysCount = 'DAYS_COUNT',
  DistinctCount = 'DISTINCT_COUNT',
  Floor = 'FLOOR',
  Max = 'MAX',
  Min = 'MIN',
  Month = 'MONTH',
  Round = 'ROUND',
  Sum = 'SUM',
}

/**
 * 表达式描述类型, 1->数值 2->运算符 3->标签 4->函数 5->计算公式
 *
 * 表达式描述类型: 2->运算符 4->函数
 */
export enum ExpressionType {
  Func = 'FUNC',
  FuncCalc = 'FUNC_CALC',
  Number = 'NUMBER',
  Symbol = 'SYMBOL',
  Tag = 'TAG',
}

/**
 * 统计项在某列下展示（标签是Long  字段是String）
 *
 * Location
 */
export type Location = {
  /**
   * 数据单元id
   */
  dataUnitId?: number;
  /**
   * 标签id(Long)  或者 字段id（String）
   */
  sourceId?: any;
  /**
   * 类型：1- 标签 3-字段
   */
  sourceType?: SourceType;
  [property: string]: any;
};
