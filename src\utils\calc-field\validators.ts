import { FILTER_VALUE_TYPE, NEW_RELATION_TYPE } from '@/components/filter-group/filter-const';
import { CalcFieldVO, CalcType, ExpressionType, ValueType } from '@/types/calc-field';

// 检测公式中的标签是否配置了动态入参
const checkDynamicParams = (formulasCalcs: any[]) => {
  if (!formulasCalcs || !Array.isArray(formulasCalcs)) return { hasError: false, errorTags: [] };

  const errorTags: string[] = [];

  formulasCalcs.forEach((item) => {
    if (item.expressionType === ExpressionType.TAG && item.refTag) {
      const { conditionGroups } = item.refTag;
      if (conditionGroups && Array.isArray(conditionGroups)) {
        conditionGroups.forEach((group) => {
          if (group.conditionItems && Array.isArray(group.conditionItems)) {
            group.conditionItems.forEach((conditionItem: any) => {
              if (
                ![NEW_RELATION_TYPE.EMPTY.val, NEW_RELATION_TYPE.NOT_EMPTY.val].includes(conditionItem.operator) &&
                conditionItem.valueType === FILTER_VALUE_TYPE.VARIABLE_DYNAMIC
              ) {
                const tagName = item.refTag.name || item.showValue || '未知标签';
                if (!errorTags.includes(tagName)) {
                  errorTags.push(tagName);
                }
              }
            });
          }
        });
      }
    }

    // 递归检查函数内的参数
    if (item.expressionType === ExpressionType.FUNC && item.refFunction?.arguments) {
      const funcResult = checkDynamicParams(item.refFunction.arguments);
      errorTags.push(...funcResult.errorTags);
    }
  });

  return { hasError: errorTags.length > 0, errorTags: [...new Set(errorTags)] };
};

// 检查阶梯计算中的动态入参
const checkCalculationDynamicParams = (calculationCalcs: any[]) => {
  if (!calculationCalcs || !Array.isArray(calculationCalcs)) return { hasError: false, errorTags: [] };

  const errorTags: string[] = [];

  calculationCalcs.forEach((calc) => {
    // 检查每个规则组中的计算模式
    if (calc.calculationModes && Array.isArray(calc.calculationModes)) {
      calc.calculationModes.forEach((mode: any) => {
        // 检查条件公式 (conditionFormulas)
        if (mode.conditionFormulas?.formulasCalcs) {
          const conditionResult = checkDynamicParams(mode.conditionFormulas.formulasCalcs);
          errorTags.push(...conditionResult.errorTags);
        }

        // 检查结果公式 (resultFormulas) - 当结果类型为计算公式时
        if (mode.valueType === ValueType.CALC_FORMULAS && mode.resultFormulas?.formulasCalcs) {
          const resultResult = checkDynamicParams(mode.resultFormulas.formulasCalcs);
          errorTags.push(...resultResult.errorTags);
        }
      });
    }
  });

  return { hasError: errorTags.length > 0, errorTags: [...new Set(errorTags)] };
};

export const dynamicParamsValidator = async (_rule: any, value: CalcFieldVO) => {
  if (!value) return Promise.resolve();

  let result = { hasError: false, errorTags: [] };

  // 检查公式计算
  if (value.calcType === CalcType.FormulasCalc) {
    result = checkDynamicParams(value.formulasCalcs || []);
  }

  // 检查阶梯计算
  if (value.calcType === CalcType.Calculation) {
    result = checkCalculationDynamicParams(value.calculationCalcs || []);
  }

  if (result.hasError) {
    return Promise.reject(new Error(`请完善动态入参：${result.errorTags.join('、')}`));
  }

  return Promise.resolve();
};
