.applyList {
  width: 100%;
  height: 100%;
  overflow: hidden;
  // overflow-y: auto;
  display: flex;
  flex-direction: column;

  .breadcrumbWrapper {
    :global {
      li {
        display: flex;

        .ant-breadcrumb-link {
          max-width: 300px;
          overflow: hidden;
          display: inline-block;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .headerWrapper {
    padding: 10px 20px;
    margin-bottom: 16px;
    display: flex;
    // justify-content: space-between;
    align-items: center;
    column-gap: 8px;
    padding-right: 0;

    .changeType {
      width: 76px;
      border: 1px solid #c9cdd4;
      border-radius: 2px;
      padding: 2px 0;
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      .changeIcon {
        cursor: pointer;
        font-size: 18px;
        padding: 4px;
        color: #4e5969;
        transition: all 0.24s;
      }

      .activeIcon {
        color: #4d7bf6;
        background-color: #e8f3ff;
        border-radius: 4px;
      }
    }

    .folder {
      margin-top: 2px;

      .folderIcon {
        font-size: 20px;
        color: #86909c;
        cursor: pointer;
      }
    }
  }
}

.listWrapper {
  // padding-top: 10px;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  display: grid;
  // 150
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  grid-auto-rows: 120px;
  padding-top: 16px;

  .cardWrapper {
    padding: 11px 0;
    width: 120px;
    // width: 80px;
    height: fit-content;
    // margin: 0 20px;
    position: relative;
    text-align: center;
    row-gap: 6px;
    transition: all 0.24s;
    cursor: pointer;

    // &.folderWraper {
    //   width: 80px;
    //   height: 64px;
    // }
    // &.filesWrapper {
    //   width: 72px;
    //   height: 64px;
    // }
    // img {
    //   width: 100%;
    //   object-fit: contain;
    // }
    .folderImg {
      width: 62px;
      height: 56px;
    }

    .fileImg {
      width: 56px;
      height: 56px;
    }

    .titleWrap {
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 6px;
        height: 6px;
      }
    }

    .title {
      font-weight: normal;
      font-size: 14px;
      color: #1d2129;
      line-height: 22px;
      margin-top: 12px;
    }

    .badge {
      position: absolute;
      display: inline-block;
      // width: 18px;
      // height: 18px;
      color: white;
      // background-color: #4d7bf6;
      border-radius: 2px;
      left: 30px;
      top: 9px;
      text-align: center;
      font-size: 12px;

      &.gray {
        background-color: #89a1be;
      }
    }

    .formDropdownIcon {
      width: 18px;
      height: 18px;
      background: #fff;
      border-radius: 4px;
      visibility: hidden;
      position: absolute;
      right: 10px;
      top: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      column-gap: 8px;

      .icon {
        font-size: 16px;

        &:hover {
          color: #4d7bf6;
        }
      }
    }

    .btnIcon {
      visibility: hidden;
      position: absolute;
      right: 40px;
      bottom: 48px;
      display: flex;
      column-gap: 8px;

      .icon {
        font-size: 16px;

        &:hover {
          color: #4d7bf6;
        }
      }
    }

    .actions {
      display: none;
    }

    &:hover {
      background: #e8f3ff;
      border-radius: 10px;
      transform: translateY(-4px);

      .btnIcon {
        visibility: visible;
      }

      .formDropdownIcon {
        visibility: visible;
      }

      .actions {
        display: block;
      }
    }

    &.active {
      &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: -1;
        background-color: rgba(34, 86, 155, 24%);
      }
    }

    animation-name: opacitys;
    animation-duration: 0.24s;
    animation-iteration-count: 1;

    @keyframes opacitys {
      0% {
        transform: scale(0.985);
        opacity: 0.5;
      }

      100% {
        transform: scale(1);
        opacity: 1;
      }
    }
  }
}

.tableWrapper {
  flex: 1;
  padding: 0 20px;
  // overflow: hidden;
  overflow: auto;

  .rowWrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .nameWrapper {
      display: flex;
      align-items: center;
      column-gap: 12px;
      font-weight: bold;
      font-size: 14px;
      color: #1d2129;
      line-height: 20px;
    }

    img {
      width: 30px;
      object-fit: contain;
    }

    .btnIcon {
      visibility: hidden;
      display: flex;
      column-gap: 8px;

      .icon {
        font-size: 16px;

        &:hover {
          color: #4d7bf6;
        }
      }
    }

    .badge {
      width: 20px;
      height: 20px;
      color: white;
      background-color: #4d7bf6;
      border-radius: 2px;
      text-align: center;
    }

    &:hover {
      .btnIcon {
        visibility: visible;
      }

      .badge {
        display: none;
      }
    }
  }
}

:global {
  .ant-collapse > .ant-collapse-item > .ant-collapse-header {
    padding: 8px 16px;
  }

  .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
    padding-top: 0;
    padding-bottom: 0;
    padding: 0;
  }
}

.sortBtn {
  position: absolute;
  top: -50px;
  right: 12px;

  button span:last-child {
    font-size: 12px;
  }
}
