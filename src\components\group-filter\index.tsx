import { Form, Select } from '@gwy/components-web';
import { forwardRef, memo, useEffect, useImperativeHandle, useMemo } from 'react';
import { DATA_TYPE } from '../filter-group/filter-const';

interface GroupFilterProps {
  dataUnitId: number;
  tags: any[];
  disabled?: boolean;
  value?: any;
  onChange?: (value: any) => void;
}

const GroupFilter = memo(
  forwardRef((props: GroupFilterProps, ref) => {
    const { dataUnitId, tags, disabled = false, value, onChange } = props;
    const [form] = Form.useForm();
    // const form = Form.useFormInstance();
    useImperativeHandle(ref, () => ({
      validate: () => {
        return form.validateFields();
      },
    }));
    // 时间类型标签
    const dataTimeTags = useMemo(() => {
      return tags.filter((tag) => tag.tagMetaDataConfig?.type === DATA_TYPE.DATETIME.val);
    }, [tags]);

    useEffect(() => {
      const { groupTags = [], timeTag = {} } = value || {};
      const groupTagIds = groupTags.map((tag) => tag.tagId);
      const timeTagId = timeTag?.tagId;

      form.setFieldsValue({
        groupTagIds: groupTagIds || [],
        timeTagId: timeTagId || undefined,
      });
    }, [value, form]);

    const onValuesChange = (changedFields: any, allFields: any) => {
      const { groupTagIds, timeTagId } = allFields;
      const groupTags = tags
        .filter((tag) => groupTagIds.includes(tag.tagId))
        .map((tag) => ({
          dataUnitId,
          tagId: tag.tagId,
        }));
      const value = {
        groupTags,
        timeTag: {
          dataUnitId,
          tagId: timeTagId,
        },
      };
      onChange?.(value);
    };

    return (
      <Form form={form} onValuesChange={onValuesChange}>
        <Form.Item label="分组标签" name="groupTagIds" rules={[{ required: true, message: '请选择分组标签' }]}>
          <Select
            mode="multiple"
            style={{ width: '100%' }}
            disabled={disabled}
            options={tags.map((tag) => ({ label: tag.name, value: tag.tagId }))}
          />
        </Form.Item>
        <Form.Item label="时间标签" name="timeTagId" rules={[{ required: true, message: '请选择时间标签' }]}>
          <Select style={{ width: '100%' }} disabled={disabled} options={dataTimeTags.map((tag) => ({ label: tag.name, value: tag.tagId }))} />
        </Form.Item>
      </Form>
    );
  }),
);

export default GroupFilter;
