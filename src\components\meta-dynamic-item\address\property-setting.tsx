import { commonAPI } from '@/services';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Form, Radio } from '@gwy/components-web';
import { useEffect, useState } from 'react';
import { AddressRangeOptions } from './const';

type Props = {
  field: FormField;
  tag: DataUnitTagVO;
};

const AddressPropertySetting = ({ field, tag }: Props) => {
  const form = Form.useFormInstance();
  const { metaDataAddressDTO } = tag.tagMetaDataConfig;
  const { codeList, rangeType: metaRangeType } = metaDataAddressDTO;

  // const rangeType = Form.useWatch([AddressDTOKey, 'rangeType'], form);

  const [areaTree, setAreaTree] = useState([]);

  const fetchAreaTree = async () => {
    const data = await commonAPI.getAreaTree();
    const loopTree = (list) => {
      return (list || []).map((li) => ({ ...li, label: li.name, value: li.code, children: loopTree(li.children) }));
    };
    setAreaTree(loopTree(data));
  };

  useEffect(() => {
    fetchAreaTree();
  }, []);

  useEffect(() => {
    form.setFieldsValue(field.config);
  }, []);

  return (
    <div>
      <Form.Item label="可选层级" name={['addressRange']}>
        <Radio.Group options={AddressRangeOptions} />
      </Form.Item>
      {/* <Form.Item label="地址范围" name={[AddressDTOKey, 'rangeType']} rules={[{ required: true, message: '请选择' }]}>
        <Radio.Group options={rangeTypeOptions} />
      </Form.Item>
      {rangeType !== RangeType.unlimited && (
        <Form.Item name={[AddressDTOKey, 'codeList']} rules={[{ required: true, message: '请选择' }]}>
          <Cascader style={{ width: '100%' }} options={areaTree} multiple maxTagCount="responsive" maxTagPlaceholder={maxTagPlaceholder} />
        </Form.Item>
      )} */}
    </div>
  );
};

export default AddressPropertySetting;
