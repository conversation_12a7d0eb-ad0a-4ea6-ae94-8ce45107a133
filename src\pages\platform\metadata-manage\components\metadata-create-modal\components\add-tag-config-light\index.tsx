import MetaDataDialog from '@/components/metadata-dialog';
import { OperateType } from '@/const/common';
import { APPROVE_ENUM, APPROVE_TYPE, OPEN_TYPE, TAG_OPT_ENUM } from '@/const/datasource';
import { metaDataListOptions } from '@/const/metadata';
import { datasourceLightAPI } from '@/services';
import { getVersionTagList } from '@/services/datasource';
import { MetaDataConfigVO, MetaDataVO } from '@/types/metadata';
import { formatDateTime, genUuid } from '@/utils';
import { SearchOutlined, StarFilled } from '@ant-design/icons';
import { Button, Checkbox, Input, Space, Table, TableColumnsType } from '@gwy/components-web';
import { isEmpty } from 'lodash-es';
import { forwardRef, ForwardRefRenderFunction, useEffect, useImperativeHandle, useState } from 'react';
import styles from './index.less';
const { Search } = Input;

export enum TagOperateType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
}

type IRef = object;
type Iprops = {
  initialValue?: any;
  operateType: 'add' | 'edit' | 'approve' | 'view';
  currentVersionId?: string | number;
  dataUnit?: any;
};
interface DataType {
  key: React.Key;
  tagId: number;
  name: string;
  // metaName: number;
  metaDataId: string; //数元类型id
  tagMetaDataConfig: any;
  enable: boolean | number; //是否启用
  identifierFlag?: boolean;
  status?: APPROVE_ENUM;
  optType?: TAG_OPT_ENUM;
  bizCreateTime?: string;
  bizUpdateTime?: string;
  [key: string]: any;
}
const AddTagConfigLight: ForwardRefRenderFunction<IRef, Iprops> = (props: Iprops, ref) => {
  const { initialValue, operateType, currentVersionId, dataUnit } = props;
  //   本地展示数据
  const [configTags, setConfigTags] = useState([]);
  const [relateTagIds, setRelateTagIds] = useState<number[]>([]);

  useImperativeHandle(ref, () => ({
    getValues: () => {
      return {
        // configTags,
        relateTagIds,
      };
    },
  }));

  // 数元配置
  const [metaDataDialog, setMetaDataDialog] = useState<{
    open?: boolean;
    operateType?: OperateType;
    record?: DataType;
    metaData?: MetaDataVO;
    metaDataConfig?: MetaDataConfigVO;
    metaDataConfigApproved?: MetaDataConfigVO;
  }>({});

  const getTagList = async (currentVersionId) => {
    let allTags = await getVersionTagList(currentVersionId);
    setConfigTags(allTags.map((tag) => ({ ...tag, tempTagId: genUuid() })));

    if (dataUnit?.light) {
      const data = await datasourceLightAPI.getTagList(dataUnit.lightDataUnitId);
      setRelateTagIds(data?.map((item) => item.tagId));
    } else {
      setRelateTagIds(allTags.filter((tag) => tag.identifierFlag).map((item) => item.tagId));
    }
  };

  useEffect(() => {
    if (currentVersionId) {
      getTagList(currentVersionId);
    }
  }, [currentVersionId]);

  let columns: TableColumnsType<DataType> = [
    {
      title: '序号',
      width: '10%',
      dataIndex: 'idx',
      render: (value, record, index) => {
        return <div className={styles.clipTriangleWrapper}>{index + 1}</div>;
      },
    },
    {
      title: '标签名称',
      width: '20%',
      dataIndex: 'name',
      render: (value, record, index) => {
        const { tagId } = record;
        return (
          <span className={styles.uniquetagWrapper}>
            {value || '-'}
            {record.identifierFlag && <StarFilled className={styles.uniquetagIcon} />}
          </span>
        );
      },
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters, close }) => (
        <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
          <Input
            placeholder="请输入标签名称"
            value={selectedKeys[0]}
            onChange={(e) => {
              setSelectedKeys(e.target.value ? [e.target.value] : []);
            }}
            onPressEnter={() => {
              confirm();
            }}
            style={{ marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => {
                confirm();
              }}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              搜索
            </Button>
            <Button
              onClick={() => {
                clearFilters();
                confirm();
              }}
              size="small"
              style={{ width: 90 }}
            >
              重置
            </Button>
          </Space>
        </div>
      ),
      onFilter: (value, record) =>
        (record?.name || '')
          .toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()),
      // ...getColumnSearchProps,
    },
    {
      title: '数元类型',
      width: '15%',
      dataIndex: 'tagMetaDataConfig',
      filters: metaDataListOptions?.map((item) => ({ ...item, text: item?.name, value: item.dataType })),
      onFilter: (value, record) => {
        if (isEmpty(record?.tagMetaDataConfig)) {
          return false;
        }
        return value === record?.tagMetaDataConfig?.type;
      },
      render: (value, record) => {
        const { tempTagId, tagId } = record;
        let tagMetaDataConfig: MetaDataConfigVO;
        try {
          if (typeof record.tagMetaDataConfig === 'string') {
            tagMetaDataConfig = JSON.parse(record.tagMetaDataConfig);
          } else {
            tagMetaDataConfig = record.tagMetaDataConfig;
          }
        } catch {}
        return (
          <div>
            <span
              style={{ color: '#4D7BF6' }}
              onClick={() => {
                if (!isEmpty(tagMetaDataConfig)) {
                  const operateTypes = {
                    add: OperateType.edit,
                    edit: OperateType.edit,
                    approve: OperateType.view,
                    view: OperateType.view,
                  };
                  setMetaDataDialog({
                    open: true,
                    operateType: operateTypes[operateType],
                    record,
                    metaData: {
                      metaDataId: tagMetaDataConfig.metaDataId,
                      dataType: tagMetaDataConfig.type,
                    },
                    metaDataConfig: tagMetaDataConfig,
                  });
                } else {
                  // setMetaDataSelect({ open: true, record });
                }
              }}
            >
              {!isEmpty(tagMetaDataConfig) ? metaDataListOptions.find((item) => item.dataType === tagMetaDataConfig.type)?.name : '-'}
            </span>
          </div>
        );
      },
    },
    {
      title: '标签状态',
      width: '15%',
      dataIndex: 'enable',
      filters: [
        {
          text: '启用',
          value: true,
        },
        {
          text: '禁用',
          value: false,
        },
      ],
      filterMultiple: true,
      onFilter: (value, record) => {
        if (record?.enable !== null) {
          return record?.enable === value;
        } else {
          record.enable === null;
        }
        console.log(value, record, 'record');
      },
      render: (value, record) => {
        return (
          <div className={styles.tagBox}>
            {record.status === APPROVE_ENUM.ING ? (
              <div
                className={styles.tag}
                style={{
                  color: APPROVE_TYPE?.[record.status]?.color,
                  borderColor: APPROVE_TYPE?.[record.status]?.color,
                  backgroundColor: APPROVE_TYPE?.[record.status]?.backgroundColor,
                }}
              >
                {APPROVE_TYPE?.[record.status]?.label}
              </div>
            ) : (
              <div
                className={styles.tag}
                style={{
                  color: OPEN_TYPE?.[(value || 'false').toString()]?.color,
                  borderColor: OPEN_TYPE?.[(value || 'false').toString()]?.color,
                  backgroundColor: OPEN_TYPE?.[(value || 'false').toString()]?.backgroundColor,
                }}
              >
                {OPEN_TYPE?.[(value || 'false').toString()]?.label}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '创建时间',
      width: '15%',
      dataIndex: 'bizCreateTime',
      sorter: (a, b) => {
        const aTime = new Date(a.bizCreateTime).getTime();
        const bTime = new Date(b.bizCreateTime).getTime();
        return aTime - bTime;
      },
      render: (value) => {
        return <div>{value ? formatDateTime(value) : '-'}</div>;
      },
    },
    {
      title: '更新时间',
      width: '15%',
      dataIndex: 'bizUpdateTime',
      sorter: (a, b) => {
        const aTime = new Date(a.bizUpdateTime).getTime();
        const bTime = new Date(b.bizUpdateTime).getTime();
        return aTime - bTime;
      },
      render: (value) => {
        return <div>{value ? formatDateTime(value) : '-'}</div>;
      },
    },
    ['edit', 'add'].includes(operateType) && {
      title: '操作',
      width: 100,
      dataIndex: 'action',
      // fixed: 'right',
      render: (value, record) => {
        const { optType, enable, identifierFlag, tagId, tagMetaDataConfig, tempTagId } = record;
        return (
          <div className={styles.btnWrapper}>
            <Checkbox
              disabled={identifierFlag}
              checked={relateTagIds.includes(tagId)}
              onChange={(e) => {
                if (e.target.checked) {
                  setRelateTagIds([...relateTagIds, tagId]);
                } else {
                  setRelateTagIds(relateTagIds.filter((item) => item !== tagId));
                }
              }}
            />
          </div>
        );
      },
    },
  ];
  columns = columns.filter(Boolean);

  return (
    <>
      <div className={styles.tagList}>
        <div className={styles.filterWrapper}>
          <div className={styles.left}>
            <span style={{ fontWeight: 'bold' }}>标签列表</span>
          </div>
        </div>
        <div className={styles.tableWrapper}>
          <Table<DataType>
            bordered
            size="small"
            columns={columns}
            // rowKey="tagId"
            dataSource={
              operateType === 'view'
                ? configTags.filter((tag) => !tag?.deletedFlag)?.filter((tag) => relateTagIds.includes(tag.tagId))
                : configTags.filter((tag) => !tag?.deletedFlag)
            }
            scroll={{ x: 'max-content' }}
            pagination={false}
          />
        </div>
      </div>

      {metaDataDialog.open && (
        <MetaDataDialog
          open
          operateType={OperateType.view}
          metaDataId={metaDataDialog.metaData.metaDataId}
          metaDataConfig={metaDataDialog.metaDataConfig}
          onClose={() => {
            setMetaDataDialog({ open: false });
          }}
        />
      )}
    </>
  );
};
export default forwardRef(AddTagConfigLight);
