import { textCharTypeOptions } from '@/const/metadata';

export const validator = (rule, value, allowedCharacters) => {
  if (value) {
    if (!value.trim()) {
      return Promise.reject('请填写非空白内容');
    }
    const unMatchValue = allowedCharacters.reduce((str, char) => {
      const { regExp } = textCharTypeOptions.find((ite) => ite.value === char) || {};
      if (regExp) {
        return str.replace(regExp, '');
      }
      return str;
    }, value);
    if (unMatchValue) {
      return Promise.reject(
        `格式错误，请输入：${textCharTypeOptions
          .filter((ite) => allowedCharacters.includes(ite.value))
          .map((ite) => ite.label)
          .join('、')}`,
      );
    }
  }
  return Promise.resolve();
};
