import { CloseCircleFilled, DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Form, Select } from '@gwy/components-web';
import { useMemo } from 'react';
import RenderDynamicItem from './components/render-item';
import { DATA_TYPE } from './filter-const';
import styles from './index.less';

interface Iprops {
  prevfix?: any[];
  tags: any[];
  canRelateTags: any[];
}
const FilterItem = (props: Iprops) => {
  const { prevfix = [], tags = [], canRelateTags } = props;
  const form = Form.useFormInstance();

  const usedTags = useMemo(() => {
    return tags.map((tag) => ({
      ...tag,
      label: tag.name,
      value: tag.tagId,
    }));
  }, [tags]);

  const getRelationOps = (tagId) => {
    if (!tagId) return [];
    const tag = tags.find((item) => item.tagId === tagId);
    const { tagMetaDataConfig } = tag || {};
    const { type } = tagMetaDataConfig || {};
    switch (type) {
      case DATA_TYPE.TEXT.val: {
        const { metaDataTextDTO } = tagMetaDataConfig || {};
        const dataType = metaDataTextDTO?.inputType === 'MANUAL' ? DATA_TYPE.TEXT?.val : DATA_TYPE.TEXTENUM?.val;
        return (DATA_TYPE[dataType]?.relation || []).map((rela) => ({
          ...rela,
          label: rela.text,
          value: rela.val,
        }));
      }
      default: {
        return (DATA_TYPE[type]?.relation || []).map((rela) => ({
          ...rela,
          label: rela.text,
          value: rela.val,
        }));
      }
    }
  };
  const getCanuseRelateTags = (tag) => {
    const { tagMetaDataConfig, tagId } = tag || {};
    const { type } = tagMetaDataConfig || {};
    return (canRelateTags || [])
      .filter((tag) => tag?.tagMetaDataConfig?.type === type && tagId !== tag.tagId)
      .map((tag) => ({
        ...tag,
        label: tag.name,
        value: tag.tagId,
      }));
  };

  return (
    <>
      <Form form={form}>
        <Form.List name={[...prevfix, 'conditionGroups']}>
          {(fields, { add, remove }) => (
            <>
              {fields.map((field, index) => (
                <div key={field.key} className={styles.itemWrapper}>
                  <div className={styles.itemContent}>
                    <div className={styles.groupTop}>
                      <div className={styles.left}>
                        <div className={styles.groupTitle}>条件组{index + 1}</div>
                      </div>
                      <div className={styles.right}>
                        <DeleteOutlined style={{ fontSize: 16, cursor: 'pointer' }} onClick={() => remove(index)} />
                      </div>
                    </div>
                    {/* <Space align="start"> */}
                    <Form.List name={[field.name, 'conditionItems']} initialValue={[{}]}>
                      {(fieldItems, { add: addItem, remove: removeItem }) =>
                        fieldItems.map((fieldItem, idx) => (
                          <div key={idx}>
                            <div style={{ display: 'flex', alignItems: 'baseline', justifyContent: 'flex-start', columnGap: '15px', width: '100%' }}>
                              <div>{idx === 0 ? '当' : '且'}</div>
                              <Form.Item
                                name={[fieldItem.name, 'tagId']}
                                style={{ width: '120px' }}
                                rules={[{ required: true, message: '请选择标签' }]}
                              >
                                <Select
                                  placeholder="请选择标签"
                                  options={usedTags}
                                  onChange={(value) => {
                                    const tag = usedTags.find((item) => item.value === value);
                                    // const values = form.getFieldsValue();
                                    form.setFieldValue(
                                      [...prevfix, 'conditionGroups', field.name, 'conditionItems', fieldItem.name, 'type'],
                                      tag?.tagMetaDataConfig?.type,
                                    );
                                    form.setFieldValue(
                                      [...prevfix, 'conditionGroups', field.name, 'conditionItems', fieldItem.name, 'code'],
                                      tag?.code,
                                    );
                                    // form.setFieldValue([...prevfix, 'conditionGroups', field.name, 'conditionItems', fieldItem.name, 'itemLogic'],)
                                    form.setFieldValue(
                                      [...prevfix, 'conditionGroups', field.name, 'conditionItems', fieldItem.name, 'value'],
                                      undefined,
                                    );
                                    form.setFieldValue(
                                      [...prevfix, 'conditionGroups', field.name, 'conditionItems', fieldItem.name, 'operator'],
                                      undefined,
                                    );
                                    form.setFieldValue(
                                      [...prevfix, 'conditionGroups', field.name, 'conditionItems', fieldItem.name, 'valueType'],
                                      undefined,
                                    );
                                  }}
                                />
                              </Form.Item>
                              <Form.Item name="type" hidden />
                              <Form.Item name="code" hidden />
                              <Form.Item name={[fieldItem.name, 'itemLogic']} initialValue="AND" hidden />
                              <Form.Item shouldUpdate>
                                {() => {
                                  const currentTagId = form.getFieldValue([
                                    ...prevfix,
                                    'conditionGroups',
                                    field.name,
                                    'conditionItems',
                                    fieldItem.name,
                                    'tagId',
                                  ]);
                                  const relaOps = getRelationOps(currentTagId);
                                  return (
                                    <Form.Item
                                      name={[fieldItem.name, 'operator']}
                                      style={{ width: '100px' }}
                                      rules={[{ required: true, message: '请选择' }]}
                                    >
                                      <Select
                                        placeholder="请选择"
                                        options={relaOps}
                                        onChange={() => {
                                          form.setFieldValue(
                                            [...prevfix, 'conditionGroups', field.name, 'conditionItems', fieldItem.name, 'value'],
                                            undefined,
                                          );
                                        }}
                                      />
                                    </Form.Item>
                                  );
                                }}
                              </Form.Item>
                              <Form.Item shouldUpdate noStyle>
                                {() => {
                                  const currentTagId = form.getFieldValue([
                                    ...prevfix,
                                    'conditionGroups',
                                    field.name,
                                    'conditionItems',
                                    fieldItem.name,
                                    'tagId',
                                  ]);
                                  const tag = tags.find((item) => item.tagId === currentTagId);
                                  const currentRelation = form.getFieldValue([
                                    ...prevfix,
                                    'conditionGroups',
                                    field.name,
                                    'conditionItems',
                                    fieldItem.name,
                                    'operator',
                                  ]);
                                  const canRelatedTags = getCanuseRelateTags(tag);
                                  return (
                                    <RenderDynamicItem
                                      form={form}
                                      prefix={[fieldItem.name]}
                                      currentTag={tag}
                                      tags={tags}
                                      relation={currentRelation}
                                      parenntPrefix={['conditionGroups', field.name, 'conditionItems', fieldItem.name]}
                                      canRelatedTags={canRelatedTags}
                                    />
                                  );
                                }}
                              </Form.Item>

                              <span
                                onClick={() => {
                                  if (fieldItems.length - 1 === 0) {
                                    return;
                                  }
                                  removeItem(idx);
                                }}
                              >
                                <CloseCircleFilled style={{ color: '#C9CDD4', fontSize: 16, cursor: 'pointer' }} />
                              </span>
                            </div>
                            {idx === fieldItems.length - 1 && (
                              <div className={styles.addBtn} onClick={() => addItem()} style={{ marginLeft: '-5px' }}>
                                <PlusCircleOutlined />
                                <span>添加条件</span>
                              </div>
                            )}
                          </div>
                        ))
                      }
                    </Form.List>
                    <Form.Item name={[field.name, 'order']} hidden initialValue={index} />
                    {/* </Space> */}
                  </div>
                  {index === fields.length - 1 && (
                    <div
                      className={styles.addBtn}
                      onClick={() =>
                        add({
                          groupLogic: 'AND',
                        })
                      }
                      style={{ margin: '10px auto', textAlign: 'center' }}
                    >
                      <PlusCircleOutlined />
                      <span>添加条件组</span>
                    </div>
                  )}
                  <div>
                    {index !== fields.length - 1 && (
                      <Form.Item name={[field.name, 'groupLogic']} style={{ margin: '10px 0px' }} initialValue={'AND'}>
                        <Select placeholder="请选择" style={{ width: 'fit-content' }} allowClear={false}>
                          <Select.Option value="AND">且</Select.Option>
                          <Select.Option value="OR">或</Select.Option>
                        </Select>
                      </Form.Item>
                    )}
                  </div>
                </div>
              ))}
              {fields.length === 0 && (
                <div className={styles.addBtnWrapper}>
                  <Button
                    type="link"
                    icon={<PlusCircleOutlined />}
                    onClick={() => {
                      add({
                        groupLogic: 'AND',
                      });
                    }}
                  >
                    添加条件组
                  </Button>
                </div>
              )}
            </>
          )}
        </Form.List>
      </Form>
      {/* 筛选分组 */}
    </>
  );
};

export default FilterItem;
