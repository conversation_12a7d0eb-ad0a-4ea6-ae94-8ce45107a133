import { DataSave } from '@/types/tag';
import classNames from 'classnames';
import { CSSProperties } from 'react';
import styles from './index.less';

type Props = {
  type?: DataSave;
  style?: CSSProperties;
  className?: string;
};

const TagIOType = ({ type, style, className }: Props) => {
  return (
    <div className={classNames(styles.container, className, type === DataSave.INPUT ? styles.input : styles.output)} style={{ ...style }}>
      {type === DataSave.INPUT ? '输入' : '输出'}
    </div>
  );
};

export default TagIOType;
