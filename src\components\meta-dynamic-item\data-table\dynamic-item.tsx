import CodeMeta from '@/components/meta-dynamic-item/code';
import NumberMeta from '@/components/meta-dynamic-item/number';
import ObjectMeta from '@/components/meta-dynamic-item/object';
import TextMeta from '@/components/meta-dynamic-item/text';
import TimeMeta from '@/components/meta-dynamic-item/time';
import UnitMeta from '@/components/meta-dynamic-item/unit';
import { MetaDataType } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { memo, useCallback } from 'react';
import AddressMeta from '../address';
import { UseIn } from '../const';
import FileMeta from '../file';

type Props = {
  value?: any;
  onChange?: (value?: any) => void;
  tag?: DataUnitTagVO;
  field?: FormField;
  prefix?: string[]; // form前缀路径
  isPreview?: boolean; // 仅预览模式
};

const DynamicItem = ({ tag, field, value, onChange, ...otherProps }: Props) => {
  const type = tag?.tagMetaDataConfig?.type;
  const { readonly } = field || {};
  const { value: val, data } = value || {};

  const _value = readonly ? value : data;
  const handleChange = useCallback(
    (data) => {
      onChange && onChange({ value: val, data });
    },
    [val, onChange],
  );

  switch (type) {
    case MetaDataType.Text: {
      return <TextMeta useIn={UseIn.App} tag={tag} field={field} value={_value} onChange={handleChange} {...(otherProps || {})} />;
    }
    case MetaDataType.Number: {
      return <NumberMeta useIn={UseIn.App} tag={tag} field={field} value={_value} onChange={handleChange} {...(otherProps || {})} />;
    }
    case MetaDataType.DateTime: {
      return <TimeMeta useIn={UseIn.App} tag={tag} field={field} value={_value} onChange={handleChange} {...(otherProps || {})} />;
    }
    case MetaDataType.Object: {
      return <ObjectMeta useIn={UseIn.App} tag={tag} field={field} value={_value} onChange={handleChange} {...(otherProps || {})} />;
    }
    case MetaDataType.Unit: {
      return <UnitMeta useIn={UseIn.App} tag={tag} field={field} value={_value} onChange={handleChange} {...(otherProps || {})} />;
    }
    case MetaDataType.Code: {
      return <CodeMeta useIn={UseIn.App} tag={tag} field={field} value={_value} onChange={handleChange} {...(otherProps || {})} />;
    }
    case MetaDataType.File: {
      return <FileMeta useIn={UseIn.App} tag={tag} field={field} value={_value} onChange={handleChange} {...(otherProps || {})} />;
    }
    case MetaDataType.Address: {
      return <AddressMeta useIn={UseIn.App} tag={tag} field={field} value={_value} onChange={handleChange} {...(otherProps || {})} />;
    }
    default: {
      return null;
    }
  }
};

export default memo(DynamicItem);
