import { Timeline } from '@gwy/components-web';

type IProps = {
  timeLineList: any[];
  clickFun: (value) => void;
};
const HistoryVersion = (props: IProps) => {
  const { timeLineList, clickFun } = props;
  console.log(timeLineList, 'timeLineList');

  const items = timeLineList?.map((item) => ({
    children: (
      <div
        onClick={() => clickFun(item)}
        style={{
          padding: '4px',
          background: '#f7f8fa',
        }}
      >
        <span
          style={{
            fontWeight: 'bold',
          }}
        >
          版本号：
        </span>
        <span>{item?.version || 'V1.0'}</span>
        <span style={{ marginLeft: '20px' }}>{item?.updateTime || item?.createTime || '_'}</span>
      </div>
    ),
  }));
  return (
    <div
      style={{
        padding: '15px 0',
      }}
    >
      <Timeline items={items} />
    </div>
  );
};

export default HistoryVersion;
