import { SelectedStateType } from '@/pages/app-designer/const';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { FormField, SourceType } from '@/types/form-field';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { produce } from 'immer';
import { forwardRef, useContext, useImperativeHandle, useMemo, useRef } from 'react';
import SectionHeader from '../../../section-header';
import CalcFieldConfig from './calc-field-config';
import FormItemConfig, { FormItemConfigRef } from './form-item-config';
import styles from './index.less';

export type PropertyConfigRef = {
  validateFields: () => Promise<any>;
};
const PropertyConfig = forwardRef<PropertyConfigRef, any>((props, ref) => {
  const {
    post,
    configuredDataUnits,
    allDataUnitsWithTags,
    calcFields,
    configuredFields,
    setConfiguredFields,
    combineTagConfig,
    setCombineTagConfig,
    tableList,
    setTableList,
    setCalcFields,
    selectedState,
    tagsMap,
  } = useContext(AppDesignerContext);
  const activeId = selectedState.formItemId;

  const activeFormField = useMemo(() => {
    if (selectedState.type === SelectedStateType.baseGroupItem) {
      return configuredFields.find((f) => getMetaTagUniqueId(f) === activeId);
    } else if (selectedState.type === SelectedStateType.combineTagItem) {
      return combineTagConfig?.fields?.find((f) => getMetaTagUniqueId(f) === activeId);
    } else if (selectedState.type === SelectedStateType.tableItem) {
      return tableList?.find((t) => t.id === selectedState.tableId)?.fields?.find((f) => getMetaTagUniqueId(f) === activeId);
    }
  }, [activeId, configuredFields, selectedState, combineTagConfig, tableList]);
  const activeTag = tagsMap[activeId];

  const formRef = useRef<FormItemConfigRef>();

  useImperativeHandle(ref, () => ({
    validateFields: () => {
      return formRef.current?.validateFields();
    },
  }));

  const renderTag = () => {
    return (
      <FormItemConfig
        key={activeId + selectedState.type}
        ref={formRef}
        tag={activeTag}
        field={activeFormField}
        onChange={(values) => {
          const { readonly, fieldName, placeholder, required, widgetWith, inputDefaultConfig, ...config } = values || {};
          const updateFormItem = (formItem: FormField) => {
            formItem.readonly = readonly;
            formItem.fieldName = fieldName;
            formItem.placeholder = placeholder;
            formItem.required = required;
            formItem.widgetWith = widgetWith;
            formItem.inputDefaultConfig = inputDefaultConfig;
            formItem.config = {
              ...formItem.config,
              ...config,
            };
          };

          if (selectedState.type === SelectedStateType.baseGroupItem) {
            const cFieldsNew = produce(configuredFields, (draft) => {
              const formItem = draft.find((f) => getMetaTagUniqueId(f) === activeId);
              updateFormItem(formItem);
            });
            setConfiguredFields(cFieldsNew);
          } else if (selectedState.type === SelectedStateType.combineTagItem) {
            const cFieldsNew = produce(combineTagConfig, (draft) => {
              const formItem = draft.fields.find((f) => getMetaTagUniqueId(f) === activeId);
              updateFormItem(formItem);
            });
            setCombineTagConfig(cFieldsNew);
          } else if (selectedState.type === SelectedStateType.tableItem) {
            const cFieldsNew = produce(tableList, (draft) => {
              const table = draft.find((t) => t.id === selectedState.tableId);
              const formItem = table.fields.find((f) => getMetaTagUniqueId(f) === activeId);
              updateFormItem(formItem);
            });
            setTableList(cFieldsNew);
          }
        }}
      />
    );
  };

  const renderCalcField = () => {
    return (
      <div className={styles.combineFieldConfigWrapper}>
        <SectionHeader title="字段配置" />
        <CalcFieldConfig
          key={activeId + selectedState.type}
          ref={formRef}
          field={activeFormField}
          postId={post?.postId}
          configuredDataUnits={configuredDataUnits}
          allDataUnitsWithTags={allDataUnitsWithTags}
          calcFields={calcFields}
          onChange={(values) => {
            const updateFormItem = (formItem: FormField) => {
              const { formFieldConfig, ...rest } = values || {};
              formItem.fieldConfig = {
                ...formItem.fieldConfig,
                ...rest,
                formFieldConfig: {
                  ...formItem.fieldConfig?.formFieldConfig,
                  ...formFieldConfig,
                },
              };
            };

            if (selectedState.type === SelectedStateType.baseGroupItem) {
              const cFieldsNew = produce(configuredFields, (draft) => {
                const formItem = draft.find((f) => getMetaTagUniqueId(f) === activeId);
                updateFormItem(formItem);
              });
              setConfiguredFields(cFieldsNew);
            } else if (selectedState.type === SelectedStateType.combineTagItem) {
              const cFieldsNew = produce(combineTagConfig, (draft) => {
                const formItem = draft.fields.find((f) => getMetaTagUniqueId(f) === activeId);
                updateFormItem(formItem);
              });
              setCombineTagConfig(cFieldsNew);
            } else if (selectedState.type === SelectedStateType.tableItem) {
              const cFieldsNew = produce(tableList, (draft) => {
                const table = draft.find((t) => t.id === selectedState.tableId);
                const formItem = table.fields.find((f) => getMetaTagUniqueId(f) === activeId);
                updateFormItem(formItem);
              });
              setTableList(cFieldsNew);
            }

            // 同步更新计算字段
            setCalcFields(
              produce(calcFields, (draft) => {
                const calcField = draft.find((f) => f.fieldId === activeId);
                const { formFieldConfig, ...rest } = values || {};
                Object.assign(calcField, rest);
              }),
            );
          }}
        />
      </div>
    );
  };

  return (
    <div className={styles.container}>
      {activeFormField?.sourceType === SourceType.Tag && renderTag()}
      {activeFormField?.sourceType === SourceType.Field && renderCalcField()}

      {configuredFields?.length === 0 && <div className={styles.emptyWrapper}>请在左侧画布中选中标签或字段</div>}
    </div>
  );
});

export default PropertyConfig;
