import { CDN_URL_DATASOURCE } from '@/const/common';
import { orgAPI } from '@/services';
import { getFirstChildPost, getMergeTreeData } from '@/utils/misc';
import { CaretRightOutlined } from '@ant-design/icons';
import { Collapse } from 'antd';
import classNames from 'classnames';
import _ from 'lodash';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import styles from './index.module.less';

interface IpProps {
  setBaseInfo: (p: any) => void;
  searchInput: any;
}
const PostListIndex = forwardRef<any, IpProps>((props, ref) => {
  const { setBaseInfo, searchInput } = props;
  const [postList, setPostList] = useState([]);
  const [currentPostId, setCurrentPostId] = useState('');
  const [selectParams, setSelectParams] = useState({
    postId: undefined,
    selectedPost: {},
    current: 1,
    pageSize: 10,
  });

  useImperativeHandle(ref, () => ({
    setCurrentPostId,
    setSelectParams,
  }));

  const findTreePosts = async () => {
    try {
      const data = await orgAPI.getOrgPostTree();
      const postList = getMergeTreeData(data);
      setPostList(postList);
      const firstPost = getFirstChildPost(postList) as any;
      // const posts = collectPosts(data || []) || [];
      // const mainPost = posts.find((item) => item.deptProperty === 2 && item.ifMainPost);
      // const selectPost = isEmpty(mainPost) ? firstPost : mainPost;
      const selectPost = firstPost;
      setCurrentPostId(selectPost?.postId);
      setSelectParams((pre) => ({
        ...pre,
        postId: (selectPost || {})?.postId || '',
        selectedPost: selectPost || {},
      }));
    } catch (e) {}
  };
  useEffect(() => {
    findTreePosts();
  }, []);

  const filterPosts = useMemo(() => {
    const memoData = _.cloneDeep(postList);
    const checkPassFn = (item, searchValue) => {
      const { postId, postName, title, orgId, blocId, deptName } = item || {};
      if (postId && postName) {
        return postName.includes(searchValue);
      }
      if (!postId && deptName) {
        return deptName.includes(searchValue);
      }
      return false;
    };
    const filterFunc = (orgList) => {
      // 说明是集团
      return orgList
        .map((org) => {
          if (
            (org?.blocId && !org.postId && org?.title.includes(searchInput)) ||
            // 对部门
            (!org.postId && org?.deptName && org?.deptName.includes(searchInput)) ||
            // 对岗位
            (org.postId && org?.postName.includes(searchInput))
          ) {
            return org;
          }
          if (org.children && org.children.length) {
            return {
              ...org,
              children: filterFunc(org.children),
            };
          }
          return undefined;
        })
        .filter((item) => checkPassFn(item, searchInput) || (item?.children && item?.children?.length));
    };
    if (searchInput) {
      const data = filterFunc(memoData);
      return data;
    }
    return postList;
  }, [searchInput, postList]);
  const expandPostKeys = useMemo(() => {
    const handleIds = (OrgList) => {
      return OrgList.reduce((prev, cur) => {
        const tempArr = [];
        const targetId = cur.postId || cur.deptId || cur.blocId;
        tempArr.push(targetId);
        if (cur?.children?.length) {
          tempArr.push(...handleIds(cur?.children || []));
        }
        return prev.concat(tempArr);
      }, []).filter(Boolean);
    };
    let expandPostKeys = handleIds(filterPosts);
    // 利用set去重
    expandPostKeys = Array.from(new Set(expandPostKeys));
    return expandPostKeys;
  }, [filterPosts]);
  const handleActive = (item) => {
    console.log('handleActive');
    const { postId } = item;
    postId && setCurrentPostId(postId);
    setSelectParams((pre) => ({
      ...pre,
      postId: item.postId,
      selectedPost: item,
    }));
  };
  useEffect(() => {
    setBaseInfo((pre) => ({ ...pre, ...selectParams }));
  }, [selectParams]);
  const renderOrgAndPost = (orgList, rank) => {
    return (
      <div>
        {!!orgList &&
          !!orgList?.length &&
          orgList.map((item) => {
            const { children, title, orgId, blocId, postId, deptName, postName, deptId, label } = item;
            const uniqOrgId = blocId || deptId;
            const existPostId = !!item?.postId;
            const activeNow = currentPostId === item.postId;
            const hasChildren = children && children.length;
            if (rank === 0) {
              return (
                <>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      fontWeight: 'normal',
                      padding: '6px 0 6px 8px',
                      margin: '0 12px',
                    }}
                  >
                    {/* 'org_icon_selected.png' */}
                    <img src={`${CDN_URL_DATASOURCE}/form-manage/${'org_icon.png'}`} width={16} alt="" style={{ marginRight: 6 }} />
                    <span className={styles.topTitle}>{item?.deptName}</span>
                  </div>
                  {!!children && !!children?.length && renderOrgAndPost(children, rank + 1)}
                </>
              );
            }
            return (
              <div key={uniqOrgId}>
                {hasChildren ? (
                  <Collapse
                    ghost
                    defaultActiveKey={expandPostKeys}
                    collapsible={postId ? 'icon' : 'header'}
                    expandIcon={({ isActive }) => (
                      <CaretRightOutlined
                        rotate={isActive ? 90 : 0}
                        style={{
                          paddingLeft: rank === 1 ? 26 : (rank - 1) * 18 + 26,
                        }}
                      />
                    )}
                  >
                    <Collapse.Panel
                      key={item.postId || item.deptId || item.blocId}
                      className={classNames({
                        [styles.canSelect]: !!postId,
                        selectedHeader: activeNow,
                      })}
                      header={
                        postId ? (
                          <span onClick={() => handleActive(item)}>
                            <div>{postName}</div>
                          </span>
                        ) : (
                          deptName
                        )
                      }
                    >
                      {/* {!!item?.appFolderVO && renderCardItem(item?.appFolderVO)} */}
                      {!!children && !!children?.length && renderOrgAndPost(children, rank + 1)}
                    </Collapse.Panel>
                  </Collapse>
                ) : (
                  <div
                    className={classNames({
                      // [styles.canSelect]: memoList.includes(item.postId),
                      [styles.canSelect]: true,
                      [styles.selected]: activeNow,
                    })}
                    style={{
                      // display: memoList.includes(item.postId) ? 'block' : 'none',
                      fontWeight: existPostId ? 'normal' : '600',
                      paddingTop: 6,
                      paddingBottom: 6,
                      margin: '0 12px',
                      paddingLeft: rank === 1 ? 42 - 12 : (rank - 1) * 18 + 43 - 12,
                      cursor: existPostId ? 'pointer' : 'none',
                      background: existPostId && activeNow ? '#E8F3FF' : 'none',
                      // color: memoList.includes(item.postId) ? '#000' : '#949da7',
                    }}
                    onClick={() => handleActive(item)}
                  >
                    <div>{label}</div>
                  </div>
                )}
              </div>
            );
          })}
      </div>
    );
  };

  return (
    <div
      className={styles.wrapper}
      style={{
        height: 'calc(100vh - 250px)',
        overflow: 'auto',
        paddingBottom: '30px',
      }}
    >
      {renderOrgAndPost(filterPosts, 0)}
      <div
        className={classNames({
          [styles.canSelect]: true,
          [styles.selected]: !currentPostId,
        })}
        style={{
          paddingTop: 5,
          paddingBottom: 5,
          margin: '0 12px',
          paddingLeft: 20 - 12,
          cursor: 'pointer',
        }}
        onClick={() => {
          setCurrentPostId('');
          setSelectParams((pre) => ({
            ...pre,
            postId: '',
            selectedPost: {},
          }));
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img
            src={`${CDN_URL_DATASOURCE}/form-manage/${currentPostId ? 'user_icon.png' : 'user_icon_selected.png'}`}
            width={16}
            alt=""
            style={{ marginRight: 6 }}
          />
          个人表单
        </div>
      </div>
    </div>
  );
});

export default PostListIndex;
