import { MetaUnitType } from '@/const/metadata';
import { useRoute } from '@/hooks';
import { openType } from '../../metadata-manage/components/metadata-list';
import MetaDataEdit from '../components/edit-modal';
interface RouteState {
  record?: {
    bizParam?: string;
  };
  onClose?: () => void;
}
const ApproveMpdal = () => {
  const { state } = useRoute<RouteState>();
  const { record, onClose } = state || {};
  const { approveId } = JSON.parse(record.bizParam ?? '{}');

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <MetaDataEdit
        onCancel={() => onClose?.()}
        onOk={() => onClose?.()}
        operateType={openType.approveDataSource}
        approveId={approveId}
        dataSourceType={MetaUnitType.Normal}
        editPermission
      />
    </div>
  );
};

export default ApproveMpdal;
