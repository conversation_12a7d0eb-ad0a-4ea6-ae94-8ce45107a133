import MetaDataDialog from '@/components/metadata-dialog';
import MetaDataList from '@/components/metadata-select/metadata-list';
import { CodeNumberType, CodeType, CustomCode } from '@/const/metadata';
import { MetaDataConfigVO, MetaDataVO } from '@/types/metadata';
import { Tabs } from '@gwy/components-web';
import { useState } from 'react';
import styles from './index.less';

const DatametaManage = () => {
  const [metaDataDialog, setMetaDataDialog] = useState<{
    open?: boolean;
    metaData?: MetaDataVO;
    metaDataConfig?: MetaDataConfigVO;
    metaDataConfigApproved?: MetaDataConfigVO;
  }>({});

  return (
    <div className={styles.container}>
      <Tabs
        type="card"
        items={[
          {
            label: `数元字典`,
            key: '1',
            children: (
              <div className={styles.tabContent}>
                <MetaDataList
                  searchWrapperStyle={{ width: 410 }}
                  onSelect={(metaData) => {
                    setMetaDataDialog({
                      open: true,
                      metaData,
                      metaDataConfig: {
                        metaDataId: metaData.metaDataId,
                        type: metaData.dataType,
                        metaDataCodeDTO: {
                          type: CodeType.custom,
                          elementTypeList: [CustomCode.chinese, CustomCode.number, CustomCode.english_lower, CustomCode.date],
                          configList: [
                            {
                              id: '1',
                              elementList: [
                                {
                                  id: '1',
                                  type: CustomCode.chinese,
                                  maxLength: 4,
                                },
                                {
                                  id: '2',
                                  type: CustomCode.number,
                                  maxLength: 4,
                                  numberForm: CodeNumberType.increment,
                                },
                              ],
                            },
                          ],
                        },
                      },
                      metaDataConfigApproved: {
                        metaDataId: metaData.metaDataId,
                        type: metaData.dataType,
                        metaDataCodeDTO: {
                          type: CodeType.custom,
                          elementTypeList: [CustomCode.chinese, CustomCode.number],
                          configList: [
                            {
                              id: '1',
                            },
                          ],
                        },
                      },
                    });
                  }}
                />
              </div>
            ),
          },
        ]}
      />

      {metaDataDialog.open && (
        <MetaDataDialog
          open
          defaultActiveTab={'1'}
          hideFooter
          metaDataId={metaDataDialog.metaData.metaDataId}
          // metaDataConfig={metaDataDialog.metaDataConfig}
          // metaDataConfigApproved={metaDataDialog.metaDataConfigApproved}
          onClose={() => {
            setMetaDataDialog({ open: false });
          }}
          onOk={(values) => {
            console.log('values=====', values);
          }}
        />
      )}
    </div>
  );
};

export default DatametaManage;
