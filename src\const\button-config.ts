export enum ButtonOperateType {
  LAUNCH = 'LAUNCH',
  AGREE = 'AGREE',
  REFUSE = 'REFUSE',
  RETURN = 'RETURN',
  END = 'END',
}

export enum ButtonStyle {
  Primary = 'primary',
  PrimaryGhost = 'primary-ghost',
  Danger = 'danger',
  Default = 'default',
}

export const buttonOptions = [
  { label: '发起', value: ButtonOperateType.LAUNCH, defaultButtonStyle: ButtonStyle.Primary },
  { label: '同意', value: ButtonOperateType.AGREE, defaultButtonStyle: ButtonStyle.Primary },
  { label: '拒绝', value: ButtonOperateType.REFUSE, defaultButtonStyle: ButtonStyle.Danger },
  { label: '退回', value: ButtonOperateType.RETURN, defaultButtonStyle: ButtonStyle.PrimaryGhost },
  { label: '完结', value: ButtonOperateType.END, defaultButtonStyle: ButtonStyle.Default },
];
