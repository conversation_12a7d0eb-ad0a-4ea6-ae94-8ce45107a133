import { SourceType } from '@/types/form-field';

// 表单类型
export enum FormType {
  Normal = 'NORMAL', // 普通表单
}

// 应用类型
export enum AppType {
  SUBMIT = 'SUBMIT', // 发起
  Approve = 'APPROVE', // 审批
}

// 选中状态类型
export enum SelectedStateType {
  baseGroup = 'baseGroup', // 基础分组
  baseGroupItem = 'baseGroupItem', // 基础分组的项（标签、字段）
  combineTag = 'combineTag', // 组合标签
  combineTagItem = 'combineTagItem', // 组合标签的项
  table = 'table', // 数据表格
  tableItem = 'tableItem', // 数据表格的项
  buttonList = 'buttonList', // 按钮设置
}

// 选中状态
export type SelectedState = {
  type: SelectedStateType; // 选中类型
  formItemId?: string; // 标签id或者字段id
  formItemType?: SourceType; // 区分是标签还是字段
  tableId?: string; // 表格id
};
