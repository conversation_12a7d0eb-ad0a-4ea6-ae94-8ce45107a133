import DataStatistics from '@/pages/platform/metadata-manage/components/metadata-edit-modal/components/edit-basicinfo/data-statistics';
import { getDatasourceCategoryList } from '@/services/datasource';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Col, Form, Input, Modal, Row, Tooltip } from '@gwy/components-web';
import { isEmpty } from 'lodash-es';
import { forwardRef, ForwardRefRenderFunction, useEffect, useImperativeHandle, useState } from 'react';
import styles from './index.less';
type Iprops = {
  operateType: 'add' | 'edit' | 'approve' | 'view';
  dataSourceType: 'NORMAL' | 'GENERAL';
  editData?: any;
  compareData?: any;
};

type IRef = any;

enum OPERATE_TYPE {
  EDIT = 'edit',
  ADD = 'add',
  VIEW = 'view',
  APPROVE = 'approve',
}
const EditBasicInfo: ForwardRefRenderFunction<IRef, Iprops> = (props: Iprops, ref) => {
  const { operateType, dataSourceType, editData, compareData } = props;
  const [form] = Form.useForm();
  const [dataSourceCateOps, setDataSourceOps] = useState([]);
  const [enable, setEnable] = useState(false);
  useImperativeHandle(ref, () => ({
    getValues: async () => {
      try {
        const values = await form.validateFields();
        return {
          ...editData,
          enable,
          ...(values || {}),
        };
      } catch (e: any) {
        const formData = form.getFieldsValue();
        const { name, categoryId } = formData;
        if (!name) {
          return Promise.reject('当前数据单元名称未填写，请填写后再次提交');
        }
        if (!categoryId) {
          return Promise.reject('未选择分类，请填写后再次提交');
        }
        const { errorFields } = e || {};
        if (errorFields?.length > 0 && Array.isArray(errorFields)) {
          return Promise.reject(errorFields[0].errors[0]);
        }
      }
    },
  }));
  const getDataSourceOps = async () => {
    const data = await getDatasourceCategoryList({
      type: dataSourceType,
    });
    setDataSourceOps(
      data.map((item) => ({
        label: item.categoryName,
        value: item.categoryId,
        key: item.categoryId,
      })),
    );
  };

  useEffect(() => {
    getDataSourceOps();
  }, []);

  useEffect(() => {
    if (!isEmpty(editData)) {
      console.log(editData, 'editData---');
      form.setFieldsValue({
        ...editData,
      });
      setEnable(editData.enable);
    }
  }, [editData, compareData, operateType]);

  const renderDiff = (diffKey, renderOps?) => {
    if (isEmpty(compareData)) {
      return null;
    }

    if (!isEmpty(editData) && !isEmpty(compareData) && editData[diffKey] !== compareData[diffKey]) {
      let title = renderOps?.label ? renderOps?.label : `修改前：${compareData[diffKey]}`;
      if (compareData[diffKey] === null) {
        title = '修改前：无';
      }
      return (
        <Tooltip title={title}>
          <ExclamationCircleOutlined className={styles.diffIcon} />
        </Tooltip>
      );
    }

    return null;
  };

  const handleEnable = (value) => {
    return Modal.confirm({
      title: `是否${value ? '启用' : '禁用'}数据单元`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        setEnable(value);
      },
    });
  };

  return (
    <div className={styles.basicEditWrap}>
      <Form form={form}>
        <Row gutter={24}>
          <Col span={24}>
            {operateType === OPERATE_TYPE.EDIT ? (
              <Form.Item name="name" label="类型名称" rules={[{ required: true, message: '请输入数据元名称' }]}>
                <Input placeholder="请输入类型名称" maxLength={20} />
              </Form.Item>
            ) : (
              <Form.Item label="类型名称">
                <span className="ant-form-text">{editData?.name}</span>
                {operateType === OPERATE_TYPE.APPROVE && renderDiff('name')}
              </Form.Item>
            )}
          </Col>
          <Col span={12}>
            <Form.Item label="创建人">
              <span className="ant-form-text">{editData?.createUser}</span>
              {operateType === OPERATE_TYPE.APPROVE && renderDiff('createUser')}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="创建时间">
              <span className="ant-form-text">{editData?.bizCreateTime}</span>
              {operateType === OPERATE_TYPE.APPROVE && renderDiff('bizCreateTime')}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="更新人">
              <span className="ant-form-text">{editData?.updateUser}</span>
              {operateType === OPERATE_TYPE.APPROVE && renderDiff('updateUser')}
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="更新时间">
              <span className="ant-form-text">{editData?.bizUpdateTime}</span>
              {operateType === OPERATE_TYPE.APPROVE && '-'}
            </Form.Item>
          </Col>
          <Col span={24}>
            {operateType === OPERATE_TYPE.EDIT ? (
              <Form.Item label="类型描述" name="description">
                <Input.TextArea placeholder="请输入类型描述" rows={4} maxLength={300} />
              </Form.Item>
            ) : (
              <Form.Item label="类型描述" name="description">
                <span className="ant-form-text">{editData?.description}</span>
                {operateType === OPERATE_TYPE.APPROVE &&
                  renderDiff('description', { label: compareData?.description ? `修改前：${compareData?.description}` : `修改前：无` })}
              </Form.Item>
            )}
          </Col>
        </Row>
      </Form>

      {editData?.dataUnitId && <DataStatistics dataUnit={editData} />}
    </div>
  );
};

export default forwardRef(EditBasicInfo);
