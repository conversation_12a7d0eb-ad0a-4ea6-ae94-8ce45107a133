import { EnumType } from '@/const/metadata';
import { datasourceAPI } from '@/services';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { cloneDeep } from 'lodash-es';
import { memo, useEffect, useState } from 'react';
import { UseIn } from '../../const';
import CustomTreeSelect, { fieldNames } from './custom-tree-select';

// 过滤包含/排除的选项
const loop = (list, enumListLimit, appoint, enumType) => {
  return list.filter((item) => {
    let subList = [];
    if (item[fieldNames[enumType].children] && item[fieldNames[enumType].children].length) {
      item.selectable = false;
      subList = loop(item.subEnumList, enumListLimit, appoint, enumType);
    }

    let match = enumListLimit?.some((id) => id === item[fieldNames[enumType].value]);
    if (!appoint) {
      match = !match;
      if (match && Array.isArray(item[fieldNames[enumType].children]) && item[fieldNames[enumType].children].length && !subList.length) {
        match = !match;
      }
    }
    item[fieldNames[enumType].children] = subList;
    return match || (subList && subList.length);
  });
};

const TextEnumMeta = memo<{
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  isPreview?: boolean;
  options?: any;
}>(({ useIn, tag, field, value, onChange, isPreview, options }) => {
  const { enumList: metaEnumList, enumType, dictCode } = tag.tagMetaDataConfig?.metaDataTextDTO || {};
  const { placeholder } = field || {};
  const { multipleChoice, appoint } = field?.config || {};
  let { enumList: enumListLimit } = field?.config?.metaDataTextDTO || {};

  // if (options?.length > 0) {
  //   enumListLimit = enumListLimit.filter((item) => !!options.find((option) => option === item));
  // }

  const [enumList, setEnumList] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    console.log(options, 'options------------');
    if (enumType === EnumType.general) {
      setLoading(true);
      try {
        datasourceAPI.getDictTreeDataByCode(dictCode).then((res) => {
          setEnumList(loop(res || [], enumListLimit, appoint, enumType));
        });
      } catch (error) {}
      setLoading(false);
    } else {
      setEnumList(loop(cloneDeep(metaEnumList || []), enumListLimit, appoint, enumType));
    }
  }, [metaEnumList, enumListLimit, appoint, enumType, dictCode, options]);

  if (field?.readonly || isPreview) {
    if (useIn === UseIn.Designer) {
      return '回显数据';
    }
    const { value: tagValue, data: tagData } = value || {};
    return tagValue ?? '-';
  }

  return (
    <CustomTreeSelect
      enumType={enumType}
      multiple={multipleChoice}
      treeData={enumList}
      loading={loading}
      value={value}
      onChange={onChange}
      style={{ width: '100%' }}
      placeholder={placeholder || '请选择'}
    />
  );
});

export default TextEnumMeta;
