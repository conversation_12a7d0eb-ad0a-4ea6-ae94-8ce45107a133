import { AddressType } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { memo } from 'react';
import { UseIn } from '../const';
import AreaItem from './area';
import MapItem from './map';

export type AddressMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  isPreview?: boolean;
  onChange?: (value) => void;
  propsStyles?: React.CSSProperties;
};

const AddressMeta = memo(({ isPreview, useIn, tag, field, value, onChange, propsStyles }: AddressMetaProps) => {
  const { type } = tag.tagMetaDataConfig.metaDataAddressDTO;
  const { readonly } = field || {};
  const { metaDataAddressDTO } = field?.config || {};
  const {} = metaDataAddressDTO || {};

  if (readonly || isPreview) {
    if (useIn === UseIn.Designer) {
      return '回显数据';
    }
    const { value: tagValue, data: tagData } = value || {};
    return tagValue ?? '-';
  }
  return [AddressType.area, AddressType.detail].includes(type) ? (
    <AreaItem value={value} onChange={onChange} tag={tag} field={field} propsStyles={propsStyles} />
  ) : (
    <MapItem value={value} onChange={onChange} tag={tag} field={field} />
  );
});

export default AddressMeta;
