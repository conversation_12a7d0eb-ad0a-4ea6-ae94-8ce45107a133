import CodeMeta from '@/components/meta-dynamic-item/code';
import NumberMeta from '@/components/meta-dynamic-item/number';
import ObjectMeta from '@/components/meta-dynamic-item/object';
import TextMeta from '@/components/meta-dynamic-item/text';
import TimeMeta from '@/components/meta-dynamic-item/time';
import UnitMeta from '@/components/meta-dynamic-item/unit';
import { MetaDataType } from '@/const/metadata';
import { queryMutData } from '@/services/datasource-data';
import { SourceType } from '@/types/form-field';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Col, Empty, Form, Modal, Row, Tooltip } from '@gwy/components-web';
import { isEmpty } from 'lodash-es';
import { useEffect, useMemo, useState } from 'react';
import styles from './idnex.less';
import TableAndCombine from './table-combine';
interface Iprops {
  onOk: (values?: any) => void;
  onCancel: () => void;
  formVersion: any;
  mainDataId: any;
  cTagsMap: any;
  post?: any;
  // cFields?: any;
}
const MainDataView = (props: Iprops) => {
  const { onOk, onCancel, cTagsMap, mainDataId, formVersion, post } = props;
  const [mainDetail, setMainDetail] = useState(null);
  const [form] = Form.useForm();
  const [datasource, setDataSource] = useState([]);
  const [cFields, tableList] = useMemo(() => {
    const cFields = (formVersion?.extConfig?.fields || [])?.map((item) => ({ ...item, readonly: true }));
    const tableList = formVersion?.extConfig?.tableConfigs;
    return [cFields, tableList];
  }, [formVersion]);

  const findRecords = async () => {
    const DataUnitMain = formVersion?.dataUnits?.find((dataUnit) => dataUnit?.mainDataUnit) || {};
    const queryParams = {
      formVersionId: formVersion?.formVersionId,
      postId: post?.postId,
      queryConfig: {
        dataUnits: formVersion?.dataUnits,
        queryFormType: 1, // 1是基础数据，2是数据表格
        dataId: mainDataId,
        tableConFig: {
          dataUnitId: DataUnitMain?.dataUnitId,
          dataId: mainDataId,
        },
      },
    };
    const data = await queryMutData(queryParams);
    const { records } = data || {};
    setDataSource(records);
  };

  useEffect(() => {
    console.log(mainDataId, formVersion, post?.postId, 'main-data-view');
    if (mainDataId && !isEmpty(formVersion) && post?.postId) {
      findRecords();
    }
  }, [mainDataId, formVersion, post]);

  const renderItems = () => {
    return cFields?.map((formField, index) => {
      const uniqueId = getMetaTagUniqueId(formField);
      const renderTag = () => {
        const tag = cTagsMap[uniqueId];
        if (!tag) {
          return null;
        }
        const record = datasource[0];
        if (!record) {
          return null;
        }
        // 获取value值
        const renderCell = () => {
          const type = tag?.tagMetaDataConfig?.type;
          const value = record[formField.code];
          console.log(value, 'value*************');
          switch (type) {
            case MetaDataType.Text: {
              return <TextMeta tag={tag} field={formField} value={value} />;
            }
            case MetaDataType.Number: {
              return <NumberMeta tag={tag} field={formField} value={value} />;
            }
            case MetaDataType.DateTime: {
              return <TimeMeta tag={tag} field={formField} value={value} />;
            }
            case MetaDataType.Object: {
              return <ObjectMeta tag={tag} field={formField} value={value} />;
            }
            case MetaDataType.Unit: {
              return <UnitMeta tag={tag} field={formField} value={value} />;
            }
            case MetaDataType.Code: {
              return <CodeMeta tag={tag} field={formField} value={value} />;
            }
            default: {
              return null;
            }
          }
        };

        return (
          <Form.Item
            label={
              <div className={styles.labelWrapper}>
                <span>
                  <span className={styles.labelName}>{formField.fieldName || tag.name}</span>
                  {formField.fieldName && (
                    <Tooltip title={`原标签名称：${tag.name}`}>
                      <InfoCircleOutlined style={{ marginLeft: 5, color: '#4D7BF6' }} />
                    </Tooltip>
                  )}
                </span>
              </div>
            }
          >
            {renderCell()}
          </Form.Item>
        );
      };

      const renderCombineField = () => {
        return (
          <>
            <Form.Item
              label={
                <div className={styles.labelWrapper}>
                  <span>{formField.combineConfig?.name}</span>
                </div>
              }
            >
              <span>回显数据</span>
            </Form.Item>
          </>
        );
      };

      return (
        <Col span={12} key={uniqueId}>
          <div className={styles.formItemWrapper}>
            {formField.sourceType === SourceType.Tag && renderTag()}
            {formField.sourceType === SourceType.Field && renderCombineField()}
          </div>
        </Col>
      );
    });
  };

  const renderDynamincForm = () => {
    return (
      <Form layout="vertical" form={form} className={styles.dynamincFormStyle}>
        {/* <Form.Item label="名称" name="tagList">  */}
        <Row gutter={24}>{renderItems()}</Row>
      </Form>
    );
  };
  return (
    <Modal title="主数据详情" onCancel={() => onCancel()} onOk={() => onOk()} open size="middle">
      <div className={styles.title}>基础分组</div>
      <div className={styles.groupContent}>{renderDynamincForm()}</div>
      {/* <div className={styles.title}>组合标签</div>
            <div>
                <TableAndCombine combineConfig={formVersion?.extConfig?.combineConfig} cTagsMap={cTagsMap} records={datasource} post={post} />
            </div> */}
      <div className={styles.title}>数据表格</div>
      <div>
        {tableList?.length > 0 ? (
          tableList.map((tabelItem, tableIndex) => (
            <div key={tableIndex}>
              <TableAndCombine tableConfig={tabelItem} cTagsMap={cTagsMap} mainDataId={mainDataId} post={post} formVersion={formVersion} />
            </div>
          ))
        ) : (
          <Empty description="暂无数据" />
        )}
      </div>
    </Modal>
  );
};

export default MainDataView;
