// 同组数据查询类型
export enum SAME_GROUP_QUERY_TYPE {
  ONLY_SUB = 'only_sub',
  ALL = 'all',
}

export const SAME_GROUP_QUERY_LABEL = {
  [SAME_GROUP_QUERY_TYPE.ONLY_SUB]: '仅子数据',
  [SAME_GROUP_QUERY_TYPE.ALL]: '全部数据',
};

export const SAME_GROUP_QUERY_OPTIONS = [
  { label: SAME_GROUP_QUERY_LABEL[SAME_GROUP_QUERY_TYPE.ONLY_SUB], value: SAME_GROUP_QUERY_TYPE.ONLY_SUB },
  { label: SAME_GROUP_QUERY_LABEL[SAME_GROUP_QUERY_TYPE.ALL], value: SAME_GROUP_QUERY_TYPE.ALL },
];

// 数据查询方式(类型)
export enum DATA_QUERY_TYPE {
  SINGLE = 'single',
  SAME = 'same',
}
export const DATA_QUERY_LABEL = {
  [DATA_QUERY_TYPE.SINGLE]: '基于单条数据',
  [DATA_QUERY_TYPE.SAME]: '基于同条数据',
};

export enum GROUP_LOGIC_TYPE {
  AND = 'AND',
  OR = 'OR',
}

export const GROUP_LOGIC_LABEL = {
  [GROUP_LOGIC_TYPE.AND]: '且',
  [GROUP_LOGIC_TYPE.OR]: '或',
};

export const GROUP_LOGIC_OPTIONS = [
  { label: GROUP_LOGIC_LABEL[GROUP_LOGIC_TYPE.AND], value: GROUP_LOGIC_TYPE.AND },
  { label: GROUP_LOGIC_LABEL[GROUP_LOGIC_TYPE.OR], value: GROUP_LOGIC_TYPE.OR },
];

// 数据单元关系类型
export const NEW_RELATION_TYPE = {
  EQUAL: {
    text: '等于',
    val: 'eq',
  },
  NOT_EQUAL: {
    text: '不等于',
    val: 'ne',
  },
  GREATER_THAN: {
    text: '大于',
    val: 'gt',
  },
  GREATER_THAN_OR_EQUAL: {
    text: '大于等于',
    val: 'gte',
  },
  LESS_THAN: {
    text: '小于',
    val: 'lt',
  },
  LESS_THAN_OR_EQUAL: {
    text: '小于等于',
    val: 'lte',
  },
  BETWEEN: {
    text: '在范围内',
    val: 'in_range',
  },
  NOT_BETWEEN: {
    text: '在范围外',
    val: 'not_in_range',
  },
  DYNAMIC_RANGE: {
    text: '动态范围',
    val: 'dynamic_range',
  },
  CONTAIN: {
    text: '包含',
    val: 'contain',
  },
  NOT_CONTAIN: {
    text: '排除',
    val: 'not_contain',
  },
  EMPTY: {
    text: '为空',
    val: 'isnull',
  },
  NOT_EMPTY: {
    text: '不为空',
    val: 'notnull',
  },
  // 数值
  // EQ_NUMBER: {
  //   text: '等于',
  //   val: 14,
  // },
  // // 数值不等于
  // NE_NUMBER: {
  //   text: '不等于',
  //   val: 15,
  // },
  // // 日期
  // EQ_DATE: {
  //   text: '等于',
  //   val: 16,
  // },
  // // 日期不等于
  // NE_DATE: {
  //   text: '不等于',
  //   val: 17,
  // },
  // 日期早于
  BEFORE_DATE: {
    text: '早于',
    val: 'lt',
  },
  // 日期-早于等于
  BEFORE_EQ_DATE: {
    text: '早于等于',
    val: 'lte',
  },
  // 日期-晚于
  AFTER_DATE: {
    text: '晚于',
    val: 'gt',
  },
  // 日期-晚于等于
  AFTER_EQ_DATE: {
    text: '晚于等于',
    val: 'gte',
  },
  // // 日期-范围内
  // IN_RANGE_DATE: {
  //   text: '在范围内',
  //   val: 22,
  // },
  // // 位置 等于
  // EQ_POSITION: {
  //   text: '等于',
  //   val: 23,
  // },
  // // 位置 不等于
  // NE_POSITION: {
  //   text: '不等于',
  //   val: 24,
  // },
  // NONE: {
  //   text: '暂无',
  //   val: 25,
  // },
  MIN: {
    text: '最小值',
    val: 'min',
  },
  MAX: {
    text: '最大值',
    val: 'max',
  },
  // 日期-最新一条
  NEWEST_DATE: {
    text: '最新一条',
    val: 'latest',
  },
  // POST_UNDO: {
  //   text: '当前岗位未处理',
  //   val: 28,
  // },
  // POST_DONE: {
  //   text: '当前岗位已处理',
  //   val: 29,
  // },
  // POST_PART_DONE: {
  //   text: '当前岗位部分处理',
  //   val: 30,
  // },
  // PART_EMPTY: {
  //   text: '部分处理',
  //   val: 31,
  // },
  // UNIT_EXCHANGE: {
  //   text: '单位转换',
  //   val: 32,
  // },
};

// 数据单元类型

export const DATA_TYPE = {
  TEXT: {
    text: '文本',
    val: 'TEXT',
    relation: [
      NEW_RELATION_TYPE.EQUAL,
      NEW_RELATION_TYPE.NOT_EQUAL,
      NEW_RELATION_TYPE.CONTAIN,
      NEW_RELATION_TYPE.NOT_CONTAIN,
      NEW_RELATION_TYPE.EMPTY,
      NEW_RELATION_TYPE.NOT_EMPTY,
      // RELATION_TYPE.START_LIKE,
      // RELATION_TYPE.START_NOT_LIKE,
      // RELATION_TYPE.END_LIKE,
      // RELATION_TYPE.END_NOT_LIKE,
    ],
  },
  TEXTENUM: {
    text: '文本枚举',
    val: 'TEXTENUM',
    relation: [
      NEW_RELATION_TYPE.EQUAL,
      NEW_RELATION_TYPE.NOT_EQUAL,
      NEW_RELATION_TYPE.EMPTY,
      NEW_RELATION_TYPE.NOT_EMPTY,
      NEW_RELATION_TYPE.CONTAIN,
      NEW_RELATION_TYPE.NOT_CONTAIN,
      // RELATION_TYPE.START_LIKE,
      // RELATION_TYPE.START_NOT_LIKE,
      // RELATION_TYPE.END_LIKE,
      // RELATION_TYPE.END_NOT_LIKE,
    ],
  },
  NUMBER: {
    text: '数字',
    val: 'NUMBER',
    relation: [
      NEW_RELATION_TYPE.EQUAL,
      NEW_RELATION_TYPE.NOT_EQUAL,
      NEW_RELATION_TYPE.GREATER_THAN,
      NEW_RELATION_TYPE.GREATER_THAN_OR_EQUAL,
      NEW_RELATION_TYPE.LESS_THAN,
      NEW_RELATION_TYPE.LESS_THAN_OR_EQUAL,
      NEW_RELATION_TYPE.BETWEEN,
      NEW_RELATION_TYPE.NOT_BETWEEN,
      NEW_RELATION_TYPE.EMPTY,
      NEW_RELATION_TYPE.NOT_EMPTY,
      // NEW_RELATION_TYPE.MAX,
      // NEW_RELATION_TYPE.MIN,
    ],
  },
  DATETIME: {
    text: '时间',
    val: 'DATETIME',
    relation: [
      NEW_RELATION_TYPE.AFTER_DATE,
      NEW_RELATION_TYPE.AFTER_EQ_DATE,
      NEW_RELATION_TYPE.BETWEEN,
      NEW_RELATION_TYPE.BEFORE_DATE,
      NEW_RELATION_TYPE.BEFORE_EQ_DATE,
      NEW_RELATION_TYPE.DYNAMIC_RANGE,
      NEW_RELATION_TYPE.EMPTY,
      NEW_RELATION_TYPE.NOT_EMPTY,
      NEW_RELATION_TYPE.NEWEST_DATE,
    ],
  },

  UNIT: {
    text: '单位',
    val: 'UNIT',
    relation: [
      NEW_RELATION_TYPE.EQUAL,
      NEW_RELATION_TYPE.NOT_EQUAL,
      NEW_RELATION_TYPE.EMPTY,
      NEW_RELATION_TYPE.NOT_EMPTY,
      NEW_RELATION_TYPE.GREATER_THAN,
      NEW_RELATION_TYPE.GREATER_THAN_OR_EQUAL,
      NEW_RELATION_TYPE.LESS_THAN,
      NEW_RELATION_TYPE.LESS_THAN_OR_EQUAL,
      NEW_RELATION_TYPE.BETWEEN,
      NEW_RELATION_TYPE.NOT_BETWEEN,
    ],
  },

  CODE: {
    text: '编码',
    val: 'CODE',
    // 允许的操作符
    relation: [
      NEW_RELATION_TYPE.EQUAL,
      NEW_RELATION_TYPE.NOT_EQUAL,
      NEW_RELATION_TYPE.EMPTY,
      NEW_RELATION_TYPE.NOT_EMPTY,
      NEW_RELATION_TYPE.CONTAIN,
      NEW_RELATION_TYPE.NOT_CONTAIN,
    ],
  },

  OBJECT: {
    text: '对象',
    val: 'OBJECT',
    relation: [
      NEW_RELATION_TYPE.EQUAL,
      NEW_RELATION_TYPE.NOT_EQUAL,
      // NEW_RELATION_TYPE.CONTAIN,
      // NEW_RELATION_TYPE.NOT_CONTAIN,
      NEW_RELATION_TYPE.EMPTY,
      NEW_RELATION_TYPE.NOT_EMPTY,
      // NEW_RELATION_TYPE.START_LIKE,
      // RELATION_TYPE.START_NOT_LIKE,
      // RELATION_TYPE.END_LIKE,
      // RELATION_TYPE.END_NOT_LIKE,
    ],
  },
  USER: {
    text: '用户',
    val: 'USER',
    relation: [
      NEW_RELATION_TYPE.EQUAL,
      NEW_RELATION_TYPE.NOT_EQUAL,
      NEW_RELATION_TYPE.CONTAIN,
      NEW_RELATION_TYPE.NOT_CONTAIN,
      NEW_RELATION_TYPE.EMPTY,
      NEW_RELATION_TYPE.NOT_EMPTY,
      // NEW_RELATION_TYPE.START_LIKE,
      // RELATION_TYPE.START_NOT_LIKE,
      // RELATION_TYPE.END_LIKE,
      // RELATION_TYPE.END_NOT_LIKE,
    ],
  },
  DEPT: {
    text: '部门',
    val: 'DEPT',
    relation: [
      NEW_RELATION_TYPE.EQUAL,
      NEW_RELATION_TYPE.NOT_EQUAL,
      NEW_RELATION_TYPE.CONTAIN,
      NEW_RELATION_TYPE.NOT_CONTAIN,
      NEW_RELATION_TYPE.EMPTY,
      NEW_RELATION_TYPE.NOT_EMPTY,
      // NEW_RELATION_TYPE.START_LIKE,
      // RELATION_TYPE.START_NOT_LIKE,
      // RELATION_TYPE.END_LIKE,
      // RELATION_TYPE.END_NOT_LIKE,
    ],
  },
  POST: {
    text: '岗位',
    val: 'POST',
    relation: [
      NEW_RELATION_TYPE.EQUAL,
      NEW_RELATION_TYPE.NOT_EQUAL,
      NEW_RELATION_TYPE.CONTAIN,
      NEW_RELATION_TYPE.NOT_CONTAIN,
      NEW_RELATION_TYPE.EMPTY,
    ],
  },
  ORG: {
    text: '公司',
    val: 'ORG',
    relation: [
      NEW_RELATION_TYPE.EQUAL,
      NEW_RELATION_TYPE.NOT_EQUAL,
      NEW_RELATION_TYPE.CONTAIN,
      NEW_RELATION_TYPE.NOT_CONTAIN,
      NEW_RELATION_TYPE.EMPTY,
    ],
  },
  FILE: {
    text: '文件',
    val: 'FILE',
  },
  ADDRESS: {
    text: '地址',
    val: 'ADDRESS',
    relation: [NEW_RELATION_TYPE.EMPTY, NEW_RELATION_TYPE.NOT_EMPTY, NEW_RELATION_TYPE.CONTAIN, NEW_RELATION_TYPE.NOT_CONTAIN],
  },
  PICTURE: {
    text: '图片',
    val: 'PICTURE',
  },
  SPECIAL: {
    text: '特殊',
    val: 'SPECIAL',
    relation: [NEW_RELATION_TYPE.EQUAL],
  },
};

export enum FILTER_VALUE_TYPE {
  // 固定值
  CONST = 'fixed_value',
  // 标签变量
  VARIABLE = 'variable',
  // 字段变量
  VARIABLE_FIELD = 'variable_field',
  // 用户变量
  VARIABLE_USER = 'variable_user',
  // 动态入参
  VARIABLE_DYNAMIC = 'variable_dynamic',
  // 关联列表
  RELATED_LIST = 'related_list',
  // 入参
  PARAMS = 'params',
  // 定向关联
  TARGETED_ASSOCIATION = 'targeted_association',
}
export const FILTER_VALUE_LIST = [
  {
    label: '固定值',
    value: FILTER_VALUE_TYPE.CONST,
  },
  {
    label: '标签变量',
    value: FILTER_VALUE_TYPE.VARIABLE,
  },
  {
    label: '动态入参',
    value: FILTER_VALUE_TYPE.VARIABLE_DYNAMIC,
  },
];

// 只有数值类型的标签才有【字段变量】选项
export const NUMBER_FILTER_VALUE_LIST = [
  ...FILTER_VALUE_LIST,
  {
    label: '字段变量',
    value: FILTER_VALUE_TYPE.VARIABLE_FIELD,
  },
];

/**
 * 动态范围类型
 */

export const DYNAMIC_SCOPE = {
  LAST_WEEK: { value: 'LAST_WEEK', text: '近一周', dependencies: [DATA_TYPE.DATETIME.val] },
  LAST_MONTH: { value: 'LAST_MONTH', text: '近一月', dependencies: [DATA_TYPE.DATETIME.val] },
  LAST_THREE_MONTH: { value: 'LAST_THREE_MONTH', text: '近三月', dependencies: [DATA_TYPE.DATETIME.val] },
  LAST_HALF_YEAR: { value: 'LAST_HALF_YEAR', text: '近半年', dependencies: [DATA_TYPE.DATETIME.val] },
  LAST_YEAR: { value: 'LAST_YEAR', text: '近一年', dependencies: [DATA_TYPE.DATETIME.val] },
  LAST_THREE_YEAR: { value: 'LAST_THREE_YEAR', text: '近三年', dependencies: [DATA_TYPE.DATETIME.val] },
  CURRENT_MONTH: { value: 'CURRENT_MONTH', text: '当月', dependencies: [DATA_TYPE.DATETIME.val] },
  MONTH_LAST: { value: 'MONTH_LAST', text: '上月', dependencies: [DATA_TYPE.DATETIME.val] },
  BEFORE_YESTERDAY: { value: 'BEFORE_YESTERDAY', text: '早于昨日', dependencies: [DATA_TYPE.DATETIME.val] },
  BEFORE_TODAY: { value: 'BEFORE_TODAY', text: '早于今日', dependencies: [DATA_TYPE.DATETIME.val] },
  BEFORE_MONTH: { value: 'BEFORE_CURRENT_MONTH', text: '早于当月', dependencies: [DATA_TYPE.DATETIME.val] },
  BEFORE_THIS_YEAR: { value: 'BEFORE_THIS_YEAR', text: '早于今年', dependencies: [DATA_TYPE.DATETIME.val] },
  TODAY: { value: 'TODAY', text: '今日', dependencies: [DATA_TYPE.DATETIME.val] },
  YESTERDAY: { value: 'YESTERDAY', text: '昨日', dependencies: [DATA_TYPE.DATETIME.val] },
  THIS_YEAR: { value: 'THIS_YEAR', text: '今年', dependencies: [DATA_TYPE.DATETIME.val] },
  BEFORE_YEAR: { value: 'BEFORE_YEAR', text: '去年', dependencies: [DATA_TYPE.DATETIME.val] },
  // 岗位
  BLOC_ALL_DISPLACE_POST: {
    value: 'BLOC_ALL_DISPLACE_POST',
    text: '当前集团的全部顶岗岗位',
    dependencies: [DATA_TYPE.POST.val],
  },
  PLATFORM_ALL_POST: {
    value: 'PLATFORM_ALL_POST',
    text: '当前平台全部岗位',
    dependencies: [DATA_TYPE.POST.val],
  },
  BLOC_ALL_POST: {
    value: 'BLOC_ALL_POST',
    text: '当前集团全部岗位',
    dependencies: [DATA_TYPE.POST.val],
  },
  ORG_ALL_POST: {
    value: 'ORG_ALL_POST',
    text: '当前公司全部岗位',
    dependencies: [DATA_TYPE.POST.val],
  },
  ORG_ALL_DISPLACE_POST: {
    value: 'ORG_ALL_DISPLACE_POST',
    text: '当前公司全部顶岗岗位',
    dependencies: [DATA_TYPE.POST.val],
  },
  CURRENT_AND_SUB_POST: {
    value: 'CURRENT_AND_SUB_POST',
    text: '当前岗位及下属岗位',
    dependencies: [DATA_TYPE.POST.val],
  },
  CURRENT_POST: {
    value: 'CURRENT_POST',
    text: '当前岗位',
    dependencies: [DATA_TYPE.POST.val],
  },
  PLATFORM_ALL_ORG: {
    value: 'PLATFORM_ALL_ORG',
    text: '当前平台所有公司',
    dependencies: [DATA_TYPE.ORG.val],
  },
  TOP_BLOC_ALL_ORG: {
    value: 'TOP_BLOC_ALL_ORG',
    text: '所有顶级集团下的全部企业',
    dependencies: [DATA_TYPE.ORG.val],
  },
  BLOC_ALL_ORG: {
    value: 'BLOC_ALL_ORG',
    text: '所有集团的全部企业',
    dependencies: [DATA_TYPE.ORG.val],
  },
  CURRENT_TOP_BLOC_ALL_ORG: {
    value: 'CURRENT_TOP_BLOC_ALL_ORG',
    text: '当前顶级集团下的全部企业',
    dependencies: [DATA_TYPE.ORG.val],
  },
  CURRENT_AND_SUB_BLOC_ALL_ORG: {
    value: 'PCURRENT_AND_SUB_BLOC_ALL_ORG',
    text: '当前集团下的所有企业',
    dependencies: [DATA_TYPE.ORG.val],
  },
  CURRENT_BLOC_ALL_ORG: {
    value: 'CURRENT_BLOC_ALL_ORG',
    text: '当前集团的所有企业',
    dependencies: [DATA_TYPE.ORG.val],
  },
  CURRENT_ORG: {
    value: 'CURRENT_ORG',
    text: '当前企业',
    dependencies: [DATA_TYPE.ORG.val],
  },
  //   PLATFORM_ALL_ORG
  //  TOP_BLOC_ALL_ORG
  //  BLOC_ALL_ORG
  //  CURRENT_TOP_BLOC_ALL_ORG
  //  CURRENT_AND_SUB_BLOC_ALL_ORG
  //  CURRENT_BLOC_ALL_ORG
  //  CURRENT_ORG
  PLATFORM_ALL_DEPT: {
    value: 'PLATFORM_ALL_DEPT',
    text: '平台的全部部门',
    dependencies: [DATA_TYPE.DEPT.val],
  },
  CURRENT_BLOC_ALL_DEPT: {
    value: 'CURRENT_BLOC_ALL_DEPT',
    text: '当前集团的全部部门',
    dependencies: [DATA_TYPE.DEPT.val],
  },
  CURRENT_ORG_ALL_DEPT: {
    value: 'CURRENT_ORG_ALL_DEPT',
    text: '当前公司的全部部门',
    dependencies: [DATA_TYPE.DEPT.val],
  },
  CURRENT_AND_SUB_DEPT: {
    value: 'CURRENT_AND_SUB_DEPT',
    text: '当前部门及下属部门',
    dependencies: [DATA_TYPE.DEPT.val],
  },
  CURRENT_DEPT: {
    value: 'CURRENT_DEPT',
    text: '当前部门',
    dependencies: [DATA_TYPE.DEPT.val],
  },
  PLATFORM_ALL_USER: {
    value: 'PLATFORM_ALL_USER',
    text: '平台全部用户',
    dependencies: [DATA_TYPE.USER.val],
  },
  CURRENT_BLOC_ALL_USER: {
    value: 'CURRENT_BLOC_ALL_USER',
    text: '当前集团全部用户',
    dependencies: [DATA_TYPE.USER.val],
  },
  CURRENT_ORG_ALL_USER: {
    value: 'CURRENT_ORG_ALL_USER',
    text: '当前公司全部用户',
    dependencies: [DATA_TYPE.USER.val],
  },
  CURRENT_AND_SUB_USER: {
    value: 'CURRENT_AND_SUB_USER',
    text: '当前用户及下属成员',
    dependencies: [DATA_TYPE.USER.val],
  },
  CURRENT_USER: {
    value: 'CURRENT_USER',
    text: '当前用户',
    dependencies: [DATA_TYPE.USER.val],
  },
};
