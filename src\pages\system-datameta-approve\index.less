.layoutContent {
  padding: 0 !important;
}

.contentWrapper {
  width: 100%;
  height: 100%;
  display: flex;

  .leftContent {
    width: 225px;
    height: 100%;
    overflow-y: auto;
    flex-shrink: 0;
    border-right: 1px solid #dce2eb;

    .leftTop {
      height: 83px;
      padding: 14px 12px 0;

      .tabTitle {
        padding: 10px 0;
        color: #2a2f37;
        font-weight: bold;
      }
    }

    .formList {
      height: calc(100% - 83px);
      overflow-y: auto;

      .formStyle {
        padding: 9px;
        border-left: 6px solid transparent;

        &.active {
          background: #fff;
          border-color: #4c7bf6;
        }

        .formName {
          flex-grow: 1;
          font-weight: bold;
          font-size: 14px;
          color: #1d2129;
          overflow: hidden;
        }

        .formDesc {
          font-size: 12px;
          color: #4e5969;
        }
      }
    }
  }

  .rightForm {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    padding: 20px 10px 0 0;
    overflow: hidden;

    .formContent {
      flex-grow: 1;
      overflow-y: auto;
      padding-left: 20px;

      .formTitle {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .titleLabel {
          color: #000;
          font-weight: bold;
          margin-right: 5px;
        }
      }

      .dynamincFormStyle {
        padding-top: 20px;

        .tagValue {
          font-size: 14px;
          font-weight: bold;
          color: '#000';
        }
      }

      .tagLabel {
        color: #838d9b;
      }
    }

    .rightBottom {
      padding: 6px 0;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      border-top: 1px solid #dce2eb;
      column-gap: 10px;
    }
  }
}

.labelWrapper {
  flex-grow: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;

  .labelName {
    color: #4e5969;
  }
}
