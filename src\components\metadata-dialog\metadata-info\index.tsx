import { MetaDataVO } from '@/types/metadata';
import { Descriptions } from '@gwy/components-web';
import styles from './index.less';

type Props = {
  metaData?: MetaDataVO;
};

const MetaDataInfo = ({ metaData }: Props) => {
  return (
    <div className={styles.container}>
      <Descriptions
        column={2}
        items={[
          {
            key: '1',
            label: '数据类型',
            children: <p>{metaData.name || '-'}</p>,
          },
          {
            key: '2',
            label: '版本号',
            children: <p>{metaData.version || '-'}</p>,
          },
          {
            key: '3',
            label: '创建时间',
            children: <p>{metaData.createTime || '-'}</p>,
          },
          {
            key: '4',
            label: '修改时间',
            children: <p>{metaData.updateTime || '-'}</p>,
          },
          {
            key: '5',
            span: 2,
            label: '数元描述',
            children: <p>{metaData.description || '-'}</p>,
          },
          {
            key: '6',
            span: 2,
            label: '使用场景',
            children: <p>{metaData.useScene || '-'}</p>,
          },
        ]}
      />
    </div>
  );
};

export default MetaDataInfo;
