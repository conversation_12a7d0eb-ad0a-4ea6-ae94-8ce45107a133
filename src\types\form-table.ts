import { FormField } from './form-field';
import { StatisticalItem } from './statistic-item';

/**
 * com.ideal.gwy.datasource.model.request.form.FormUpdateRequest.FormTableConfig
 *
 * FormTableConfig
 */
export type FormTableConfig = {
  /**
   * 表单数据单元列表
   */
  dataUnits?: FormDataUnitRequest[] | null;
  /**
   * 表单表格字段列表
   */
  fields?: FormField[] | null;
  /**
   * 表格id
   */
  id?: null | string;
  /**
   * 统计项
   */
  statisticList?: StatisticalItem[];
  /**
   * 表格名称
   */
  tableName?: null | string;
  [property: string]: any;
};

/**
 * com.ideal.gwy.datasource.model.request.form.FormDataUnitRequest
 *
 * FormDataUnitRequest
 */
export type FormDataUnitRequest = {
  /**
   * 条件组
   */
  conditionGroups?: any[] | null;
  /**
   * 数据单元id
   */
  dataUnitId?: number | null;
  /**
   * 数据单元查询额外配置
   */
  extConfig?: any;
  /**
   * 是否主数据单元
   */
  mainDataUnit?: boolean | null;
  /**
   * 数据单元名称
   */
  name?: null | string;
  [property: string]: any;
};
