import { FileFormatsOptions, FileSizeUnit, FileSizeUnitList } from '@/const/metadata';
import { Form, FormInstance, InputNumber, Radio, Select, Space, TreeSelect } from '@gwy/components-web';
import { useContext } from 'react';
import { MetaDataContext } from '../../context';

export const FileDTOKey = 'metaDataFileDTO';

type Props = {
  form?: FormInstance;
};
const formatsOptions = FileFormatsOptions.map((opt) => ({
  title: opt.label,
  value: opt.value,
  children: opt.formats.map((format) => ({
    title: format,
    value: format,
  })),
}));

const FileConfig = ({ form }: Props) => {
  const { disabled, hasApproved } = useContext(MetaDataContext);

  const ifLimitFormat = Form.useWatch([FileDTOKey, 'ifLimitFormat'], form);

  return (
    <div>
      <Form.Item label="文件格式" name={[FileDTOKey, 'ifLimitFormat']} rules={[{ required: true, message: '请选择' }]} initialValue={false}>
        <Radio.Group
          disabled={hasApproved || disabled}
          options={[
            { label: '不限制', value: false },
            { label: '限制', value: true },
          ]}
        />
      </Form.Item>
      {ifLimitFormat && (
        <Form.Item
          label="格式类型"
          name={[FileDTOKey, 'formats']}
          rules={[{ required: true, message: '请选择' }]}
          initialValue={FileFormatsOptions.map((opt) => opt.formats).flat()}
        >
          <TreeSelect treeData={formatsOptions} virtual={false} treeCheckable />
        </Form.Item>
      )}

      <Form.Item label="单文件容量大小" required style={{ marginBottom: 0 }}>
        <Space align="start">
          <Form.Item initialValue={200} style={{ width: '100px' }} name={[FileDTOKey, 'sizeMax']} rules={[{ required: true, message: '请输入' }]}>
            <InputNumber placeholder="请填写" min={0} step={1} max={1000} />
          </Form.Item>
          <Form.Item name={[FileDTOKey, 'sizeUnit']} rules={[{ required: true, message: '请选择' }]} initialValue={FileSizeUnit.MB}>
            <Select placeholder="请选择" options={FileSizeUnitList}></Select>
          </Form.Item>
        </Space>
      </Form.Item>
      <Form.Item label="文件数量" name={[FileDTOKey, 'fileNum']} rules={[{ required: true, message: '请输入' }]} initialValue={1}>
        <InputNumber min={1} step={1} max={10} precision={0} />
      </Form.Item>
    </div>
  );
};

export default FileConfig;
