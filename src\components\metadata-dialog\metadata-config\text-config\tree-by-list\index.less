.tree {
  background: none;
}

.treeNode {
  width: 100%;
  margin-top: 8px;
  padding-bottom: 0 !important;

  &.topTreeNode {
    margin-top: 0;
  }

  :global {
    .ant-tree-node-content-wrapper {
      flex-grow: 1;
      padding: 0 !important;
      margin: 0 !important;

      &:hover {
        background-color: transparent;
      }
    }

    .ant-tree-switcher {
      align-self: center;
      margin-right: 0 !important;
    }
  }
}

.treeNodeTitle {
  display: flex;
  align-items: center;
  flex-grow: 1;
  overflow: hidden;
  // justify-content: space-between;
  // flex: 1;
}

.treeNodeTitleContent {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
}

.treeNodeTitleName {
  display: flex;
  align-items: center;
  flex-grow: 1;
  overflow: hidden;
}

.treeNodeTitleNameDesc {
  border-color: transparent #d9d9d9 #d9d9d9 !important;
  box-shadow: none !important;
}

.treeNodeOperate {
  display: flex;
  width: 98px;
  padding-left: 12px;
  align-items: center;
  flex-shrink: 0;
  column-gap: 10px;
  height: 32px;

  span {
    cursor: pointer;

    &:hover {
      color: #4d7bf6 !important;
    }
  }
}
