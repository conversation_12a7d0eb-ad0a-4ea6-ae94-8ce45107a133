import { SortableList } from '@/components/SortableList';
import { SwapOutlined } from '@ant-design/icons';
import { Button, Popover } from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.less';

interface Iprops {
  onOk?: (value?: any) => void;
  list: any[];
  idKeyName: string;
  namerKeyName: string;
}
const SortList = (props: Iprops) => {
  const { idKeyName, namerKeyName, onOk, list } = props;
  const [_items, _setItems] = useState<any[]>([]);
  const [visible, setVisible] = useState(false);
  console.log(list, 'list----------');
  const init = () => {
    _setItems(list?.map((item) => ({ ...item, id: item[idKeyName] })));
  };

  useEffect(() => {
    init();
  }, [list]);

  const renderContent = () => {
    return (
      <div className={styles.container}>
        <div className={styles.list}>
          <SortableList
            items={_items}
            onChange={(items) => _setItems(items)}
            renderItem={(item) => {
              return (
                <SortableList.Item id={item.id}>
                  <div style={{ marginBottom: '12px' }}> {item[namerKeyName]}</div>
                  <SortableList.DragHandle />
                </SortableList.Item>
              );
            }}
          />
        </div>
        <div className={styles.bottom}>
          <Button
            className={styles.btn}
            onClick={(e) => {
              e.stopPropagation();
              init();
            }}
          >
            重置
          </Button>
          <Button
            className={styles.btn}
            type="primary"
            onClick={(e) => {
              e.stopPropagation();
              setVisible(false);
              onOk?.(_items);
            }}
          >
            确定
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Popover
      open={visible}
      onOpenChange={(open) => {
        setVisible(open);
      }}
      content={renderContent()}
      placement="bottom"
      trigger={'click'}
      styles={{
        root: {
          width: 180,
        },
        body: {
          padding: 0,
        },
      }}
    >
      <Button
        type="link"
        icon={<SwapOutlined />}
        onClick={(e) => {
          e.stopPropagation();
          setVisible(true);
        }}
      >
        表单排序
      </Button>
    </Popover>
  );
};

export default SortList;
