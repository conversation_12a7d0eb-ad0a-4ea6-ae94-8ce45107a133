import { AppType } from '@/pages/app-designer/const';
import { FormButtonConfig } from '@/services/form-button';
import { CalcFieldVO } from './calc-field';
import { FormField } from './form-field';
import { FormTableConfig } from './form-table';

/**
 * 表单数据操作类型
 */
export enum FormDataOperateType {
  Agree = 'AGREE',
  End = 'END',
  Launch = 'LAUNCH',
  Refuse = 'REFUSE',
  Return = 'RETURN',
}

export enum AppMode {
  View = 'VIEW',
  Edit = 'EDIT',
}

export type FormVO = {
  /**
   * 应用类型（发起、审核）
   */
  appType?: AppType;
  /**
   * 表单数据单元列表
   */
  dataUnits?: any[] | null;
  /**
   * 关闭窗口是否允许删除暂存
   */
  deleteStash?: boolean | null;
  /**
   * 描述
   */
  description?: null | string;
  /**
   * 拓展配置，如组合标签配置、数据表格配置
   */
  extConfig?: FormExtConfig;
  /**
   * 左侧字段列表
   */
  fieldVOS?: CalcFieldVO[] | null;
  /**
   * 是否聚合
   */
  groupFlag?: boolean | null;
  /**
   * 表单名称
   */
  name?: null | string;
  /**
   * 表单提醒配置
   */
  notifyConfig?: any;
  /**
   * 操作岗位id
   */
  postId?: null | string;
  /**
   * 存储的数据单元ids
   */
  storeDataUnitIds?: number[] | null;
  /**
   * word文件url
   */
  wordFileUrl?: null | string;
  /**
   * word是否只读
   */
  wordReadonly?: boolean | null;
  [property: string]: any;
};

/**
 * 拓展配置，如组合标签配置、数据表格配置
 *
 * FormExtConfig
 */
export type FormExtConfig = {
  /**
   * 基础分组名称
   */
  baseGroupName?: null | string;
  /**
   * 按钮配置
   */
  buttonConfigs?: FormButtonConfig[] | null;
  /**
   * 组合标签配置
   */
  combineTagConfig?: FormCombineTagConfig;
  /**
   * 基础分组字段
   */
  fields?: FormField[] | null;
  /**
   * 表格字段
   */
  tableConfigs?: FormTableConfig[] | null;
  [property: string]: any;
};

/**
 * 组合标签配置
 *
 * FormCombineTagConfig
 */
export type FormCombineTagConfig = {
  /**
   * 组合标签名称
   */
  combineTagName?: null | string;
  /**
   * 表单组合标签列表
   */
  fields?: FormField[] | null;
  [property: string]: any;
};

/**
 * FormAppVO
 */
export type FormAppVO = {
  /**
   * 应用类型（发起、审核）
   */
  appType?: AppType;
  /**
   * 表单id,生效后有值
   */
  formId?: number | null;
  /**
   * 表单版本id
   */
  formVersionId?: number | null;
  /**
   * 数据单元名称
   */
  name?: null | string;
  [property: string]: any;
};
