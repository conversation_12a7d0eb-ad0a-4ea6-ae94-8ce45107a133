.spin {
  height: 100%;

  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.layoutContent {
  background-color: #fff;
  padding: 0 !important;

  :global {
    .ant-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: auto;

      .ant-tabs-nav {
        margin-bottom: 0;
      }

      .ant-tabs-content-holder {
        flex-grow: 1;
        overflow: auto;

        .ant-tabs-content {
          height: 100%;

          .ant-tabs-tabpane {
            height: 100%;
            overflow: auto;
          }
        }
      }
    }
  }
}

.content {
  display: flex;
  height: 100%;
  overflow: hidden;

  .left {
    flex-shrink: 0;
    width: 300px;
    overflow: auto;
    box-shadow: 2px 0 4px 0 rgba(0, 0, 0, 10%);
    z-index: 1;
    // padding: 0 6px;
    &.wordDesign {
      width: 528px;
    }
  }

  .right {
    flex-grow: 1;
    overflow: hidden;
  }
}

.footerTips {
  color: #86909c;
}

.footerBtns {
  display: flex;
  gap: 12px;
  align-items: center;
}
