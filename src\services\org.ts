import { dataWrapper, requestUtil } from '@gwy/libs-web';

// 获取自己所有岗位
export const getAllPosts = (): Promise<any> => dataWrapper(requestUtil.get('ideal-new-org/api/common/user/myPostListInBloc'));

// 获取自己所有岗位-移除执行岗
export const myPostListInBlocTreeExcludeExecute = (): Promise<any> =>
  dataWrapper(requestUtil.get('ideal-new-org/api/common/user/myPostListInBlocTreeExcludeExecute'));

/**
 * 获取公司岗位树
 */
export const getOrgPostTree = () => dataWrapper(requestUtil.get('gwy-org/api/org/posts'));
