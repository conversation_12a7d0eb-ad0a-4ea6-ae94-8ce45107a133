import { APPROVE_ENUM, APPROVE_TYPE } from '@/const/datasource';
import { MetaUnitType } from '@/const/metadata';
import { useGlobalState, useRoute } from '@/hooks';
import { datasourceAPI } from '@/services';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Input, LayoutPage, message, Table } from '@gwy/components-web';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { openType } from '../metadata-manage/components/metadata-list';
import CreateDataSource from './components/create-modal';
import MetaDataEdit from './components/edit-modal';
import styles from './index.less';

const MetadataTypeManage = () => {
  const { state: routeState } = useRoute<{
    canEdit?: boolean;
  }>();
  const { userInfo } = useGlobalState() || {};
  const [loading, setLoading] = useState(false);
  const [state, setState] = useState<{
    open: boolean;
    type: openType;
    search: string;
    list: any[];
    editData: {
      currentVersionId?: number;
      tempVersionId?: number;
      unitData?: any;
    };
  }>({
    search: '',
    open: false,
    editData: null,
    list: [],
    type: openType.viewDataSource,
  });

  const getData = useCallback(async () => {
    setLoading(true);
    try {
      const data = await datasourceAPI.getDatasourceList({
        include_draft: true,
        include_approving: true,
        type: MetaUnitType.Normal,
        subType: MetaUnitType.Normal,
      });
      setState((pre) => ({ ...pre, list: data }));
    } catch (e) {}
    setLoading(false);
  }, []);

  const columns = useMemo(
    () => [
      { title: '序号', dataIndex: 'index', width: 50, render: (value, record, index) => index + 1 },
      { title: '类型名称', dataIndex: 'name', width: 200, ellipsis: true },
      { title: '类型描述', dataIndex: 'description', ellipsis: true },
      { title: '数据单元数量', dataIndex: 'dataUnitNum', width: 120, render: (value) => value || '0' },
      {
        title: '审批状态',
        dataIndex: 'status',
        width: 100,
        render: (val) => APPROVE_TYPE[val]?.label || '生效中',
      },
      { title: '更新人', dataIndex: 'updateUser', width: 100 },
      { title: '更新时间', dataIndex: 'updateTime', width: 180 },
      {
        title: '操作',
        dataIndex: 'operate',
        width: 80,
        render: (value, record) => (
          <Button
            type="link"
            onClick={() => {
              const tempVersions = (record?.versions || []).filter((version) => version.status === APPROVE_ENUM.STASH);
              const tempVersionId = tempVersions?.length ? tempVersions[tempVersions?.length - 1]?.dataUnitVersionId : null;
              const submitIsMySelf = record.createUserId === userInfo?.id;
              const isApproved = record.status === APPROVE_ENUM.ING;

              const checkType = !record.dataUnitId ? openType.addDatasource : openType.editDatasource;
              if (isApproved && !submitIsMySelf) {
                return message.warning('他人提交审核版本，您暂无查看权限');
              }
              setState((pre) => ({
                ...pre,
                open: true,
                // 弹窗内部有开启编辑按钮
                type: checkType,
                editData: {
                  tempVersionId,
                  unitData: record,
                  currentVersionId: record.currentVersionId,
                },
              }));
            }}
          >
            查看
          </Button>
        ),
      },
    ],
    [userInfo?.id],
  );

  useEffect(() => {
    getData();
  }, [getData]);

  const tableData = useMemo(() => {
    return (state.list || []).filter((item) => (item.name || '').includes(state.search));
  }, [state.list, state.search]);

  return (
    <LayoutPage loading={loading} header={false} footer={false} contentClassName={styles.layoutContent}>
      <div className={styles.container}>
        <div className={styles.header}>
          <Input.Search
            placeholder="搜索类型名称"
            onSearch={(val) => {
              setState((pre) => ({ ...pre, search: val, current: 1 }));
            }}
            style={{ width: 300 }}
            allowClear={false}
          />
          <Button
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => setState((pre) => ({ ...pre, open: true, type: openType.addDatasource, editData: null }))}
          >
            新增类型
          </Button>
        </div>
        <Table dataSource={tableData} columns={columns} className={styles.table} size="small" pagination={false} bordered />
      </div>
      {state.open && state.type === openType.addDatasource && (
        <CreateDataSource
          onCancel={() => setState((prev) => ({ ...prev, open: false }))}
          onOk={() => setState((prev) => ({ ...prev, open: false }))}
          refresh={getData}
          dataSourceType={MetaUnitType.Normal}
          currentVersionId={state.editData?.currentVersionId}
          addModalType={state.type}
        />
      )}
      {state.open && [openType.editDatasource, openType.viewDataSource].includes(state.type) && (
        <MetaDataEdit
          operateType={state.type}
          onCancel={() => setState((prev) => ({ ...prev, open: false }))}
          onOk={() => setState((prev) => ({ ...prev, open: false }))}
          refresh={getData}
          dataSourceType={MetaUnitType.Normal}
          editPermission={routeState.canEdit}
          currentVersionId={state.editData?.currentVersionId}
          tempVersionId={state.editData?.tempVersionId}
        />
      )}
    </LayoutPage>
  );
};

export default MetadataTypeManage;
