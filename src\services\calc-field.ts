import { dataWrapper, requestUtil } from '@gwy/libs-web';

/**
 * 创建字段
 */
export const createField = (params) => dataWrapper(requestUtil.post(`gwy-datasource/api/field/create_field`, params));

/**
 * 通用查询字段信息
 */
export const queryFieldList = (params) => dataWrapper(requestUtil.post(`gwy-datasource/api/field/query_field_list`, params));

/**
 * 分页查询字段列表
 */
export const getFieldListTab = (
  post_id,
  params: {
    fieldName: string;
    page: number;
    pageSize: number;
    /**
     * 类型：1-我创建的 2-被授权的
     */
    type: number;
    [property: string]: any;
  },
) => dataWrapper(requestUtil.get(`gwy-datasource/api/field/${post_id}/get_field_list`, { params }));

/**
 * 删除字段(字段库)
 */
export const deleteCalcField = (field_id) => dataWrapper(requestUtil.delete(`gwy-datasource/api/field/${field_id}/delete_field`));

/**
 * 选取字段
 */
export const selectField = (params) => dataWrapper(requestUtil.post(`gwy-datasource/api/field/select_field`, params));

/**
 * 获取授权岗位列表
 */
export const getFieldAuthPostList = (post_id) => dataWrapper(requestUtil.get(`gwy-datasource/api/field/${post_id}/get_field_auth_post_list`));

/**
 * 岗位授权字段列表
 */
export const getFieldAuthList = (operate_post_id, auth_post_id) =>
  dataWrapper(requestUtil.get(`gwy-datasource/api/field/${operate_post_id}/${auth_post_id}/get_field_auth_list`));

/**
 * 字段授权
 */
export const fieldAuth = (params) => dataWrapper(requestUtil.post(`gwy-datasource/api/field/field_auth`, params));
