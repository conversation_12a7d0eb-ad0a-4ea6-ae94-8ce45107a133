import {
  AddressType,
  AmountUnit,
  AreaUnit,
  CodeDateType,
  CodeNumberType,
  CodeType,
  CommonCode,
  CustomCode,
  DeptRangeType,
  EnumType,
  FileFormats,
  FileSizeUnit,
  LengthUnit,
  MetaDataGroup,
  MetaDataType,
  MetaUnitGeneralType,
  NumberRangeType,
  OrgRangeType,
  PostRangeType,
  RangeType,
  TextCharType,
  TextInputType,
  TimeRangeType,
  UnitType,
  UserRangeType,
  VolumeUnit,
  WeightUnit,
} from '@/const/metadata';

/**
 * MetaDataTextEnumDTO
 */
export type MetaDataTextEnumDTO = {
  /**
   * 描述
   */
  description?: string;
  /**
   * 枚举项id
   */
  id?: string;
  /**
   * 枚举项
   */
  item?: string;
  /**
   * 子枚举项列表
   */
  subEnumList?: MetaDataTextEnumDTO[];
  [property: string]: any;
};

/**
 * 文本通用配置
 *
 * MetaDataTextDTO
 */
export interface MetaDataTextDTO {
  /**
   * 允许包含的字符类型（输入类型为“手动输入”）
   */
  allowedCharacters?: TextCharType[];
  /**
   * 枚举项列表（输入类型为“枚举选项”）
   */
  enumList?: MetaDataTextEnumDTO[];
  /**
   * 输入类型
   */
  inputType?: TextInputType;
  /**
   * 字数限制（输入类型为“手动输入”）
   */
  maxLength?: number;
  /**
   * 引用的枚举库编码
   */
  dictCode?: string;
  /**
   * 枚举类型（自定义、枚举库）
   */
  enumType?: EnumType;
}

export type MetaDataNumberDTO = {
  /**
   * 数值范围类型
   */
  rangeType?: NumberRangeType;
  /**
   * 最小值
   */
  min?: number;
  /**
   * 最大值
   */
  max?: number;

  [property: string]: any;
};

/**
 * 时间通用配置
 *
 * MetaDataDateTimeDTO
 */
export type MetaDataDateTimeDTO = {
  /**
   * 结束时间
   */
  end?: string;
  /**
   * 范围类型
   */
  rangeType?: TimeRangeType;
  /**
   * 开始时间
   */
  start?: string;
  [property: string]: any;
};

export enum MetaDataObjectDTOType {
  GENERAL = 'GENERAL', // 通用
  NORMAL = 'NORMAL', // 普通
}

/**
 * 对象通用配置
 *
 * MetaDataObjectDTO
 */
export type MetaDataObjectDTO = {
  /**
   * 配置项
   */
  config?: PostRangeType | UserRangeType | OrgRangeType | DeptRangeType;
  /**
   * 数据单元id
   */
  dataUnitId?: number;
  // 通用数据单元类型
  generalType?: MetaUnitGeneralType;
  /**
   * 数据单元类型
   */
  type?: MetaDataObjectDTOType;
  currentVersionId?: number;
  [property: string]: any;
};

/**
 * 单位通用配置
 *
 * MetaDataUnitDTO
 */
export type MetaDataUnitDTO = {
  /**
   * 配置项
   */
  configList?: UnitTypes[];
  /**
   * 类型
   */
  type?: UnitType;
  /**
   * 范围单位
   */
  unit?: UnitTypes;
  [property: string]: any;
} & MetaDataNumberDTO;
/**
 * MetaDataCodeElementDTO
 */
export type MetaDataCodeElementDTO = {
  /**
   * 日期元素-选择形式
   */
  dateForm?: CodeDateType;
  /**
   * 元素id
   */
  id?: string;
  /**
   * 位数限制
   */
  maxLength?: number;
  /**
   * 数字元素-选择形式
   */
  numberForm?: CodeNumberType;
  /**
   * 文本值
   */
  text?: string;
  /**
   * 类型
   */
  type?: CustomCode;
  [property: string]: any;
};

/**
 * MetaDataCodeConfigDTO
 */
export type MetaDataCodeConfigDTO = {
  /**
   * 元素列表
   */
  elementList?: MetaDataCodeElementDTO[];
  /**
   * 配置id
   */
  id?: string;
  [property: string]: any;
};

/**
 * 编码通用配置
 *
 * MetaDataCodeDTO
 */
export type MetaDataCodeDTO = {
  /**
   * 配置（组）列表
   */
  configList?: MetaDataCodeConfigDTO[];
  /**
   * 包含元素类型
   */
  elementTypeList?: CustomCode[] | CommonCode[];
  /**
   * 类型
   */
  type?: CodeType;
  [property: string]: any;
};

/**
 * 文件类型配置
 */
export type MetaDataFileDTO = {
  ifLimitFormat?: boolean;
  formats?: FileFormats[];
  sizeMax: number;
  sizeUnit: FileSizeUnit;
  fileNum: number;
};

/**
 * 地址类型配置
 */
export type MetaDataAddressDTO = {
  type: AddressType;
  rangeType: RangeType;
  codeList: string[];
};

/**
 * 标签数元配置
 *
 * TagMetaDataConfigRequest
 */
export type MetaDataConfigVO = {
  /**
   * 数元id
   */
  metaDataId?: number | null;
  /**
   * 类型
   */
  type?: MetaDataType;

  metaDataTextDTO?: MetaDataTextDTO;
  metaDataNumberDTO?: MetaDataNumberDTO;
  metaDataDateTimeDTO?: MetaDataDateTimeDTO;
  metaDataObjectDTO?: MetaDataObjectDTO;
  metaDataUnitDTO?: MetaDataUnitDTO;
  metaDataCodeDTO?: MetaDataCodeDTO;
  metaDataFileDTO?: MetaDataFileDTO;
  metaDataAddressDTO?: MetaDataAddressDTO;
  [property: string]: any;
};

/**
 * MetaDataVO
 */
export type MetaDataVO = {
  /**
   * 对应图标
   */
  avatar?: string;
  /**
   * 对应编码
   */
  code?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 数元数据类型
   */
  dataType?: MetaDataType;
  /**
   * 描述
   */
  description?: string;
  /**
   * 数元id
   */
  metaDataId?: number;
  /**
   * 数元名称
   */
  name?: string;
  /**
   * 数元类型
   */
  type?: MetaDataGroup;
  /**
   * 修改时间
   */
  updateTime?: string;
  /**
   * 使用场景
   */
  useScene?: string;
  /**
   * 版本号
   */
  version?: string;
  [property: string]: any;
};

export type UnitTypes = WeightUnit | LengthUnit | AreaUnit | VolumeUnit | AmountUnit;
