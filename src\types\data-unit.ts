import { MetaUnitGeneralType, MetaUnitStatus, MetaUnitType } from '@/const/metadata';

/**
 * DataUnitBaseVO
 */
export type DataUnitBaseVO = {
  /**
   * 创建时间
   */
  createTime?: null | string;
  /**
   * 创建人
   */
  createUser?: null | string;
  /**
   * 创建人用户Id
   */
  createUserId?: null | string;
  /**
   * 当前版本
   */
  currentVersion?: null | string;
  /**
   * 当前版本id
   */
  currentVersionId?: number | null;
  /**
   * 数据单元id，版本生效后才有该值
   */
  dataUnitId?: number | null;
  /**
   * 是否启用
   */
  enable?: boolean | null;
  /**
   * 通用数据单元类型，如公司、部门、岗位、用户
   */
  generalType?: MetaUnitGeneralType;
  /**
   * 唯一标签id
   */
  identifierTagId?: number | null;
  /**
   * 唯一标签名称
   */
  identifierTagName?: null | string;
  /**
   * 数据单元名称
   */
  name?: null | string;
  /**
   * 数据单元状态
   */
  status?: MetaUnitStatus;
  /**
   * 标签数量
   */
  tagNum?: number | null;
  /**
   * 数据单元类型
   */
  type?: MetaUnitType;
  /**
   * 更新时间
   */
  updateTime?: null | string;
  /**
   * 更新人
   */
  updateUser?: null | string;
  /**
   * 更新人用id
   */
  updateUserId?: null | string;
  /**
   * 数据单元版本列表
   */
  versions?: DataUnitVersionBaseVO[] | null;

  conditionGroups?: any[];
  [property: string]: any;
};

/**
 * DataUnitVersionBaseVO
 */
export type DataUnitVersionBaseVO = {
  /**
   * 创建时间
   */
  createTime?: null | string;
  /**
   * 数据单元id，版本生效后才有该值
   */
  dataUnitId?: number | null;
  /**
   * 数据单元版本id
   */
  dataUnitVersionId?: number | null;
  /**
   * 是否启用
   */
  enable?: boolean | null;
  /**
   * 前一次数据单元版本ID （为空代表初始新建）
   */
  preVersionId?: number | null;
  /**
   * 数据单元版本状态
   */
  status?: MetaUnitStatus;
  /**
   * 更新时间
   */
  updateTime?: null | string;
  version?: null | string;
  [property: string]: any;
};
