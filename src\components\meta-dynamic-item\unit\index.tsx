import { amountUnitOptions, areaUnitOptions, lengthUnitOptions, UnitType, volumeUnitOptions, weightUnitOptions } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { InputNumber, Select } from '@gwy/components-web';
import { memo, useMemo } from 'react';
import { UseIn } from '../const';

export type UnitMetaProps = {
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  value?: any;
  onChange?: (value) => void;
  styles?: React.CSSProperties;
  isPreview?: boolean;
};

const UnitMeta = memo<UnitMetaProps>(({ useIn, tag, field, value, onChange, styles, isPreview }) => {
  const { raw_value, raw_unit } = value || {};
  const { config, readonly, placeholder } = field || {};
  const { configList } = config?.metaDataUnitDTO || {};
  const onValueVhange = (data: { raw_value?; raw_unit? }) => {
    typeof onChange === 'function' &&
      onChange({
        raw_value,
        raw_unit,
        ...data,
      });
  };

  const options = useMemo(() => {
    let list = [];
    const type = tag?.tagMetaDataConfig?.metaDataUnitDTO?.type;
    if (type === UnitType.weight) {
      list = weightUnitOptions;
    }
    if (type === UnitType.length) {
      list = lengthUnitOptions;
    }
    if (type === UnitType.area) {
      list = areaUnitOptions;
    }
    if (type === UnitType.volume) {
      list = volumeUnitOptions;
    }
    if (type === UnitType.amount) {
      list = amountUnitOptions;
    }
    return Array.isArray(configList) ? list.filter((item) => configList.includes(item.value)) : list;
  }, [configList, tag]);

  if (readonly || isPreview) {
    if (useIn === UseIn.Designer) {
      return '回显数据';
    }
    const { value: tagValue, data: tagData } = value || {};
    return tagValue ?? '-';
  }

  return (
    <InputNumber
      placeholder={placeholder || '请输入'}
      value={raw_value}
      style={{ width: '100%', ...(styles || {}) }}
      onChange={(val) => onValueVhange({ raw_value: val })}
      addonAfter={
        <Select
          style={{ width: 80 }}
          options={options}
          value={raw_unit}
          onChange={(unit) => onValueVhange({ raw_unit: unit })}
          showSearch={false}
          allowClear={false}
        />
      }
    />
  );
});

export default UnitMeta;
