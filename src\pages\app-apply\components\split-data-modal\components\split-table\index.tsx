import CodeMeta from '@/components/meta-dynamic-item/code';
import NumberMeta from '@/components/meta-dynamic-item/number';
import ObjectMeta from '@/components/meta-dynamic-item/object';
import TextMeta from '@/components/meta-dynamic-item/text';
import TimeMeta from '@/components/meta-dynamic-item/time';
import UnitMeta from '@/components/meta-dynamic-item/unit';
import { MetaDataType } from '@/const/metadata';
import { genUuid } from '@/utils';
import { PlusCircleOutlined } from '@ant-design/icons';
import { Button, Form, Radio, Table } from '@gwy/components-web';
import { ColumnProps } from 'antd/es/table';
import { useContext, useMemo, useState } from 'react';
import { DataSplitTableContext } from '../..';
import styles from '../../index.less';
import EditCell, { SplitType } from '../edit-cell';
interface Iprops {
  tags: any[];
  form: any;
  dataUnit: any;
  initDataUnits: any[];
  configDataUnits: any[];
}
const SplitDataTable = (props: Iprops) => {
  const { tags, form, dataUnit, initDataUnits, configDataUnits } = props;
  console.log('dataUnit------', dataUnit);
  const { appIndex, dataUnitIndex } = useContext(DataSplitTableContext);
  const splitType = Form.useWatch(['apps', appIndex, 'dataUnits', dataUnitIndex, 'splitType'], form);
  console.log('splitType', splitType);
  // const dataSourceItem = useMemo(() => {
  //     const template = (tags || []).reduce((acc, item) => {
  //         acc[item.columnName] = '-';
  //         return acc;
  //     }, {});
  //     return template;
  // }, [tags]);
  const [dataSource, setDataSource] = useState(() => {
    return new Array(3).fill(1).map(() => ({ ...(dataUnit?.datasource?.[0] || {}), _id: genUuid() }));
  });
  console.log('dataSource-----', dataSource);
  const handleAdd = () => {
    const multiRows = form.getFieldValue(['apps', appIndex, 'dataUnits', dataUnitIndex, 'multiRows']) || [];
    // debugger;
    form.setFieldValue(
      ['apps', appIndex, 'dataUnits', dataUnitIndex, 'multiRows'],
      [...multiRows, { ...(multiRows?.[0] || {}), splitMainData: false }],
    );
    setDataSource([...dataSource, { ...(dataUnit?.datasource?.[0] || {}), _id: genUuid() }]);
  };
  const columns = useMemo(() => {
    const isMain = dataUnit?.mainDataUnit;

    let cols = (tags || []).map<ColumnProps<any>>((tag) => {
      const { tagName, columnName, config } = tag;
      console.log(tag, 'tag-----------');
      const codeKey = !isMain ? `${dataUnit?.dataUnitId}_${tag?.code}` : tag?.code;
      const { type } = config || {};
      const column: ColumnProps<any> = {
        title: tag.fieldName || tag?.name,
        dataIndex: codeKey,
        key: codeKey,
        width: 160,
        render: (value, record, index) => {
          console.log(value, record, index, type, 'tag----------record');
          const renderCell = () => {
            switch (type) {
              case MetaDataType.Text: {
                return <TextMeta tag={tag} field={tag} value={value} isPreview />;
              }
              case MetaDataType.Number: {
                return <NumberMeta tag={tag} field={tag} value={value} isPreview />;
              }
              case MetaDataType.DateTime: {
                return <TimeMeta tag={tag} field={tag} value={value} isPreview />;
              }
              case MetaDataType.Object: {
                return <ObjectMeta tag={tag} field={tag} value={value} isPreview />;
              }
              case MetaDataType.Unit: {
                return <UnitMeta tag={tag} field={tag} value={value} isPreview />;
              }
              case MetaDataType.Code: {
                return <CodeMeta tag={tag} field={tag} value={value} isPreview />;
              }
              default: {
                return null;
              }
            }
          };

          return <div style={{ maxWidth: 300 }}>{renderCell()}</div>;
        },
      };
      column.onCell = (record, rowIndex) => {
        return {
          title: tag.fieldName || tag?.name,
          dataIndex: tag?.code,
          key: tag?.code,
          width: 160,
          record,
          editable: rowIndex !== 0 && !tag?.readonly,
          rowIndex,
          tag,
        };
      };
      return column;
    });
    const buttonTypeCol: any = {
      title: '审批',
      dataIndex: 'buttonType',
      key: 'buttonType',
      width: 240,
      fixed: 'right',
      render: () => '-',
      onCell: (record, rowIndex) => {
        return {
          record,
          rowIndex,
          isButtonType: true,
          splitType,
          // editable: rowIndex !== 0
        };
      },
    };

    cols = cols.concat([
      buttonTypeCol,
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        width: 50,
        fixed: 'right',
        render: (v, r, index) => {
          const canDelete = index > 2;
          const handleDel = () => {
            const multiRows = form.getFieldValue(['apps', appIndex, 'dataUnits', dataUnitIndex, 'multiRows']) || [];
            multiRows.splice(index, 1);
            form.setFieldValue(['apps', appIndex, 'dataUnits', dataUnitIndex, 'multiRows'], multiRows);
            setDataSource(dataSource.filter((item, i) => i !== index));
          };
          if (!canDelete) return null;
          return (
            <Button size="small" type="link" danger onClick={() => handleDel()}>
              删除
            </Button>
          );
        },
      },
    ]);
    return cols;
  }, [tags, form, dataSource, appIndex, dataUnitIndex, splitType]);

  return (
    <div className={styles.tableWrapper}>
      <div className={styles.ruleTittle} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '10px' }}>
        <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
          {dataUnit?.mainDataUnit ? '主' : '副'}数据单元：{dataUnit?.name}
        </div>
        <Form.Item hidden initialValue={dataUnit?.dataUnitId} name={[dataUnitIndex, 'dataUnitId']} />
        <Form.Item name={[dataUnitIndex, 'splitType']} style={{ marginBottom: 0 }} hidden={configDataUnits.length === 1}>
          <Radio.Group
            onChange={(e) => {
              e.stopPropagation();
              const value = e.target?.value;
              if (value === SplitType.REJECT) {
                form.setFieldValue(['apps', appIndex, 'dataUnits', dataUnitIndex, 'multiRows'], []);
                setDataSource([]);
              }
              if (value === SplitType.SPLIT) {
                const initMultiRows = initDataUnits[dataUnitIndex]?.multiRows;
                form.setFieldValue(['apps', appIndex, 'dataUnits', dataUnitIndex, 'multiRows'], initMultiRows);
                setDataSource(new Array(3).fill(1).map(() => ({ ...(dataUnit?.datasource?.[0] || {}), _id: genUuid() })));
              }
              if (value === SplitType.APPROVE) {
                const initMultiRows = initDataUnits[dataUnitIndex]?.multiRows;
                console.log(initMultiRows, initDataUnits, dataUnitIndex, 'initMultiRows--------------');
                form.setFieldValue(['apps', appIndex, 'dataUnits', dataUnitIndex, 'multiRows'], [initMultiRows?.[0]]);
                setDataSource([{ ...(dataUnit?.datasource?.[0] || {}), _id: genUuid() }]);
              }
            }}
          >
            <Radio value={SplitType.SPLIT}>拆分</Radio>
            <Radio value={SplitType.APPROVE}>审批</Radio>
            <Radio value={SplitType.REJECT}>不处理</Radio>
          </Radio.Group>
        </Form.Item>
      </div>
      {splitType !== SplitType.REJECT && (
        <Form.List name={[dataUnitIndex, 'multiRows']}>
          {() => {
            return (
              <>
                <Table
                  rowKey="_id"
                  columns={columns}
                  dataSource={dataSource}
                  pagination={false}
                  size="small"
                  bordered
                  scroll={{ x: 'max-content' }}
                  components={{
                    body: {
                      cell: EditCell,
                    },
                  }}
                />
                {splitType === SplitType.SPLIT && (
                  <div className={styles.btnWrap}>
                    <Button type="link" icon={<PlusCircleOutlined />} onClick={() => handleAdd()}>
                      添加一行
                    </Button>
                  </div>
                )}
              </>
            );
          }}
        </Form.List>
      )}
    </div>
  );
};

export default SplitDataTable;
