import DataQuerySetting from '@/components/data-query-setting';
import { ExpressionType, FuncType } from '@/types/calc-field';
import { getBeloneDataUnitTags } from '@/utils/calc-field/func';
import { Button, Col, Input, message, Row, Tooltip } from '@gwy/components-web';
import classNames from 'classnames';
import { cloneDeep, isEmpty, set } from 'lodash';
import { forwardRef, useContext, useImperativeHandle, useMemo, useState } from 'react';
import { FieldCreateContext } from '../../../field-create-modal/context';
import { FUNC_OPTIONS, SYMBOL_OPTIONS } from './const';
import styles from './index.less';
import FormulaTabs from './tabs';
import { getFuncShowValue, renderFormula } from './utils';

interface IProps {
  value?: any;
  onChange?: (value: any) => void;
  allDataUnitsWithTags?: any[];
  configuredDataUnits?: any[];
  disabled?: boolean;
  currentFunc?: FuncType;
  setCurrentFunc?: (func: FuncType) => void;
  appendFuncArg?: (arg: any) => void;
  onSelectTag?: (selectedData: any) => void;
  fieldList?: any[];
}

interface IAppRef {
  trigerClick: (obj: any) => void;
}

const FmlEditor = forwardRef<IAppRef, IProps>(
  (
    { value, onChange, allDataUnitsWithTags, configuredDataUnits, disabled, currentFunc, setCurrentFunc, appendFuncArg, onSelectTag, fieldList },
    ref,
  ) => {
    const { fieldType } = useContext(FieldCreateContext);

    // 用户手动输入的值
    const [inputVal, setInputVal] = useState(undefined);

    // 是否有数值类型限制
    // 当选择的函数是SUM、MAX、MIN、AVERAGE、ROUND时，只能添加数值类型、单位类型标签
    // 未选择函数时，只能选择数值类型、单位类型标签
    const isNumOrUnitOnly = useMemo(() => {
      const last = value[value.length - 1];

      if (last?.expressionType === ExpressionType.FUNC) {
        const { functionType } = last.refFunction;
        return ![FuncType.COUNT, FuncType.DISTINCT_COUNT].includes(functionType);
      }
      return true;
    }, [value]);

    // 添加运算符或值或函数
    const handleAdd = (type, val) => {
      const last = value[value.length - 1];

      // 如果当前为函数，再选中值，则放入函数内部
      if (currentFunc && type === ExpressionType.NUMBER) {
        appendFuncArg({ expressionType: type, showValue: String(val), value: val });
        return;
      }
      // 最后一个是函数，再点击函数，则进行替换
      if (last?.expressionType === ExpressionType.FUNC && type === ExpressionType.FUNC) {
        const newLast = {
          ...last,
          refFunction: {
            functionType: val,
            arguments: [],
          },
        };
        newLast.showValue = getFuncShowValue(newLast);
        const newValue = [...value.splice(0, value.length - 1), newLast];
        onChange(newValue);
        setCurrentFunc(val);
        return;
      }

      // 表达式开头只能是：变量、值、左括号
      if (value.length === 0 && type === ExpressionType.SYMBOL && val !== '(') {
        message.warning('表达式非法');
        return;
      }

      // 不能连续输入2个操作符
      const lastVal = value[value.length - 1]?.value;
      const isIlleagl =
        !['(', ')'].includes(val) &&
        !['(', ')'].includes(lastVal) &&
        type === ExpressionType.SYMBOL &&
        SYMBOL_OPTIONS.some((item) => item.value === lastVal);

      if (isIlleagl) {
        message.warning('表达式非法');
        return;
      }

      let appendItem;
      if (type === ExpressionType.FUNC) {
        // 添加函数
        appendItem = {
          expressionType: type,
          refFunction: {
            functionType: val,
            arguments: [],
          },
        };
        appendItem.showValue = getFuncShowValue(appendItem);
        setCurrentFunc(val);
      } else {
        appendItem = {
          expressionType: type,
          showValue: String(val),
          value: String(val),
        };
        setCurrentFunc(null);
      }

      onChange([...value, appendItem]);
    };

    // 点击删除
    const handleDelete = () => {
      onChange(value.slice(0, -1));
      setCurrentFunc(null);
    };

    // 往表达式中添加值
    const addInputVal = () => {
      if (inputVal === '' || inputVal === undefined) {
        message.warning('表达式非法');
        return;
      }
      const transformedVal = +inputVal || inputVal;
      handleAdd(ExpressionType.NUMBER, transformedVal);
      setInputVal(undefined);
    };

    // 是否开启条件组设置弹窗
    const [openOpt, setOpenOpt] = useState<{ open: boolean; type: ExpressionType }>({
      open: false,
      type: undefined,
    });
    // 当前选中的标签
    const [selected, setSelected] = useState<any>({});
    // 当前选中的标签、字段在value里的路径
    const [selectedPathname, setSelectedPathname] = useState<any>({});
    /**
     * 标签条件组设置里条件左侧数据单元标签选项
     * 取当前选中标签所在数据单元的标签
     */
    const conditionDataUnitTags = useMemo(() => {
      if (selected && selected.expressionType === ExpressionType.TAG) {
        const { dataUnitId } = selected.refTag;
        const targetDataUnit = (allDataUnitsWithTags || []).find((t) => t.dataUnitId === dataUnitId);
        return getBeloneDataUnitTags([targetDataUnit], allDataUnitsWithTags);
      }
      return [];
    }, [allDataUnitsWithTags, selected]);
    /**
     * 筛选条件右侧变量标签选项
     * 包含当前选中标签所在的数据单元，以及配置的主副数据单元的标签
     */
    const variableDataUnitTags = useMemo(() => {
      if (selected && selected.expressionType === ExpressionType.TAG) {
        const { dataUnitId } = selected.refTag;

        if ((configuredDataUnits || []).some((t) => t.dataUnitId === dataUnitId)) {
          return getBeloneDataUnitTags(configuredDataUnits, allDataUnitsWithTags);
        }
        const dataUnits = (allDataUnitsWithTags || []).filter((t) => t.dataUnitId === dataUnitId);
        return getBeloneDataUnitTags(dataUnits.concat(configuredDataUnits), allDataUnitsWithTags);
      }
      return [];
    }, [allDataUnitsWithTags, configuredDataUnits, selected]);
    const closeModal = () => {
      setOpenOpt({ open: false, type: undefined });
    };
    /**
     * 点击公式中的标签或者运算符或者字段
     * @param {obj} 当前点击的对象
     * @param {tagPathname} 当前点击的标签在value里的路径
     */
    const handleClick = ({ obj, tagPathname }: any) => {
      // 处理标签类型
      if (obj.expressionType === ExpressionType.TAG && !isEmpty(allDataUnitsWithTags)) {
        setOpenOpt({ open: true, type: ExpressionType.TAG });
      }
      setSelected(obj);
      setSelectedPathname(tagPathname);
    };

    const handleTagSettingOk = (data) => {
      const _value = cloneDeep(value);
      const _tag = cloneDeep(selected);
      _tag.refTag.conditionGroups = data?.conditionGroups || [];
      _tag.refTag.extConfig = data?.extConfig;
      set(_value, selectedPathname, _tag);
      onChange(_value);
      closeModal();
    };

    useImperativeHandle(ref, () => {
      return {
        trigerClick: handleClick,
      };
    });

    return (
      <div className={styles.container}>
        <div className={styles.title}>公式编辑区</div>
        <div className={styles.content}>
          <div style={{ fontWeight: 'bold' }}>公式=</div>
          <div>{renderFormula(disabled, value, handleClick)}</div>
        </div>
        <div className={classNames(styles.operateContent, { [styles.disabledContainer]: disabled })}>
          <FormulaTabs
            allDataUnitsWithTags={allDataUnitsWithTags || []}
            isNumOrUnitOnly={isNumOrUnitOnly}
            fields={fieldList || []}
            fieldType={fieldType}
            onSelectTag={onSelectTag}
            disabled={disabled}
          />
          <div className={classNames(styles.operateBox)}>
            <div className={styles.title}>函数</div>
            <div className={styles.operatorWrap}>
              <Row gutter={[16, 16]}>
                {FUNC_OPTIONS.map(({ label, value, title }) => {
                  return (
                    <Col key={value}>
                      <Tooltip title={title}>
                        <Button
                          disabled={disabled}
                          type={currentFunc === value ? 'primary' : 'default'}
                          onClick={() => {
                            if (currentFunc === value) {
                              // 取消选中
                              setCurrentFunc(null);
                            } else {
                              handleAdd(ExpressionType.FUNC, value);
                            }
                          }}
                        >
                          {label}
                        </Button>
                      </Tooltip>
                    </Col>
                  );
                })}
              </Row>
            </div>
            <div className={styles.title}>运算符</div>
            <div className={styles.operatorWrap}>
              <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
                {SYMBOL_OPTIONS.map(({ label, value }) => {
                  return (
                    <Col key={value}>
                      <Button disabled={disabled} onClick={() => handleAdd(ExpressionType.SYMBOL, value)}>
                        {label}
                      </Button>
                    </Col>
                  );
                })}
                <Col>
                  <Button disabled={disabled} onClick={handleDelete}>
                    删除
                  </Button>
                </Col>
              </Row>
              <Row gutter={[16, 16]} wrap={false}>
                <Col flex="auto">
                  <Input
                    disabled={disabled}
                    value={inputVal}
                    onChange={(e) => {
                      setInputVal(e.target.value.trim());
                    }}
                    onPressEnter={addInputVal}
                  />
                </Col>
                <Col>
                  <Button disabled={disabled} onClick={addInputVal}>
                    确定
                  </Button>
                </Col>
              </Row>
            </div>
          </div>
        </div>
        {openOpt.type === ExpressionType.TAG && openOpt.open && (
          <DataQuerySetting
            disabled={disabled}
            fieldType={fieldType}
            isMainDataUnit={true}
            dataUnitName={selected?.refTag?.dataUnitName}
            dataUnitId={selected?.refTag?.dataUnitId}
            conditionGroups={selected?.refTag?.conditionGroups}
            extConfig={selected?.refTag?.extConfig}
            tags={conditionDataUnitTags}
            canRelateTags={variableDataUnitTags}
            calcFields={fieldList}
            onOk={handleTagSettingOk}
            onCancel={closeModal}
          />
        )}
      </div>
    );
  },
);
FmlEditor.displayName = 'FmlEditor';

export default FmlEditor;
