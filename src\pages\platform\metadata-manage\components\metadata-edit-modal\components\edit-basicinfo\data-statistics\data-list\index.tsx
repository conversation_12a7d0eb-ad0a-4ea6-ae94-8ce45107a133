import { MetaDataType } from '@/const/metadata';
import { DataUnitTagVO } from '@/types/tag';
import { getFileIconByUrl } from '@/utils/metadata';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Image, TablePaginationConfig, TextEllipsisTooltip } from '@gwy/components-web';
import { useMemo } from 'react';
import styles from './index.less';

type Props = {
  tags?: DataUnitTagVO[];
  dataSource?: any[];
  pagination?: TablePaginationConfig;
  headerTitle?: React.ReactNode;
  toolBarRender?: () => any[];
  renderColumns?: (columns: ProColumns[]) => ProColumns[];
};

const DataList = ({ tags, dataSource, pagination, headerTitle, toolBarRender, renderColumns }: Props) => {
  const renderFileItem = (file: any) => {
    const { url, name } = file;
    const FileIcon = getFileIconByUrl(url);
    return (
      <>
        <img src={FileIcon} alt={name} />
        <a href={url} target="_blank" rel="noreferrer">
          {name}
        </a>
      </>
    );
  };
  const renderFiles = (files: any[]) => {
    return (
      <div className={styles.fileWrapper}>
        {files.map((file, idx) => (
          <div key={idx} className={styles.fileItem}>
            {renderFileItem(file)}
          </div>
        ))}
      </div>
    );
  };
  const columns = useMemo(() => {
    const cols = tags?.map<ProColumns<DataUnitTagVO>>((tag) => {
      const { tagMetaDataConfig } = tag;
      const { type } = tagMetaDataConfig || {};
      return {
        title: tag.name,
        width: 'auto',
        dataIndex: tag.code,
        render: (value, record, index) => {
          const renderCell = () => {
            const { value: tagValue, data: tagData } = record[tag.code] || {};

            if (!tagData && tagData !== 0) {
              return '-';
            }

            if (type === MetaDataType.Picture) {
              const imgs = Array.isArray(tagData) ? tagData : [tagData];
              return (
                <div className={styles.imagesWrapper}>
                  {imgs.map((img, idx) => (
                    <Image key={idx} preview src={img.url} alt={img.name} style={{ maxWidth: 40 }} />
                  ))}
                </div>
              );
            }

            if (type === MetaDataType.File) {
              // mock data
              // const files = Array.of(
              //   {
              //     url: 'https://doc.gongwuyun.com/ngwy/static/static-images/cg-gwy-platform/datasource/<EMAIL>',
              //     name: 'file1.docx',
              //   },
              //   {
              //     url: 'https://doc.gongwuyun.com/ngwy/static/static-images/cg-gwy-platform/datasource/<EMAIL>',
              //     name: 'file2.docx',
              //   },
              //   {
              //     url: 'https://doc.gongwuyun.com/ngwy/static/static-images/cg-gwy-platform/datasource/<EMAIL>',
              //     name: 'file3.docx',
              //   },
              //   {
              //     url: 'https://doc.gongwuyun.com/ngwy/static/static-images/cg-gwy-platform/datasource/<EMAIL>',
              //     name: 'file4.docx',
              //   },
              //   {
              //     url: 'https://doc.gongwuyun.com/ngwy/static/static-images/cg-gwy-platform/datasource/<EMAIL>',
              //     name: 'file5.docx',
              //   },
              //   {
              //     url: 'https://doc.gongwuyun.com/ngwy/static/static-images/cg-gwy-platform/datasource/<EMAIL>',
              //     name: 'file6.docx',
              //   },
              //   {
              //     url: 'https://doc.gongwuyun.com/ngwy/static/static-images/cg-gwy-platform/datasource/<EMAIL>',
              //     name: 'file7.docx',
              //   },
              //   {
              //     url: 'https://doc.gongwuyun.com/ngwy/static/static-images/cg-gwy-platform/datasource/<EMAIL>',
              //     name: 'file8.docx',
              //   },
              // );
              const files = Array.isArray(tagData) ? tagData : [tagData];
              return renderFiles(files);
            }

            if (typeof tagValue === 'object') {
              // 方便查bug
              return '?';
            }

            return <TextEllipsisTooltip text={tagValue ?? '-'} />;
          };

          return <div style={{ maxWidth: 300 }}>{renderCell()}</div>;
        },
      };
    });
    cols?.unshift({
      title: '序号',
      width: 85,
      dataIndex: 'idx',
      render: (value, record, index) => index + 1,
    });

    if (renderColumns) {
      return renderColumns(cols);
    }

    return cols;
  }, [tags, renderColumns]);

  return (
    <div className={styles.container}>
      <ProTable
        columns={columns}
        dataSource={dataSource}
        rowKey="_id"
        pagination={pagination}
        search={false}
        headerTitle={headerTitle}
        toolBarRender={toolBarRender}
        options={{
          setting: {
            /**
             * 默认虚拟滚动，会莫名导致部分数据被切割，无法滚出（查不出原因，官网例子没问题）
             * github中的类似问题参考：https://github.com/ant-design/pro-components/issues/8841
             * 解决：给个超大高度用于禁止虚拟滚动，同时在global.less中设置样式
             * .ant-pro-table-column-setting-overlay > .ant-popover-content > .ant-popover-inner > .ant-popover-inner-content {
                  max-height: 400px;
                  overflow-y: auto;
                }
             */
            listsHeight: 99999,
          },
          fullScreen: false,
          density: false,
          reload: false,
          search: false,
        }}
        scroll={{ x: 'max-content' }}
        bordered
        size="small"
      />
    </div>
  );
};

export default DataList;
