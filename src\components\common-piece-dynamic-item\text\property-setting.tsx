import { TextInputType } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import TextEnumPropertySetting from './enum/property-setting';
import TextManualPropertySetting from './manual/property-setting';

type Props = {
  tag: DataUnitTagVO;
  field: FormField;
};

const TextPropertySetting = ({ tag, field }: Props) => {
  const { inputType } = tag.tagMetaDataConfig?.metaDataTextDTO || {};

  if (inputType === TextInputType.enum) {
    return <TextEnumPropertySetting tag={tag} field={field} />;
  }

  return <TextManualPropertySetting tag={tag} field={field} />;
};

export default TextPropertySetting;
