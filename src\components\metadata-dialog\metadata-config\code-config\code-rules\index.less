.container {
  :global {
    .ant-collapse {
      border: none;
      background-color: transparent;

      .ant-collapse-item {
        border-bottom: none;
        background-color: #eef6ff;

        .ant-collapse-header {
          padding: 10px 12px;
          // margin: 10px 0;
          align-items: center;
        }

        .ant-collapse-content {
          background-color: transparent;
          border-top: none;

          .ant-collapse-content-box {
            padding: 0;
          }
        }
      }
    }
  }
}

.groupList {
  .groupHeader {
    // margin-bottom: 10px;
    .groupHeaderTitle {
    }

    .groupHeaderDesc {
      color: #86909c;
      font-size: 12px;
      margin-left: 10px;
    }

    .groupDelete {
      position: absolute;
      right: 12px;
      top: 12px;
    }
  }

  .groupItem {
    position: relative;
    // background-color: #eef6ff;
    border-radius: 4px;
    padding: 0 12px;
    // margin-bottom: 12px;

    .ruleList {
      .ruleItem {
        position: relative;
        padding-left: 30px;

        .ruleDelete {
          position: absolute;
          right: 0;
          top: 8px;
        }

        .ruleDrag {
          position: absolute;
          left: 0;
          top: 8px;
        }
      }
    }
  }
}

.addGroupBtnWrapper {
  display: flex;
  justify-content: center;
  align-items: center;

  &.emptyList {
    border: 1px dashed #c9cdd4;
    padding: 20px 0;
  }
}
