import { FileD<PERSON><PERSON><PERSON> } from '@/components/metadata-dialog/metadata-config/file-config';
import { FileFormatsOptions, FileSizeUnit } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { Form, InputNumber, Radio, Select, Space, TreeSelect } from '@gwy/components-web';
import { useEffect, useMemo } from 'react';

type Props = {
  field: FormField;
  tag: DataUnitTagVO;
};

const fileSizeUnitMap = {
  [FileSizeUnit.GB]: [FileSizeUnit.GB, FileSizeUnit.MB, FileSizeUnit.KB],
  [FileSizeUnit.MB]: [FileSizeUnit.MB, FileSizeUnit.KB],
  [FileSizeUnit.KB]: [FileSizeUnit.KB],
};

const getMaxFileSize = (sizeMax, metaFileSizeUnit, fileSizeUnit) => {
  const max = Number(sizeMax);
  // fileSizeUnit 会小于等于 metaFileSizeUnit
  if (metaFileSizeUnit === fileSizeUnit) {
    return max;
  }
  if (fileSizeUnit === FileSizeUnit.MB) {
    return 1024 * max;
  }
  if (fileSizeUnit === FileSizeUnit.KB) {
    return metaFileSizeUnit === FileSizeUnit.MB ? 1024 * max : 1024 * 1024 * max;
  }
};
const FilePropertySetting = ({ field, tag }: Props) => {
  const form = Form.useFormInstance();
  const { metaDataFileDTO } = tag.tagMetaDataConfig;
  const { ifLimitFormat: metaIfLimitFormat, formats, sizeMax: metaSizeMax, sizeUnit: metaSizeUnit, fileNum } = metaDataFileDTO;

  const ifLimitFormat = Form.useWatch([FileDTOKey, 'ifLimitFormat'], form);
  const sizeUnit = Form.useWatch([FileDTOKey, 'sizeUnit'], form);

  const maxSize = getMaxFileSize(metaSizeMax, metaSizeUnit, sizeUnit);

  const formatsOptions = useMemo(() => {
    return (metaIfLimitFormat ? FileFormatsOptions.filter((item) => (formats || []).some((f) => item.formats.includes(f))) : FileFormatsOptions).map(
      (opt) => ({
        title: opt.label,
        value: opt.value,
        children: (metaIfLimitFormat ? opt.formats.filter((f) => (formats || []).includes(f)) : opt.formats).map((f) => ({
          title: f,
          value: f,
        })),
      }),
    );
  }, [formats, metaIfLimitFormat]);

  const fileSizeUnitList = useMemo(() => {
    return fileSizeUnitMap[metaSizeUnit].map((s) => ({ label: s, value: s }));
  }, [metaSizeUnit]);

  useEffect(() => {
    form.setFieldsValue(field.config);
  }, []);

  return (
    <div>
      {!metaIfLimitFormat && (
        <Form.Item label="文件格式" name={[FileDTOKey, 'ifLimitFormat']} rules={[{ required: true, message: '请选择' }]}>
          <Radio.Group
            options={[
              { label: '不限制', value: false },
              { label: '限制', value: true },
            ]}
          />
        </Form.Item>
      )}
      {(metaIfLimitFormat || ifLimitFormat) && (
        <Form.Item label="格式类型" name={[FileDTOKey, 'formats']} rules={[{ required: true, message: '请选择' }]}>
          <TreeSelect treeData={formatsOptions} allowClear={false} maxTagCount="responsive" virtual={false} treeCheckable />
        </Form.Item>
      )}

      <Form.Item label="单文件容量大小" required style={{ marginBottom: 0 }}>
        <Space align="start">
          <Form.Item
            style={{ width: '100px' }}
            name={[FileDTOKey, 'sizeMax']}
            rules={[
              { required: true, message: '请输入' },
              {
                validator: (_, value) => {
                  if (value || value === 0) {
                    if (Number(value) > maxSize) {
                      return Promise.reject(`单文件容量大小不能大于${metaSizeMax}${metaSizeUnit}`);
                    }
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <InputNumber placeholder="请填写" min={0} step={1} />
          </Form.Item>
          <Form.Item name={[FileDTOKey, 'sizeUnit']} rules={[{ required: true, message: '请选择' }]}>
            <Select placeholder="请选择" options={fileSizeUnitList} allowClear={false} />
          </Form.Item>
        </Space>
      </Form.Item>
      <Form.Item
        label="文件数量"
        name={[FileDTOKey, 'fileNum']}
        rules={[
          { required: true, message: '请输入' },
          { type: 'number', max: fileNum, message: `文件数量不可超过${fileNum}` },
        ]}
      >
        <InputNumber min={1} step={1} precision={0} />
      </Form.Item>
    </div>
  );
};

export default FilePropertySetting;
