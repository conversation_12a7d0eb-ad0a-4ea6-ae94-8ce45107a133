import IconFormApprove from '@/assets/app-form-manage/icon-form-approve.png';
import IconFormExcute from '@/assets/app-form-manage/icon-form-execution.png';
import { MetaAppStatus } from '@/const/metadata';
import { BarsOutlined } from '@ant-design/icons';
import { Button, Checkbox, Divider, Empty, Input, Modal, Tabs, TextEllipsisTooltip } from '@gwy/components-web';
import { useState } from 'react';
import { executeTagType, tagType } from '../manage-list/card';
import styles from './index.less';

interface Iprops {
  onOk?: () => void;
  onCancel?: () => void;
  appList: any[];
  batchIcon?: React.ReactNode;
  batchStyle?: React.CSSProperties;
}
const { TabPane } = Tabs;
const BatchFormSetting = (props: Iprops) => {
  const { onOk, onCancel, appList, batchIcon, batchStyle } = props;
  const [activeKey, setActiveKey] = useState('1');
  const [searchInput, setSearchInput] = useState('');
  const [checkedApp, setCheckedApp] = useState([]);
  const [batchOpen, setBatchOpen] = useState(false);
  const renderCardApp = (item: any) => {
    const { formVersionId, appType, name, postName, userName, userId, createTime, status, enable } = item;
    const isExcuteForm = appType === 'SUBMIT';
    return (
      <div
        className={styles.cardWrapper}
        key={formVersionId}
        onDragStart={(e) => {
          e.dataTransfer.setData('text', item?.id);
        }}
        onDragOver={(e) => {
          e.preventDefault();
        }}
      >
        <header className={styles.headerBox}>
          <img src={isExcuteForm ? IconFormExcute : IconFormApprove} width={31} height={36} />
          <div style={{ flex: 1, overflow: 'hidden' }}>
            <article className={styles.headerInfo}>
              <div className={styles.titleBox}>
                <span className={styles.title}>
                  <TextEllipsisTooltip text={name} />
                </span>
                <div className={styles.tagBox}>
                  <div
                    className={styles.tag}
                    style={{
                      color: tagType[String(enable)]?.color,
                      backgroundColor: tagType[String(enable)]?.backgroundColor,
                      border: tagType[String(enable)]?.border,
                    }}
                  >
                    {tagType[String(enable)]?.label}
                  </div>
                  {status === MetaAppStatus.Stash && (
                    <div
                      className={styles.tag}
                      style={{
                        color: executeTagType?.[status]?.color,
                        backgroundColor: executeTagType?.[status]?.backgroundColor,
                        border: `1px solid ${executeTagType?.[status]?.color}`,
                        minWidth: '44px',
                      }}
                    >
                      {executeTagType?.[status]?.label}
                    </div>
                  )}
                </div>
              </div>
            </article>
            <div className={styles.timeLabel}>
              <span>{postName}</span>
              {userId && <span>/{userName}</span>}
            </div>
          </div>
        </header>
        <Divider style={{ margin: 0 }} />
        <aside className={styles.contentBox}>
          {/* <div className={styles.infoBox}></div> */}
          <div className={styles.footerBox}>
            <div className={styles.timeBox}>
              <span>创建时间：</span>
              <span>{createTime}</span>
            </div>
            <Checkbox value={formVersionId} key={formVersionId} />
          </div>
        </aside>
      </div>
    );
  };
  const renderList = () => {
    if (appList.length > 0) {
      return (
        <div className={styles.tabChildren}>
          <div className={styles.headerWrapper}>
            <div className={styles.searchWrapper}>
              <Input.Search
                className={styles.search}
                onChange={(e) => setSearchInput(e.target.value)}
                value={searchInput}
                placeholder="搜索表单名称"
              />
            </div>
            {/* {!!appListFilter.length && ( */}
            <Checkbox
              className={styles.checkBoxAll}
              indeterminate={checkedApp.length > 0 && checkedApp.length < appList.length}
              onChange={(e) => setCheckedApp(e.target.checked ? appList.map((app) => app.formVersionId) : [])}
              checked={appList.length === checkedApp.length}
            >
              全选
            </Checkbox>
            {/* )} */}
          </div>
          <div className={styles.bodyWrapper}>
            <Checkbox.Group value={checkedApp} onChange={(checkedList) => setCheckedApp(checkedList)} className={styles.appListCheck}>
              {(appList || [])?.filter((item) => (item?.name || '')?.includes(searchInput))?.map((item) => renderCardApp(item))}
            </Checkbox.Group>
          </div>
        </div>
      );
    }
    return <Empty description="暂无数据" />;
  };
  const items = [
    {
      label: '批量禁用',
      key: '1',
      children: renderList(),
    },
    {
      label: '批量启用',
      key: '2',
      children: renderList(),
    },
    {
      label: '批量删除',
      key: '3',
      children: renderList(),
    },
    {
      label: '批量授权',
      key: '4',
      children: renderList(),
    },
  ];

  return (
    <>
      <Button
        type="link"
        icon={
          batchIcon ? (
            batchIcon
          ) : (
            <BarsOutlined
              style={{
                color: '#4b87fb',
                marginRight: 0,
                ...(batchStyle || {}),
              }}
            />
          )
        }
        style={{
          marginRight: 10,
          color: '#4b87fb',
          ...(batchStyle || {}),
        }}
        onClick={(e) => {
          e.stopPropagation();
          setBatchOpen(true);
        }}
      >
        批量操作
      </Button>
      {batchOpen && (
        <Modal
          title="批量设置"
          size="middle"
          onCancel={() => {
            onCancel?.();
            setBatchOpen(false);
          }}
          onOk={() => {
            onOk?.();
            setBatchOpen(false);
          }}
          open={batchOpen}
        >
          <div className={styles.batchSetting}>
            <Tabs activeKey={activeKey} onChange={(key) => setActiveKey(key)} type="card" size="small" items={items}></Tabs>
          </div>
        </Modal>
      )}
    </>
  );
};

export default BatchFormSetting;
