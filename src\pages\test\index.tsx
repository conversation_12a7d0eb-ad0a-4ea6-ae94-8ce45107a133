import MapItem from '@/components/meta-dynamic-item/address/map';
import DataTable, { DataTableRef } from '@/components/meta-dynamic-item/data-table';
import { Button, Form } from '@gwy/components-web';
import { useRef, useState } from 'react';

const Test = () => {
  const [items, setItems] = useState([{ id: '1' }, { id: '2' }, { id: '3' }, { id: '4' }, { id: '5' }, { id: '6' }, { id: '7' }, { id: '8' }]);

  const [form] = Form.useForm();
  const localRef = useRef<{
    [k: string]: DataTableRef;
  }>({});
  return (
    <>
      {/* <div style={{ width: 300, height: 300, overflow: 'auto', border: '1px solid #ccc' }}>
        <SortableList
          items={items}
          onChange={setItems}
          renderItem={(item) => (
            <SortableList.Item id={item.id}>
              {item.id}
              <SortableList.DragHandle />
            </SortableList.Item>
          )}
        />
      </div> */}
      <div style={{ padding: 20 }}>
        <Form form={form}>
          <Form.Item name="table">
            <DataTable ref={(ele) => (localRef.current['aa'] = ele)} setLoading={() => false} />
          </Form.Item>
          <Form.Item name="map">
            <MapItem />
          </Form.Item>
        </Form>
        <Button
          onClick={async () => {
            try {
              const values = await localRef.current['aa'].getValues();
              console.log(values);
            } catch (error) {
              console.log(error);
            }
          }}
        >
          获取值
        </Button>
      </div>
    </>
  );
};

export default Test;
