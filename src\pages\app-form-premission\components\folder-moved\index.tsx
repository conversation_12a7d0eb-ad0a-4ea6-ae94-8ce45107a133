// import TabsHeaderNew from '@/components/tabs-header-new';
// import { dataSourceV3API } from '@/services';
import { Form, FormProps, Modal, TreeSelect, message } from '@gwy/components-web';
import React, { memo, useCallback, useEffect, useState } from 'react';
// import { AppSourceType } from '../../../../onlyoffice-application-form-manage/const';
// import { TAB_TYPE } from '../const';
import styles from './index.less';

type Props = {
  nowTab?: string;
  fromFolderId: string;
  appId: string;
  type?: string;
  postId: string;
  onCancel: () => void;
  onConfirm: () => void;
};

const FolderMoveModal: React.FC<Props> = memo(({ nowTab, fromFolderId, appId, type, postId, onCancel, onConfirm }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const formConfig: FormProps<any> = {
    form,
    labelCol: { span: 4 },
    labelAlign: 'left',
  };
  const [treeData, setTreeData] = useState<any[]>([]);
  const [toFolderId, setToFolderId] = useState<string | undefined>();
  const fetchFolderTree = useCallback(async () => {
    const params = {
      postId,
      //   bizType: TAB_TYPE[nowTab],
    };
    // 获取文件夹树
    // const { childList = [] } = await dataSourceV3API.getFolderTree(params);
    const childList = [];
    // 写一个方法 递归生成树, 将childList里所有层级都转换为树形结构
    const getChildren = (list) => {
      return list.map((item) => {
        return {
          title: item.folderName,
          value: item.folderId,
          key: item.folderId,
          children: getChildren(item.childList || []),
        };
      });
    };
    const data = getChildren([
      {
        folderName: '主页',
        folderId: 'index',
      },
      ...childList,
    ]);

    setTreeData(data);
  }, [nowTab, postId]);

  useEffect(() => {
    fetchFolderTree();
  }, [fetchFolderTree]);

  const handleOk = () => {
    form.validateFields().then(async (value) => {
      setLoading(true);
      try {
        const params = {
          ...value,
          fromFolderId,
          toFolderId: toFolderId === 'index' ? '' : toFolderId,
          bizId: appId, // 业务id
          //   oldBizId: type === AppSourceType.OLD_APP ? appId : undefined,
          //   formId: type === AppSourceType.FORM ? appId : undefined,
        };

        // await dataSourceV3API.moveToFolder(params);
        message.success('移动成功');
        onConfirm();
      } catch (err) {
        message.warning(err.desc);
      }
      setLoading(false);
    });
  };
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      open
      title="移动分组"
      size="small"
      // width={480}
      //   bodyStyle={{ padding: 0, height: 300 }}
      className={styles.folderModal}
      // closable={false}
      maskClosable={false}
      okButtonProps={{ loading }}
      onOk={handleOk}
      onCancel={handleCancel}
    >
      {/* <TabsHeaderNew title="移动分组" leftTitle onClose={onCancel} /> */}
      <div className={styles.content}>
        <Form {...formConfig} colon={false}>
          <Form.Item label="选择分组" name="bizIdList" rules={[{ required: true, message: '请选择分组' }]}>
            <TreeSelect
              showSearch
              style={{ width: '100%' }}
              dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
              placeholder="请选择分组"
              allowClear
              treeDefaultExpandAll
              onChange={(value) => {
                setToFolderId(value);
              }}
              treeData={treeData}
            />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
});

export default FolderMoveModal;
