import { DatePicker, Form, TimeRangePickerProps } from '@gwy/components-web';
import dayjs from 'dayjs';
import { DateTimeDTOKey } from '../metadata-dialog/metadata-config/time-config';

type Props = {
  value?: string[];
  onChange?: (dateStrings: string[]) => void;
  startKey?: string;
  endKey?: string;
  format?: string;
  showTime?: boolean;
} & Omit<TimeRangePickerProps, 'value'>;

const CustomRangePicker = ({ value, onChange, startKey = 'start', endKey = 'end', format = 'YYYY-MM-DD HH:mm:ss', ...rest }: Props) => {
  const form = Form.useFormInstance();

  return (
    <DatePicker.RangePicker
      {...rest}
      value={value?.[0] && value?.[1] ? [dayjs(value[0]), dayjs(value[1])] : undefined}
      onChange={(date) => {
        const start = date?.[0]?.format(format);
        const end = date?.[1]?.format(format);
        if (form) {
          form.setFieldValue([DateTimeDTOKey, startKey], start);
          form.setFieldValue([DateTimeDTOKey, endKey], end);
        }
        onChange?.([start, end]);
      }}
    />
  );
};

export default CustomRangePicker;
