import type { FC } from 'react';
import { memo } from 'react';
import { useDrop } from 'react-dnd';

export interface DustbinProps {
  className?: string;
  accept: string[];
  lastDroppedItem?: any;
  onDrop: () => void;
  onClick?: () => void;
  children?: React.ReactNode;
}

const Dustbin: FC<DustbinProps> = memo(function Dustbin({ className, accept, lastDroppedItem, onDrop, onClick, children }) {
  const [{ isOver, isOverCurrent, canDrop }, drop] = useDrop({
    accept,
    drop: (_item: unknown, monitor) => {
      if (isOverCurrent) {
        return onDrop();
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
      isOverCurrent: monitor.isOver({ shallow: true }),
    }),
  });

  const isActive = isOver && canDrop;
  let backgroundColor = 'transparent';
  // if (isActive) {
  //   backgroundColor = 'darkgreen';
  // } else if (canDrop) {
  //   backgroundColor = 'darkkhaki';
  // }

  return (
    <div
      ref={drop}
      className={className}
      onClick={(e) => {
        e.stopPropagation();
        onClick?.();
      }}
    >
      {children}
    </div>
  );
});

export default Dustbin;
