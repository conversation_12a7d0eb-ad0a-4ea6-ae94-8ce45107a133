import FilterGroup, { UseInStageEnum } from '@/components/filter-group';
import { DATA_TYPE } from '@/components/filter-group/filter-const';
import AddressMeta from '@/components/meta-dynamic-item/address';
import CodeMeta from '@/components/meta-dynamic-item/code';
import FileMeta from '@/components/meta-dynamic-item/file';
import NumberMeta from '@/components/meta-dynamic-item/number';
import ObjectMeta from '@/components/meta-dynamic-item/object';
import TextMeta from '@/components/meta-dynamic-item/text';
import TimeMeta from '@/components/meta-dynamic-item/time';
import UnitMeta from '@/components/meta-dynamic-item/unit';
import { MetaDataType, MetaTagType, MetaUnitGeneralType } from '@/const/metadata';
import { getOrgDataUnits, getOrgDataUnitsTags } from '@/services/org-data-unit';
import { FormField } from '@/types/form-field';
import { CloseCircleFilled, DeleteOutlined, EditOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Col, Form, Modal, Row, Select, TreeSelect } from '@gwy/components-web';
import { Divider } from 'antd';
import { cloneDeep, uniqueId } from 'lodash-es';
import { useEffect, useMemo, useRef, useState } from 'react';
import { createFormField } from '../../../left-panel/components-design/utils';
import EditGroupName from './edit-group-name';
import styles from './index.less';

interface Iprops {
  onCancel: () => void;
  onOk: (values?: any) => void;
  dataUnitsOps: any[]; // 已配置的数据单元
  cFiellds: any[]; // 配置的字段
  orgId: any;
  disabled?: boolean;
  detailData?: any;
  basicDataUnitOps?: any[];
}

export enum OperateTypesEnum {
  SYNC = 'SYNC',
  SET = 'SET',
}
export enum tagSyncTypeEnum {
  TAG = 'TAG',
  FIELD = 'FIELD',
}
const DataSyncSetting = (props: Iprops) => {
  const { onCancel, onOk, dataUnitsOps, disabled, cFiellds, detailData, orgId, basicDataUnitOps } = props;
  const [form] = Form.useForm();
  const [filterForm] = Form.useForm();
  // 所有有权限的数据单元，不带标签
  const [permissionDataUnits, setPermissionDataUnits] = useState([]);
  // 标签map
  const storeRef = useRef(null);
  const [unitTagMap, setUnitTagMap] = useState({});
  const [filterOpen, setFilterOpen] = useState({
    open: false,
    data: null,
  });
  // console.log(dataUnitsOps, 'dataUnitsOps---------')
  // 已配置数据单元的所有标签
  const [canUsedAllTags, hadUsedDataUnitdIds] = useMemo(() => {
    let results = [];
    let hadUsedDataUnitIds = Array.from(new Set(cloneDeep(dataUnitsOps)?.map((dataUnit) => dataUnit?.dataUnitId) || []));
    // console.log(hadUsedDataUnitIds, 'hadUsedDataUnitIds------')
    // console.log(dataUnitsOps, 'dataUnitsOps---------')
    dataUnitsOps.forEach((dataUnit) => {
      if (dataUnit?.tagList?.length > 0) {
        results.push(
          ...(dataUnit.tagList || []).map((tag) => ({
            ...tag,
            belongDataUnitId: dataUnit.dataUnitId,
            belongDataUnitName: dataUnit.name,
          })),
        );
      }
    });
    return [results, hadUsedDataUnitIds];
  }, [dataUnitsOps]);

  // 获取目标数据单元
  const getTargetDataUnits = async () => {
    // mock数据用
    let data = await getOrgDataUnits({
      orgId,
    });
    setPermissionDataUnits(
      data
        ?.filter((item) => !hadUsedDataUnitdIds.includes(item?.dataUnitId))
        ?.map((unit) => ({
          ...unit,
          label: unit.name,
          value: unit.dataUnitId,
        })),
    );
  };
  useEffect(() => {
    getTargetDataUnits();
  }, []);
  // 数据回显
  useEffect(() => {
    if (detailData) {
      // console.log(detailData, 'detailData---------');
      const showDataunits = detailData?.map?.((item) => item.targetDataUnitId) || [];
      getDataUnitToTags(showDataunits);
      form.setFieldsValue({
        dataSyncConfigs: detailData,
      });
    }
  }, [detailData]);
  // 获取数据单元对应的标签
  const getDataUnitToTags = async (dataUnit) => {
    if (unitTagMap[dataUnit]?.length > 0) {
      return unitTagMap[dataUnit];
    }
    const dataUnits = Array.isArray(dataUnit) ? dataUnit : [dataUnit];
    const data = await getOrgDataUnitsTags({
      orgId, // mock用
      dataUnitIds: dataUnits,
    });
    if (Array.isArray(dataUnit)) {
      const map = data.forEach((item) => {
        unitTagMap[item.dataUnitId] = item.tagList?.map((tag) => ({
          ...tag,
          label: tag.name,
          value: tag.tagId,
        }));
      });
      return setUnitTagMap((prev) => ({
        ...prev,
        ...map,
      }));
    }
    setUnitTagMap((prev) => ({
      ...prev,
      [dataUnit]: data?.[0]?.tagList?.map((tag) => ({
        ...tag,
        label: tag.name,
        value: tag.tagId,
      })),
    }));
  };
  // 根据belongUnitId将数组其分组并转化为树形结构
  const getTagAndFieldsOptions = (unitHasTagList, fieldsLists: FormField[], tag) => {
    // const groupObj = tagList.reduce((acc, cur) => {
    //   const { belongDataUnitId } = cur;
    //   if (!acc[belongDataUnitId]) {
    //     acc[belongDataUnitId] = [];
    //   }
    //   acc[belongDataUnitId].push(cur);
    //   return acc;
    // }, {});
    // console.log(unitHasTagList, 'unitHasTagList-----------')
    const tagAndFieldsTreeData = cloneDeep(unitHasTagList)?.map((unitItem) => {
      // console.log(unitItem, 'unitItem------------')
      const handleList = unitItem?.tagList?.map((tag) => ({
        ...tag,
        title: tag?.name,
        value: tag?.tagId,
      }));
      unitItem['children'] = handleList;
      return {
        ...unitItem,
        title: unitItem.name,
        value: unitItem.dataUnitId,
        disabled: true,
        children: handleList,
      };
    });
    // console.log(tagAndFieldsTreeData, 'tagAndFieldsTreeData------1')
    // 如果有字段
    if (fieldsLists?.length > 0 && tag?.tagMetaDataConfig?.type === MetaDataType.Number) {
      tagAndFieldsTreeData.push({
        title: '字段',
        value: uniqueId(),
        disabled: true,
        children: fieldsLists.map((field) => {
          return {
            ...field,
            title: field.fieldConfig?.fieldName,
            value: field.fieldConfig?.fieldId,
          };
        }),
      });
    }
    // console.log(tagAndFieldsTreeData, 'tagAndFieldsTreeData------')
    return tagAndFieldsTreeData;
  };

  const groupTagAndFields = useMemo(() => {
    // console.log(dataUnitsOps, 'dataUnitsOps-------------1')
    // return getTagAndFieldsOptions(basicDataUnitOps, cFiellds);
  }, [basicDataUnitOps]);
  // console.log(groupTagAndFields, 'groupTagAndFields------------')
  // 根据标签获取其类型
  const getDetailType = (tag) => {
    const { type } = tag?.tagMetaDataConfig || {};
    if ([DATA_TYPE.NUMBER.val, DATA_TYPE.DATETIME.val, DATA_TYPE.CODE.val, DATA_TYPE.UNIT.val, DATA_TYPE.PICTURE.val].includes(type)) {
      return type;
    } else if (type === DATA_TYPE.TEXT.val) {
      const { metaDataTextDTO } = tag?.tagMetaDataConfig || {};
      return metaDataTextDTO?.inputType === 'MANUAL' ? DATA_TYPE.TEXT.val : DATA_TYPE.TEXTENUM.val;
    } else if (type === DATA_TYPE.OBJECT.val) {
      const { metaDataObjectDTO } = tag?.tagMetaDataConfig || {};
      const { generalType } = metaDataObjectDTO || {};
      let tagType = DATA_TYPE.OBJECT.val;
      switch (generalType) {
        case MetaUnitGeneralType.Company:
          tagType = DATA_TYPE.ORG.val;
          break;
        case MetaUnitGeneralType.Department:
          tagType = DATA_TYPE.DEPT.val;
          break;
        case MetaUnitGeneralType.Post:
          tagType = DATA_TYPE.POST.val;
          break;
        case MetaUnitGeneralType.User:
          tagType = DATA_TYPE.USER.val;
          break;
        default:
          tagType = DATA_TYPE.OBJECT.val;
      }
      return tagType;
    } else {
      return type;
    }
  };
  const getTreeOptions = (data: any) => {
    let treeOptions = [];
    treeOptions = data?.map((ops: any) => {
      return {
        title: ops.item,
        value: ops.id,
        label: ops.item,
        children: ops.subEnumList && getTreeOptions(ops.subEnumList),
      };
    });
    return treeOptions;
  };
  // 根据类型渲染不容的组件
  const getFieldControls = (tag) => {
    if (!tag) {
      return null;
    }
    const { type } = tag?.tagMetaDataConfig || {};
    const field = createFormField(tag);
    switch (type) {
      case MetaDataType.Text: {
        return <TextMeta tag={tag} field={field} />;
      }
      case MetaDataType.Number: {
        return <NumberMeta tag={tag} field={field} />;
      }
      case MetaDataType.DateTime: {
        return <TimeMeta tag={tag} field={field} />;
      }
      case MetaDataType.Object: {
        return <ObjectMeta tag={tag} field={field} />;
      }
      case MetaDataType.Unit: {
        return <UnitMeta tag={tag} field={field} />;
      }
      case MetaDataType.Code: {
        return <CodeMeta tag={tag} field={field} />;
      }
      case MetaDataType.File: {
        return <FileMeta tag={tag} field={field} />;
      }
      case MetaDataType.Address: {
        return (
          <AddressMeta
            tag={tag}
            field={field}
            propsStyles={{
              minWidth: '125px',
            }}
          />
        );
      }
      default: {
        return null;
      }
    }
  };

  const customtagCheck = (type, tag, targetTag) => {
    switch (type) {
      case MetaDataType.Unit: {
        const { type: checkTagType } = tag.tagMetaDataConfig?.metaDataUnitDTO || {};
        const { type: TargetTagType } = targetTag.tagMetaDataConfig?.metaDataUnitDTO || {};
        return checkTagType === TargetTagType;
      }
      default: {
        return true;
      }
    }
    // return false;
  };

  const checkTagTypeEqual = (tag, targetTag) => {
    const curTagType = getDetailType(tag);
    const targetType = getDetailType(targetTag);
    const isEqualType = curTagType === targetType;
    let customCheck = true;
    if (curTagType === targetType && targetType === MetaDataType.Unit) {
      customCheck = customtagCheck(targetType, tag, targetTag);
    }
    return isEqualType && customCheck;
  };

  const getTreeTagOps = (groupTagAndFields, targetTag) => {
    // const tagAndFieldsTreeData
    // const unitHasTags = getCanuseRelateTags()
    // console.log(groupTagAndFields, 'groupTagAndFields------------')
    const { type, tagId } = targetTag || {};
    const resluts = cloneDeep(groupTagAndFields || [])?.filter((unit) => {
      if (unit?.title === '字段') {
        return true;
      } else {
        let flag = false;
        // console.log(unit, 'children')
        unit.children = unit.children?.filter((tag) => checkTagTypeEqual(tag, targetTag) && tagId !== tag.tagId);
        // console.log(unit.children, 'children')
        if (unit?.children?.length > 0) {
          flag = true;
        }
        return flag;
      }
    });
    // console.log(resluts, 'results----------')
    return resluts;
  };
  const getFieldValue = (prefix, settingOps, totalFix) => {
    const { operateType, tag } = settingOps;
    // console.log('tag---------', tag);
    const { tagMetaDataConfig, tagId } = tag || {};
    const { type } = tagMetaDataConfig || {};
    const getCanuseRelateTags = (tags, tag) => {
      const { tagMetaDataConfig, tagId } = tag || {};
      const { type } = tagMetaDataConfig || {};
      return (tags || [])
        .filter((tag) => tag?.tagMetaDataConfig?.type === type && tagId !== tag.tagId)
        .map((tag) => ({
          ...tag,
          label: tag.name,
          value: tag.tagId,
        }));
    };

    const treeTagOps = getTreeTagOps(getTagAndFieldsOptions(basicDataUnitOps, cFiellds, tag), tag);
    if (operateType === OperateTypesEnum.SYNC) {
      return (
        <>
          <Form.Item name={[...prefix, 'sourceObjectId']} label="当前标签/字段">
            <TreeSelect
              placeholder={'请选择数据源'}
              treeData={treeTagOps}
              popupMatchSelectWidth={false}
              treeDefaultExpandAll
              onChange={(value) => {
                // 如果是字段需手动赋值sourceFieldId
                // const isTag = ;
                let chooseItem = {} as any;
                treeTagOps.forEach((parentItem) => {
                  if (parentItem.children.length > 0) {
                    parentItem.children.forEach((childItem) => {
                      if (childItem.value === value) {
                        // setIsTag(childItem.isTag);
                        chooseItem = childItem;
                      }
                    });
                  }
                });
                const isTag = chooseItem?.dataUnitId;
                // form.setFieldValue([...totalFix, 'targetValue'], undefined);
                if (isTag) {
                  form.setFieldValue([...totalFix, 'sourceDataUnitId'], chooseItem.dataUnitId);
                  form.setFieldValue([...totalFix, 'sourceTagId'], chooseItem.tagId);
                  form.setFieldValue([...totalFix, 'sourceFieldId'], undefined);
                  form.setFieldValue([...totalFix, 'tagSyncType'], tagSyncTypeEnum.TAG);
                } else {
                  form.setFieldValue([...totalFix, 'sourceFieldId'], chooseItem.sourceId);
                  form.setFieldValue([...totalFix, 'tagSyncType'], tagSyncTypeEnum.FIELD);
                  form.setFieldValue([...totalFix, 'sourceDataUnitId'], undefined);
                  form.setFieldValue([...totalFix, 'sourceTagId'], undefined);
                }
                // 如果是标签需手动赋值sourceDataUnitId， sourceTagId
              }}
            />
          </Form.Item>
          <Form.Item name={[...prefix, 'sourceDataUnitId']} hidden />
          <Form.Item name={[...prefix, 'sourceTagId']} hidden />
          <Form.Item name={[...prefix, 'sourceFieldId']} hidden />
          <Form.Item name={[...prefix, 'sourceFieldId']} hidden />
        </>
      );
    } else if (operateType === OperateTypesEnum.SET) {
      const type = getDetailType(tag);
      return (
        <>
          <Form.Item name={[...prefix, 'targetValue']} label="目标标签值">
            {getFieldControls(tag)}
          </Form.Item>
        </>
      );
    }
    return (
      <Form.Item name={[...prefix, 'targetValue']} label="目标标签值">
        <Select options={[]} placeholder="请选择" />
      </Form.Item>
    );
  };

  return (
    <>
      <Modal
        title="数据同步"
        open
        onCancel={onCancel}
        onOk={async () => {
          // console.log(form.getFieldsValue())
          const values = await form.getFieldsValue();
          onOk?.(values);
        }}
        size="middle"
        canDrag
      >
        <Form form={form}>
          <Form.List
            name="dataSyncConfigs"
            initialValue={[
              {
                tagSyncs: [{}],
              },
            ]}
          >
            {(sourceGroups, { add: addSync, remove }) => (
              <>
                {sourceGroups.map((syncItem, syncIndex) => (
                  <div className={styles.syncItem} key={syncIndex}>
                    <div className={styles.syncWrap}>
                      <div className={styles.groupItemTop}>
                        <div className={styles.removeBtn}>
                          <div>
                            {/* <span>{`数据同步${syncIndex + 1}`}</span> */}
                            <Form.Item name={[syncItem.name, 'dataSyncName']} noStyle>
                              <EditGroupName defaultName={`数据同步${syncIndex + 1}`} />
                            </Form.Item>
                          </div>
                          {sourceGroups.length > 1 && <DeleteOutlined onClick={() => remove(syncIndex)} />}
                        </div>
                        <Row gutter={24} style={{ width: '100%' }}>
                          <Col span={16}>
                            <Form.Item label="目标数据单元" name={[syncItem.name, 'targetDataUnitId']}>
                              <Select
                                placeholder="请选择数据源"
                                options={permissionDataUnits}
                                onChange={(value) => {
                                  // 需要清空数据并重新获取数据单元
                                  const curTagSyncs = form.getFieldValue(['dataSyncConfigs', syncItem.name, 'tagSyncs']);
                                  const setTagSync = curTagSyncs?.map((curSync) => ({
                                    operateType: undefined,
                                    tagSyncType: undefined,
                                    sourceDataUnitId: undefined,
                                    sourceObjectId: undefined,
                                    sourceTagId: undefined,
                                    sourceFieldId: undefined,
                                    targetValue: undefined,
                                    targetTagId: undefined,
                                  }));
                                  form.setFieldValue(['dataSyncConfigs', syncItem.name, 'tagSyncs'], setTagSync);
                                  if (value) {
                                    getDataUnitToTags(value);
                                  }
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={8}>
                            <Form.Item name={[syncItem.name, 'sourceConditionGroups']} hidden />
                            <Form.Item label="触发条件" shouldUpdate>
                              {() => {
                                const sourceConditionGroups = form.getFieldValue(['dataSyncConfigs', syncItem.name, 'sourceConditionGroups']);
                                return (
                                  <Button
                                    type="link"
                                    icon={<EditOutlined />}
                                    onClick={() => {
                                      setFilterOpen({
                                        open: true,
                                        data: {
                                          detail: form.getFieldValue(['dataSyncConfigs', syncItem.name, 'sourceConditionGroups']),
                                          totalFix: ['dataSyncConfigs', syncItem.name, 'sourceConditionGroups'],
                                        },
                                      });
                                    }}
                                  >
                                    {sourceConditionGroups?.length > 0 ? `${sourceConditionGroups.length}个条件` : '配置条件'}
                                  </Button>
                                );
                              }}
                            </Form.Item>
                          </Col>
                        </Row>
                      </div>
                      <Divider style={{ margin: 0, marginBottom: 10 }} />
                      <div className={styles.groupContent}>
                        <Form.Item shouldUpdate>
                          {() => {
                            return (
                              <Form.List name={[syncItem.name, 'tagSyncs']}>
                                {(syncMaps, { add: addSyncMap, remove: removeSyncMap }) => {
                                  const dataUnitId = form.getFieldValue(['dataSyncConfigs', syncItem.name, 'targetDataUnitId']);
                                  console.log('dataUnitId', dataUnitId);
                                  const targetTagOps = dataUnitId ? unitTagMap?.[dataUnitId] || [] : [];
                                  console.log('targetTagOps', targetTagOps, unitTagMap);
                                  const _tagSyncs = form.getFieldValue(['dataSyncConfigs', syncItem.name, 'tagSyncs']);
                                  return syncMaps.map((syncMap, syncMapIndex) => {
                                    return (
                                      <>
                                        <Row gutter={24} style={{ width: '100%', position: 'relative' }} key={syncMapIndex}>
                                          <Col
                                            span={16}
                                            style={{
                                              display: 'flex',
                                              gap: '10px',
                                            }}
                                          >
                                            <Form.Item label="操作类型" name={[syncMap.name, 'operateType']} style={{ flex: 1 }}>
                                              <Select
                                                placeholder="请选择"
                                                options={[
                                                  {
                                                    label: '同步数据',
                                                    value: OperateTypesEnum.SYNC,
                                                  },
                                                  {
                                                    label: '设置数据',
                                                    value: OperateTypesEnum.SET,
                                                  },
                                                ]}
                                                onChange={() => {
                                                  // 需将值清空
                                                  const prefix = ['dataSyncConfigs', syncItem.name, 'tagSyncs', syncMap.name];
                                                  form.setFieldValue([...prefix, 'sourceObjectId'], undefined);
                                                  form.setFieldValue([...prefix, 'sourceDataUnitId'], undefined);
                                                  form.setFieldValue([...prefix, 'sourceTagId'], undefined);
                                                  form.setFieldValue([...prefix, 'sourceFieldId'], undefined);
                                                  form.setFieldValue([...prefix, 'targetValue'], undefined);
                                                  form.setFieldValue([...prefix, 'tagSyncType'], undefined);
                                                }}
                                              ></Select>
                                            </Form.Item>
                                            <Form.Item label="目标标签" name={[syncMap.name, 'targetTagId']} style={{ width: '50%' }}>
                                              <Select
                                                placeholder="请选择"
                                                options={targetTagOps
                                                  ?.filter((tag) => ![MetaTagType.System, MetaTagType.Special].includes(tag.type))
                                                  ?.map((t) => ({
                                                    ...t,
                                                    disabled: _tagSyncs?.some((item) => item?.targetTagId === t.value),
                                                  }))}
                                                onChange={() => {
                                                  // 需将值清空
                                                  const prefix = ['dataSyncConfigs', syncItem.name, 'tagSyncs', syncMap.name];
                                                  form.setFieldValue([...prefix, 'sourceObjectId'], undefined);
                                                  form.setFieldValue([...prefix, 'sourceDataUnitId'], undefined);
                                                  form.setFieldValue([...prefix, 'sourceTagId'], undefined);
                                                  form.setFieldValue([...prefix, 'sourceFieldId'], undefined);
                                                  form.setFieldValue([...prefix, 'targetValue'], undefined);
                                                  form.setFieldValue([...prefix, 'tagSyncType'], undefined);
                                                }}
                                              ></Select>
                                            </Form.Item>
                                            <Form.Item name={[syncMap.name, 'targetDataUnitId']} hidden />
                                          </Col>
                                          <Col span={8}>
                                            <Form.Item shouldUpdate noStyle>
                                              {() => {
                                                const curTagId = form.getFieldValue([
                                                  'dataSyncConfigs',
                                                  syncItem.name,
                                                  'tagSyncs',
                                                  syncMap.name,
                                                  'targetTagId',
                                                ]);
                                                const curTag = targetTagOps.find((tag) => tag.tagId === curTagId);
                                                const operateType = form.getFieldValue([
                                                  'dataSyncConfigs',
                                                  syncItem.name,
                                                  'tagSyncs',
                                                  syncMap.name,
                                                  'operateType',
                                                ]);
                                                return getFieldValue(
                                                  [syncMap.name],
                                                  {
                                                    tag: curTag,
                                                    operateType,
                                                  },
                                                  ['dataSyncConfigs', syncItem.name, 'tagSyncs', syncMap.name],
                                                );
                                              }}
                                            </Form.Item>
                                          </Col>
                                          {syncMapIndex >= 1 && (
                                            <CloseCircleFilled
                                              onClick={() => {
                                                removeSyncMap(syncMapIndex);
                                              }}
                                              style={{
                                                position: 'absolute',
                                                top: '10px',
                                                right: '-10px',
                                                color: '#ccc',
                                              }}
                                            />
                                          )}
                                        </Row>
                                        {syncMapIndex === syncMaps.length - 1 && (
                                          <Button
                                            type="link"
                                            icon={<PlusCircleOutlined />}
                                            onClick={() => addSyncMap()}
                                            style={{
                                              color: '#4881fa',
                                              marginLeft: '-15px',
                                              marginTop: '-10px',
                                            }}
                                          >
                                            新增规则
                                          </Button>
                                        )}
                                      </>
                                    );
                                  });
                                }}
                              </Form.List>
                            );
                          }}
                        </Form.Item>
                        {/* </Col> */}
                        {/* <Col span={8}></Col> */}
                      </div>
                    </div>
                  </div>
                ))}
                <div className={styles.syncAdd}>
                  <Button
                    icon={<PlusCircleOutlined />}
                    type="link"
                    onClick={() => {
                      addSync({
                        tagSyncs: [{}],
                      });
                    }}
                  >
                    添加同步数据单元
                  </Button>
                </div>
              </>
            )}
          </Form.List>
        </Form>
      </Modal>
      {filterOpen.open && (
        <Modal
          title="配置条件"
          open={filterOpen.open}
          size="middle"
          onCancel={() => {
            setFilterOpen({
              open: false,
              data: null,
            });
          }}
          onOk={() => {
            const values = filterForm.getFieldsValue();
            form.setFieldValue([...filterOpen.data.totalFix], values?.conditionGroups);
            setFilterOpen({
              open: false,
              data: null,
            });
          }}
        >
          <Form form={filterForm}>
            <FilterGroup
              tags={canUsedAllTags}
              canRelateTags={canUsedAllTags}
              conditionGroups={filterOpen.data?.detail}
              useInStage={UseInStageEnum.DataSync}
            />
          </Form>
        </Modal>
      )}
    </>
  );
};

export default DataSyncSetting;
