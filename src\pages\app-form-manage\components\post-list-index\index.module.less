.wrapper {
  flex: 1;
  overflow: auto;

  :global {
    .ant-collapse > .ant-collapse-item > .ant-collapse-header {
      padding: 6px 4px;
      margin: 0 12px;
    }

    .ant-collapse > .ant-collapse-item.selectedHeader > .ant-collapse-header {
      color: #4d7bf6;
      font-weight: bold !important;
      border-radius: 4px;
      background: #e8f3ff;
    }

    .ant-collapse > .ant-collapse-item > .ant-collapse-header > .ant-collapse-expand-icon > .ant-collapse-arrow {
      margin-right: 6px;
    }

    .ant-collapse-content > .ant-collapse-content-box {
      padding: 0;
    }

    .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
      padding-left: 0;
      padding-right: 0;
      padding-top: 0;
    }

    .ant-collapse-expand-icon {
      padding-inline-end: 3px !important;
    }
  }

  .canSelect {
    > div:first-child:hover,
    .topTitle:hover {
      color: #519bfd;
      cursor: pointer;
    }
  }

  .selected {
    color: #4d7bf6;
    font-weight: bold !important;
    border-radius: 4px;
    background: #e8f3ff;
  }
}
