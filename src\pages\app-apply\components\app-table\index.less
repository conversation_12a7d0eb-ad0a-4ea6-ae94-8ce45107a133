.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  flex-shrink: 0;
  height: 36px;
}

.content {
  flex-grow: 1;
  overflow: auto;
}

.containerHeader {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  // margin-bottom: 16px;
}
@media print {
  :has(.printElement) > :not(.printElement):not(:has(.printElement)) {
    display: none;
  }
}
