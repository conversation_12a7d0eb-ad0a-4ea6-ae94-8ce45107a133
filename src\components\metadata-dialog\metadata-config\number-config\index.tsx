import { Form, FormInstance } from '@gwy/components-web';
import { Rule } from 'antd/es/form';
import { useMemo } from 'react';
import { UseIn } from '../const';
import NumberRender from './number-render';

export const NumberDTOKey = 'metaDataNumberDTO';

type Props = {
  form?: FormInstance;
  getValidatorRules?: () => Rule[];
  useIn?: UseIn;
};

const NumberConfig = ({ form, getValidatorRules, useIn = UseIn.Meta }: Props) => {
  const rules = useMemo(() => (typeof getValidatorRules === 'function' ? getValidatorRules() : undefined), []);

  return (
    <div>
      <Form.Item label="配置数值范围" required style={{ marginBottom: 0 }}>
        <NumberRender form={form} prefix={[NumberDTOKey]} rules={rules} useIn={useIn} />
      </Form.Item>
    </div>
  );
};

export default NumberConfig;
