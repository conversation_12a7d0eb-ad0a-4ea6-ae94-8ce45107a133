.cardWrapper {
  animation-name: opacitys;
  animation-duration: 0.24s;
  animation-iteration-count: 1;

  .ellipsis {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
  }
  @keyframes opacitys {
    0% {
      transform: scale(0.985);
      opacity: 0.5;
    }

    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  width: 100%;
  height: 100%;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  transition: all 0.24s;

  &:hover {
    border-color: #4d7bf6;
    box-shadow: 0 2px 15px 2px rgba(0, 0, 0, 10%);
  }

  .headerBox {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;

    img {
      margin-right: 8px;
    }

    .headerInfo {
      width: 100%;
      overflow: hidden;

      .tooltip {
        .ellipsis();
      }

      .titleBox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;

        .title {
          margin-bottom: 0;
          flex: unset;
          width: unset;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .tag {
          flex-shrink: 0;
          margin-right: 0;
          margin-left: 6px;
          padding: 0 4px;
          line-height: 18px;
        }

        .tagBox {
          display: inline-flex;
          align-items: center;

          .tag {
            width: 34px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0;
            font-size: 12px;
            border-radius: 2px;
          }
        }
      }

      .dateInfo {
        font-size: 12px;
        font-weight: 400;
        color: #4e5969;
      }

      section {
        font-weight: bold;
        font-size: 14px;
        color: #1d2129;
        line-height: 20px;
        margin-bottom: 6px;
        .ellipsis();
      }

      input {
        margin-bottom: 2px;
      }

      div {
        font-weight: 400;
        font-size: 12px;
        color: #4e5969;
        line-height: 14px;
      }
    }
  }

  .contentBox {
    display: flex;
    width: 100%;
    padding: 9px 16px 12px;

    .infoBox {
      width: 100%;
      margin-bottom: 0;
      font-size: 12px;
      overflow: hidden;

      .tooltip {
        .ellipsis();

        font-weight: 400;
        color: #4e5969;
      }

      .authInfo {
        display: flex;
        align-items: center;
        line-height: 14px;

        .authBox {
          .ellipsis();

          font-weight: 400;
          color: #4e5969;
        }
      }
    }

    .footerBox {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .tagBox {
        display: flex;
        align-items: center;

        .tag {
          width: 52px;
          height: 22px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          border-radius: 2px;
        }
      }
    }
  }
}
