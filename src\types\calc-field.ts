export type CalcFieldVO = {
  /**
   * 计算方式 (1-公式计算, 2-阶梯计算)
   */
  calcType?: CalcType;
  /**
   * 阶梯计算
   */
  calculationCalcs?: any[];
  /**
   * 创建岗位id
   */
  createPostId?: string;
  /**
   * 创建岗位
   */
  createPostName?: string;
  /**
   * 创建人id
   */
  createUserId?: string;
  /**
   * 创建人名称
   */
  createUserName?: string;
  /**
   * 字段id
   */
  fieldId?: string;
  /**
   * 字段名称
   */
  fieldName?: string;
  /**
   * 字段类型 (1-表单字段, 2-独立字段)
   */
  fieldType?: FieldType;
  /**
   * 表单配置属性
   */
  formFieldConfig?: FormFieldConfig;
  /**
   * 公式计算
   */
  formulasCalcs?: any[];
  [property: string]: any;
};

/**
 * 表单配置属性
 *
 * FormFieldConfig
 */
export type FormFieldConfig = {
  /**
   * 小数位数
   */
  decimalNum?: number | null;
  /**
   * 字段别名
   */
  fieldOtherName?: null | string;
  /**
   * 是否百分比显示
   */
  ifPercent?: TriggerCondition;
  /**
   * 是否四舍五入
   */
  ifRound?: TriggerCondition;
  /**
   * 组件宽度
   */
  widgetWith?: null | string;
  [property: string]: any;
};

/**
 * 是否开启触发条件
 *
 * 是否百分比显示
 *
 * 是否四舍五入
 */
export enum TriggerCondition {
  No = 'NO',
  Yes = 'YES',
}

/**
 * 计算方式 (公式计算, 阶梯计算)
 */
export enum CalcType {
  FormulasCalc = 'FORMULAS_CALC', // 公式计算
  Calculation = 'CALCULATION', // 阶梯计算
}

/**
 * 字段类型 1-表单字段 2-独立字段
 */
export enum FieldType {
  FormField = 'FORM_FIELD',
  IndependentField = 'INDEPENDENT_FIELD',
}

/**
 * 公式计算表达式描述类型
 */
export enum ExpressionType {
  NUMBER = 'NUMBER', // 数值
  SYMBOL = 'SYMBOL', // 运算符
  TAG = 'TAG', // 标签
  FUNC = 'FUNC', // 函数
  FUNC_CALC = 'FUNC_CALC', // 计算公式(字段)
}

/**
 * 函数类型
 */
export enum FuncType {
  AVERAGE = 'AVERAGE', // 平均值
  MAX = 'MAX', // 最大值
  MIN = 'MIN', // 最小值
  ROUND = 'ROUND', // 四舍五入
  SUM = 'SUM', // 求和
  COUNT = 'COUNT', // 计数
  DISTINCT_COUNT = 'DISTINCT_COUNT', // 去重计数
  CEIL = 'CEIL', // 向上取整
  FLOOR = 'FLOOR', // 向下取整
}

/**
 * 阶梯计算-规则组-选择模式
 */
export enum ResultType {
  DIRECT_RESULT = 'DIRECT_RESULT', // 直接结果
  CALCULATION_MODE = 'CALCULATION_MODE', // 阶梯模式
}

/**
 * 阶梯计算-规则组-结果类型
 */
export enum ValueType {
  FIXED_VALUE = 'FIXED_VALUE', // 固定值
  CALC_FORMULAS = 'CALC_FORMULAS', // 计算公式
}
