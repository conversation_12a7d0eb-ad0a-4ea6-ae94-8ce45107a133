import { CalculatorOutlined, CloseCircleFilled } from '@ant-design/icons';
import { memo, useState } from 'react';
// import PreviewLibFields from '../../../preview-lib-fields';
import { CalcFieldVO } from '@/types/calc-field';
import { TextEllipsisTooltip } from '@gwy/components-web';
import classNames from 'classnames';
import styles from './index.less';

interface IProps {
  field?: CalcFieldVO;
  active?: boolean;
  canDelete?: boolean;
  onDelete?: (e: any) => void;
}

const FieldItem = memo<IProps>(({ field, active, canDelete, onDelete }) => {
  // 是否展示字段详情弹窗
  const [isPreview, setIsPreivew] = useState(false);

  const handleClick = () => {
    setIsPreivew(true);
  };

  return (
    <>
      <div className={styles.container}>
        <div
          className={classNames(styles.detail, {
            [styles.active]: active,
          })}
          onClick={handleClick}
        >
          <CalculatorOutlined style={{ marginRight: 10 }} />
          <div className={styles.fieldName}>
            <TextEllipsisTooltip text={field.fieldName} />
          </div>
        </div>
        {canDelete && <CloseCircleFilled className={styles.closeIcon} onClick={(e) => onDelete?.(e)} />}
      </div>

      {/* {isPreview && (
        <PreviewLibFields
          onCancel={() => setIsPreivew(false)}
          onOk={() => setIsPreivew(false)}
          fieldItem={field}
          dataRuleList={[]}
        />
      )} */}
    </>
  );
});

export default FieldItem;
