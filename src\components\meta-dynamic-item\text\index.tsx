import { TextInputType } from '@/const/metadata';
import { FormField, InputStyle } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { dispatchDynamicParamChange } from '@/utils/event-emitter';
import { InputProps, Select } from '@gwy/components-web';
import { memo, useCallback } from 'react';
import { UseIn } from '../const';
import { WidgetType } from './const';
import TextEnumMeta from './enum';
import TextManualMeta from './manual';

const TextMeta = memo<
  {
    useIn?: UseIn;
    // 展示类型
    displayType?: WidgetType;
    tag?: DataUnitTagVO;
    field?: FormField;
    value?: any;
    onChange?: (value) => void;
    isPreview?: boolean;
    disabled?: boolean;
  } & Pick<InputProps, 'maxLength' | 'placeholder'>
>((props) => {
  const { useIn, tag, field, value, onChange, isPreview, disabled } = props;

  const { inputType: metaInputType } = tag.tagMetaDataConfig?.metaDataTextDTO || {};
  const { inputStyle, selectorTexts } = field?.config || {};

  const handleChange = useCallback(
    (value) => {
      typeof onChange === 'function' && onChange(value);
      dispatchDynamicParamChange({ dataUnitId: tag.dataUnitId, tagId: tag.tagId, value });
    },
    [onChange, tag],
  );
  const render = () => {
    if (metaInputType === TextInputType.enum) {
      return <TextEnumMeta useIn={useIn} tag={tag} field={field} value={value} onChange={handleChange} isPreview={isPreview} disabled={disabled} />;
    }
    if (metaInputType === TextInputType.manual) {
      if (inputStyle === InputStyle.Selector) {
        return (
          <Select
            disabled={disabled}
            options={(selectorTexts as any)?.map((ite) => ({ value: ite, label: ite }))}
            value={value}
            onChange={handleChange}
          />
        );
      }
      return <TextManualMeta useIn={useIn} tag={tag} field={field} value={value} onChange={handleChange} isPreview={isPreview} disabled={disabled} />;
    }
  };

  return render();
});

export default TextMeta;
