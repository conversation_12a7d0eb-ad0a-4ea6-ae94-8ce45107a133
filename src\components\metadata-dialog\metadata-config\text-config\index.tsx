import { EnumType, enumTypeOptions, TextCharType, textCharTypeOptions, TextInputType, textInputTypeOptions } from '@/const/metadata';
import { getDictList } from '@/services/datasource';
import { SettingOutlined } from '@ant-design/icons';
import { Checkbox, Form, FormInstance, InputNumber, Radio, Select, Space } from '@gwy/components-web';
import { isEmpty } from 'lodash-es';
import { useContext, useEffect, useState } from 'react';
import { MetaDataContext } from '../../context';
import EnumLibraryManage from './enum-library-manage';
import styles from './index.less';
import TreeByList, { OPERATE_TYPE } from './tree-by-list';

export const TextDTOKey = 'metaDataTextDTO';

type Props = {
  metaDataConfig?: any;
  form?: FormInstance;
};

const TextConfig = ({ form, metaDataConfig }: Props) => {
  const { disabled, hasApproved } = useContext(MetaDataContext);

  const [enumLibraryList, setEnumLibraryList] = useState([]);
  const [enumLibraryManageDialog, setEnumLibraryManageDialog] = useState<{
    open?: boolean;
  }>({});

  const inputType = Form.useWatch([TextDTOKey, 'inputType'], form);
  const enumType = Form.useWatch([TextDTOKey, 'enumType'], form);

  const fetchEnumLibraryList = async () => {
    const data = await getDictList({
      code: !isEmpty(metaDataConfig) ? metaDataConfig[TextDTOKey]?.dictCode : null,
    });
    setEnumLibraryList(
      data.map((item) => ({
        label: item.name,
        value: item.code,
        key: item.code,
      })),
    );
  };
  useEffect(() => {
    fetchEnumLibraryList();
  }, []);

  useEffect(() => {
    if (!enumType && !isEmpty(metaDataConfig)) {
      form.setFieldValue([TextDTOKey, 'enumType'], EnumType.custom);
    }
  }, [enumType]);

  return (
    <div>
      <Form.Item
        label="输入方式"
        name={[TextDTOKey, 'inputType']}
        rules={[{ required: true, message: '请选择' }]}
        initialValue={TextInputType.manual}
      >
        <Radio.Group disabled={hasApproved || disabled} options={textInputTypeOptions} />
      </Form.Item>

      {inputType === TextInputType.manual && (
        <div>
          <Form.Item
            label="文本包含"
            name={[TextDTOKey, 'allowedCharacters']}
            rules={[{ required: true, message: '请选择' }]}
            initialValue={[TextCharType.chinese, TextCharType.english_lower, TextCharType.english_upper, TextCharType.number, TextCharType.symbol]}
          >
            <Checkbox.Group options={textCharTypeOptions} />
          </Form.Item>
          <Form.Item label="配置文本长度" required>
            <Space>
              <Form.Item
                name={[TextDTOKey, 'maxLength']}
                rules={[{ required: true, message: '请输入' }]}
                initialValue={2000}
                help="输入不可超过2000字数限制"
              >
                <InputNumber max={2000} />
              </Form.Item>
            </Space>
          </Form.Item>
        </div>
      )}

      {inputType === TextInputType.enum && (
        <Form.Item label="枚举类型" name={[TextDTOKey, 'enumType']} rules={[{ required: true, message: '请选择' }]} initialValue={EnumType.general}>
          <Radio.Group disabled={hasApproved || disabled}>
            {enumTypeOptions.map((option) => (
              <Radio key={option.value} value={option.value}>
                {option.label}&nbsp;&nbsp;
                {option.value === EnumType.general && !hasApproved && !disabled && (
                  <span
                    style={{ color: '#1677ff' }}
                    onClick={(e) => {
                      setEnumLibraryManageDialog({ open: true });
                    }}
                  >
                    <SettingOutlined />
                    &nbsp;管理
                  </span>
                )}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
      )}

      {enumType === EnumType.general && (
        <Form.Item name={[TextDTOKey, 'dictCode']} rules={[{ required: true, message: '请选择' }]}>
          <Select options={enumLibraryList} placeholder="请选择" disabled={hasApproved || disabled} getPopupContainer={() => document.body} />
        </Form.Item>
      )}

      {enumType === EnumType.custom && (
        <div>
          <Form.Item
            label="枚举选择"
            name={[TextDTOKey, 'enumList']}
            required
            rules={[
              {
                validator: (_, value) => {
                  if (value && value.length) {
                    if (Array.isArray(value) && value.some((item) => !item.item)) {
                      return Promise.reject(new Error('请设置当前配置'));
                    }
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('请设置当前配置'));
                },
              },
            ]}
            className={styles.enmuItemContainer}
          >
            <TreeByList disabled={disabled} canAdd={!disabled} tagOprateType={OPERATE_TYPE.ADD} mode={undefined} style={{ marginLeft: -24 }} />
          </Form.Item>
        </div>
      )}

      {enumLibraryManageDialog.open && (
        <EnumLibraryManage
          open
          onClose={() => {
            setEnumLibraryManageDialog({ open: false });
            fetchEnumLibraryList();
          }}
        />
      )}
    </div>
  );
};

export default TextConfig;
