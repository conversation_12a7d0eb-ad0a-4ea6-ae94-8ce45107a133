.sortTagsWrapper {
  margin-top: 12px;

  .sortTagsItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    column-gap: 15px;
    width: 100%;

    .left {
      display: flex;
      align-items: center;
      column-gap: 24px;
    }

    .right {
      display: flex;

      .deleteBtn {
        span {
          color: #c9cdd4;
          cursor: pointer;
        }

        &:hover {
          span {
            color: #aaa;
          }
        }
      }
    }
  }

  .addBtn {
    display: inline-flex;
    align-items: center;
    justify-content: flex-start;
    column-gap: 6px;
    cursor: pointer;
    color: #1890ff;
    font-weight: 400;
  }
}
