import { TextInputType } from '@/const/metadata';
import { FormField } from '@/types/form-field';
import { DataUnitTagVO } from '@/types/tag';
import { getMetaTagUniqueId } from '@/utils/metadata';
import { Form } from '@gwy/components-web';
import React, { memo, useMemo } from 'react';
import { UseIn } from '../const';
import TextMeta from './index';
import { validator } from './validator';

const TextMetaForm = memo<{
  useIn?: UseIn;
  tag?: DataUnitTagVO;
  field?: FormField;
  label?: React.ReactNode;
  prefix?: any[];
  isPreview?: boolean;
}>(({ useIn, tag, field, label, prefix = [], isPreview }) => {
  const { inputType: metaInputType } = tag.tagMetaDataConfig?.metaDataTextDTO || {};
  const { readonly, required, config } = field || {};
  const { allowedCharacters, inputType } = config?.metaDataTextDTO || {};
  const rules = useMemo(
    () =>
      !readonly && [UseIn.App].includes(useIn)
        ? [
            {
              required: required,
              message: '请填写必填项',
            },
            metaInputType === TextInputType.manual &&
              (!inputType || inputType === TextInputType.manual) && {
                validator: (rule, value) => validator(rule, value, allowedCharacters),
              },
          ].filter(Boolean)
        : undefined,
    [metaInputType, required, inputType, readonly],
  );

  return (
    <>
      <Form.Item label={label} name={[...(prefix || []), getMetaTagUniqueId(field), 'value']} rules={rules} required={required && !readonly}>
        <TextMeta useIn={useIn} tag={tag} field={field} isPreview={isPreview} />
      </Form.Item>
    </>
  );
});

export default TextMetaForm;
