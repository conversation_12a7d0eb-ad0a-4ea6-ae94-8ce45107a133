import React, { useCallback, useState } from 'react';
import styles from './index.less';

export enum SortState {
  Initial = 0,
  Ascending = 1, // 升序 1 → 2 → 3	A → B → C	2023-01-01 → 2023-02-01
  Descending = 2, // 降序	3 → 2 → 1	C → B → A	2023-02-01 → 2023-01-01
}

type IProps = {
  onClick?: (state: SortState) => void;
};

const SortButton: React.FC<IProps> = ({ onClick }) => {
  const [sortState, setSortState] = useState<SortState>(SortState.Initial);

  const handleClick = useCallback(
    (event: React.MouseEvent<HTMLDivElement>, tempSortState: SortState) => {
      event.stopPropagation();
      let newState = tempSortState;
      if (tempSortState === sortState) {
        newState = 0;
      }
      setSortState(newState);
      onClick?.(newState);
    },
    [sortState, onClick],
  );

  return (
    <div className={styles.container} onClick={(event) => event.stopPropagation()} role="button" tabIndex={0}>
      <div
        onClick={(event) => handleClick(event, 1)}
        className={`${styles.triangleUp} ${
          sortState === SortState.Ascending ? styles.active : sortState === SortState.Initial ? styles.initial : ''
        }`}
      />
      <div
        onClick={(event) => handleClick(event, 2)}
        className={`${styles.triangleDown} ${
          sortState === SortState.Descending ? styles.active : sortState === SortState.Initial ? styles.initial : ''
        }`}
      />
    </div>
  );
};

export default SortButton;
