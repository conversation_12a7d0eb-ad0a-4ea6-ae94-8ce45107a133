import { message } from '@gwy/components-web';
import { PropertyConfigRef } from './property-config';

export const validateFormFields = async (propertyConfigRef: PropertyConfigRef) => {
  if (!propertyConfigRef) return Promise.resolve();
  try {
    await propertyConfigRef.validateFields();
    return Promise.resolve();
  } catch (e) {
    message.error('当前属性配置存在异常，请完善配置后再切换');
    return Promise.reject();
  }
};
