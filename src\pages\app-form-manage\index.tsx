import IconFormApprove from '@/assets/app-form-manage/icon-form-approve.png';
import IconFormExcute from '@/assets/app-form-manage/icon-form-execution.png';
import { useBridge } from '@/hooks';
import { formManageApi } from '@/services';
import { formatDateTime } from '@/utils/misc';
import { MenuFoldOutlined, SearchOutlined } from '@ant-design/icons';
import { Empty, Input, LayoutPage, Tooltip } from '@gwy/components-web';
import { Bridge } from '@gwy/libs-web';
import { isEmpty } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { openAppApply } from '../app-apply';
import ApplyList, { ApplyListRef } from './components/apply-list';
import PostListIndex from './components/post-list-index';
import styles from './index.less';

export const refreshAppFormManage = (bridge: Bridge) => {
  bridge.refresh('/datasource/app-form-manage');
};

const AppFormManage = () => {
  const appManageRef = useRef<ApplyListRef>(null);
  const postListRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [clickStatisticsList, setClickStatisticsList] = useState<any[]>([]);
  const [leftExpand, setLeftExpand] = useState<boolean>(true);
  const [baseInfo, setBaseInfo] = useState<{
    [key: string]: any;
  }>({});

  const bridge = useBridge({
    onRefresh: () => {
      appManageRef.current?.refreshAppList();
    },
  });

  const getAppClickStatistics = async () => {
    const data = await formManageApi.getRecentUsedForm({
      orgId: baseInfo?.selectedPost?.orgId,
    });
    setClickStatisticsList(data || []);
  };

  const updateRecentFormInfo = async (formId) => {
    await formManageApi.updateRecentUsedForm({
      formId,
      orgId: baseInfo?.selectedPost?.orgId,
    });
  };

  useEffect(() => {
    baseInfo?.selectedPost?.orgId && getAppClickStatistics();
  }, [baseInfo?.selectedPost?.orgId]);

  const [searchPostInput, setSearchPostInput] = useState('');
  const [searchInput, setSearchInput] = useState('');

  const handleOpenAppApply = async (data) => {
    const { formId } = data || {};
    updateRecentFormInfo(formId);
    openAppApply(bridge, {
      hideInMenu: true,
      state: {
        app: data,
        post: baseInfo.selectedPost,
      },
    });
  };
  return (
    <LayoutPage
      loading={loading}
      footer={false}
      header={{ title: '表单应用', onClose: () => bridge.close() }}
      contentClassName={styles.layoutContent}
    >
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <div className={styles.headerTitle}>
            <div className={styles.bdLeft}></div>
            最近使用
          </div>
          {isEmpty(clickStatisticsList) ? (
            <div
              style={{
                height: 'calc(100% - 70px)',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Empty description="暂无表单" />
            </div>
          ) : (
            <div className={styles.body}>
              {clickStatisticsList.map((click) => (
                <div
                  key={click.id}
                  className={styles.main}
                  onClick={() => {
                    handleOpenAppApply(click);
                  }}
                >
                  {/* <FileFilled style={{ color: '#86909c' }} /> */}
                  <img src={click?.appType === 'SUBMIT' ? IconFormExcute : IconFormApprove} width={25} height={28} />
                  <div className={styles.content}>
                    <span className={styles.name} title={click.name}>
                      {click.name}
                    </span>
                    <span className={styles.time}>{formatDateTime(click.lastUseTime)}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        {/* <Divider type="vertical" className={styles.dividerLine} />
        <div className={styles.headerRight}>
          <div className={styles.headerTitle}>快捷导航</div>
          <div className={styles.body}>
            <div
              className={styles.main}
              onClick={async () => {
                try {
                  const formVersionId = await createFormVersion({
                    postId: baseInfo?.selectedPost?.postId,
                    appType: AppType.SUBMIT,
                  });
                  openAppDesigner(bridge, {
                    hideInMenu: true,
                    state: {
                      formVersionId,
                      post: baseInfo?.selectedPost,
                    },
                  });
                } catch (error) {}
              }}
            >
              <img src={`${CDN_URL_DATASOURCE}/form-manage/factory.png`} />
              <span className={styles.btnLabel}>创建表单</span>
            </div>
            <div
              className={styles.main}
              onClick={() => {
                openAppFormPremission(bridge, {
                  hideInMenu: true,
                });
              }}
            >
              <img src={`${CDN_URL_DATASOURCE}/form-manage/auth.png`} />
              <span className={styles.btnLabel}>表单权限</span>
            </div>
            <Tooltip placement="top" title="功能暂未开发">
              <div className={styles.main}>
                <img src={`${CDN_URL_DATASOURCE}/form-manage/module.png`} />
                <span className={styles.btnLabel}>模板管理</span>
              </div>
            </Tooltip>
          </div>
        </div> */}
      </div>
      <div className={styles.container} id="app-form-manage">
        <div className={styles.mainWrapper}>
          {leftExpand ? (
            <div className={styles.leftWrapper}>
              <div className={styles.titleWrapper}>
                <Tooltip title="收起岗位列表" placement="right">
                  <MenuFoldOutlined onClick={() => setLeftExpand(false)} />
                </Tooltip>
                <Input
                  prefix={<SearchOutlined style={{ color: '#909296' }} />}
                  style={{ width: 240 }}
                  placeholder={`搜索岗位名称`}
                  allowClear
                  onBlur={(e) => {
                    setSearchPostInput(e.target.value);
                  }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      setSearchPostInput((e?.target as HTMLInputElement)?.value.trim());
                    }
                  }}
                />
              </div>
              <PostListIndex ref={postListRef} setBaseInfo={setBaseInfo} searchInput={searchPostInput} />
            </div>
          ) : (
            <div className={styles.leftWrapperFold}>
              <Tooltip title="展开岗位列表" placement="right">
                <MenuFoldOutlined className={styles.menuExpand} onClick={() => setLeftExpand(true)} />
              </Tooltip>
            </div>
          )}

          <div className={styles.rightWrapper}>
            <div className={styles.contentWrapper}>
              <ApplyList
                ref={appManageRef}
                getAppClickStatistics={getAppClickStatistics}
                loading={loading}
                setLoading={setLoading}
                baseInfo={baseInfo}
                setBaseInfo={setBaseInfo}
                bridge={bridge}
                setSearchInput={setSearchInput}
                searchInput={searchInput}
                openAppApply={handleOpenAppApply}
              />
            </div>
          </div>
        </div>
      </div>
    </LayoutPage>
  );
};

export default AppFormManage;
