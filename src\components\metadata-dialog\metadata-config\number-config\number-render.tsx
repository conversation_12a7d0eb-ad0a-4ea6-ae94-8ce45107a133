import { NumberRangeType, numberRangeTypeOptions } from '@/const/metadata';
import { Form, FormInstance, InputNumber, Select, Space } from '@gwy/components-web';
import { Rule } from 'antd/es/form';
import { UseIn } from '../const';

type Props = {
  form?: FormInstance;
  prefix: any;
  rules?: Rule[];
  useIn?: UseIn;
};

const NumberRender = ({ form, prefix, rules = [], useIn = UseIn.Meta }: Props) => {
  const rangeType = Form.useWatch([...prefix, 'rangeType'], form);

  const renderRange = () => {
    return (
      <>
        <Form.Item
          name={[...prefix, 'min']}
          initialValue={0}
          rules={[
            { required: true, message: '请输入' },
            {
              validator(rule, value) {
                const max = form.getFieldValue([...prefix, 'max']);
                if (typeof max === 'number' && typeof value === 'number' && value >= max) {
                  return Promise.reject('最小值必须小于最大值');
                }
                return Promise.resolve();
              },
            },
            ...rules,
          ]}
        >
          <InputNumber placeholder="请输入最小值" style={{ width: 100 }} />
        </Form.Item>
        <Form.Item>~</Form.Item>
        <Form.Item
          name={[...prefix, 'max']}
          initialValue={100}
          dependencies={[[...prefix, 'min']]}
          rules={[
            { required: true, message: '请输入' },
            {
              validator(rule, value) {
                const min = form.getFieldValue([...prefix, 'min']);
                if (typeof min === 'number' && typeof value === 'number' && value <= min) {
                  return Promise.reject('最大值必须大于最小值');
                }
                return Promise.resolve();
              },
            },
            ...rules,
          ]}
        >
          <InputNumber placeholder="请输入最大值" style={{ width: 100 }} />
        </Form.Item>
      </>
    );
  };
  return (
    <div>
      <Space align="start" style={{ alignItems: 'baseline' }}>
        <Form.Item name={[...prefix, 'rangeType']} initialValue={NumberRangeType.unlimited} rules={[{ required: true, message: '请选择' }]}>
          <Select
            style={{ width: 100 }}
            options={numberRangeTypeOptions}
            onChange={() => {
              form.setFieldValue([...prefix, 'min'], undefined);
              form.setFieldValue([...prefix, 'max'], undefined);
            }}
            placeholder="请选择"
          />
        </Form.Item>

        {[NumberRangeType.lt, NumberRangeType.lte].includes(rangeType) && (
          <Form.Item name={[...prefix, 'max']} rules={[{ required: true, message: '请输入' }, ...rules]}>
            <InputNumber placeholder="请输入最大值" style={{ width: 100 }} />
          </Form.Item>
        )}
        {[NumberRangeType.gt, NumberRangeType.gte].includes(rangeType) && (
          <Form.Item name={[...prefix, 'min']} rules={[{ required: true, message: '请输入' }, ...rules]}>
            <InputNumber placeholder="请输入最小值" style={{ width: 100 }} />
          </Form.Item>
        )}

        {useIn !== UseIn.Designer && [NumberRangeType.between, NumberRangeType.out].includes(rangeType) ? renderRange() : null}
      </Space>
      {useIn === UseIn.Designer && [NumberRangeType.between, NumberRangeType.out].includes(rangeType) ? (
        <Space align="start" style={{ alignItems: 'baseline' }}>
          {renderRange()}
        </Space>
      ) : null}
    </div>
  );
};

export default NumberRender;
