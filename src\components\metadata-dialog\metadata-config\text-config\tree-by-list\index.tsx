import { genUuid } from '@/utils';
import { DeleteOutlined, EditOutlined, MenuUnfoldOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Input, InputNumber, Tooltip, Tree } from 'antd';
import classNames from 'classnames';
import { isEmpty } from 'lodash-es';
import { CSSProperties, memo, useEffect, useState } from 'react';
import styles from './index.less';

// 操作类型
export const OPERATE_TYPE = {
  ADD: 0, // 新增
  EDIT: 1, // 编辑
  VIEW: 2, // 查看
  AUDIT: 3, // 审核
};

export enum ITypeMode {
  number = 'number',
}

interface IProps {
  disabled?: boolean;
  canAdd?: boolean;
  keysConfig?: {
    parentKey?: string;
    idKey?: string;
    childrenKey?: string;
    valueKey?: string;
    // 用于计算的字段key
    calKey?: string;
    // 用于描述的字段key
    descKey?: string;
  };
  value?: any[];
  onChange?: (value: any[]) => void;
  // 标签操作状态
  tagOprateType: number;
  // 选中的岗位
  selectedPost?: any;
  mode?: ITypeMode;
  style?: CSSProperties;
}

const TreeByList = memo<IProps>((props) => {
  const { disabled, canAdd, keysConfig, value, onChange, tagOprateType, selectedPost, mode, style } = props;
  const {
    parentKey = 'parentId',
    idKey = 'id',
    valueKey = 'item',
    calKey = 'value',
    descKey = 'description',
    childrenKey = 'subEnumList',
  } = keysConfig || {};
  const [treeData, setTreeData] = useState([]);

  const [expandKeys, setExpandKeys] = useState(null);

  useEffect(() => {
    if (Array.isArray(value) && value.length > 0) {
      const loop = (list) => {
        for (const item of list) {
          item.showTextArea = item.showTextArea || !!item[descKey];
          if (item.children) {
            loop(item.children);
          }
        }
        return list;
      };
      setTreeData(loop(value));
    } else {
      setTreeData([
        {
          [valueKey]: '',
          [descKey]: '',
          [idKey]: genUuid(8),
          [parentKey]: '',
          orgId: selectedPost?.orgId,
          postId: selectedPost?.id,
          [childrenKey]: [],
          // 临时字段，标识该条数据为新增，可以修改
          isNewData: true,
          showTextArea: false,
        },
      ]);
    }
  }, [value]);

  useEffect(() => {
    if (!expandKeys && value) {
      setExpandKeys(value.map((item) => item[idKey]).filter((k) => !!k));
    }
  }, [expandKeys, value]);

  const onReRender = () => {
    setTreeData([...treeData]);
    typeof onChange === 'function' && onChange(treeData);
  };

  const onValueChange = (e, node, key) => {
    const { value } = e.target;
    node[key] = value;
    onReRender();
  };

  const renderTreeNode = (parent, node, index, level = 0) => {
    return (
      <Tree.TreeNode
        key={node[idKey] || `${level}-${index}`}
        className={classNames(styles.treeNode, {
          [styles.topTreeNode]: level === 0 && index === 0,
        })}
        title={
          <div className={styles.treeNodeTitle}>
            <div className={styles.treeNodeTitleContent}>
              <div className={styles.treeNodeTitleName}>
                <Input
                  disabled={disabled}
                  placeholder={mode === ITypeMode.number ? '请输入标题' : '请输入选项值'}
                  defaultValue={node[valueKey]}
                  onBlur={(e) => onValueChange(e, node, valueKey)}
                  onPressEnter={(e) => onValueChange(e, node, valueKey)}
                  style={{ flex: 1 }}
                  maxLength={20}
                  showCount
                />
                {mode === ITypeMode.number && (
                  <InputNumber
                    defaultValue={node[calKey]}
                    placeholder="请输入数值"
                    disabled={disabled}
                    onBlur={(e) => onValueChange(e, node, calKey)}
                    onPressEnter={(e) => onValueChange(e, node, calKey)}
                    style={{ flex: 1 }}
                  />
                )}
              </div>
              {node.showTextArea && (
                <Input.TextArea
                  disabled={disabled}
                  autoSize={{ minRows: 1, maxRows: 3 }}
                  defaultValue={node[descKey]}
                  placeholder={`${mode === ITypeMode.number ? '请输入标题' : '请输入选项值'}的描述提示信息（非必填，最多200字）`}
                  onBlur={(e) => onValueChange(e, node, descKey)}
                  onPressEnter={(e) => onValueChange(e, node, descKey)}
                  maxLength={200}
                  className={styles.treeNodeTitleNameDesc}
                />
              )}
            </div>
            <div className={styles.treeNodeOperate}>
              {[OPERATE_TYPE.ADD, OPERATE_TYPE.EDIT].includes(tagOprateType) && (!disabled || canAdd) && (
                <>
                  <Tooltip title="添加选项">
                    <PlusCircleOutlined
                      onClick={() => {
                        const list = level === 0 ? parent : parent[childrenKey];
                        list.push({
                          [valueKey]: '',
                          [childrenKey]: [],
                          [idKey]: genUuid(8),
                          [parentKey]: parent[idKey] || '',
                          orgId: selectedPost?.orgId,
                          postId: selectedPost?.id,
                          // 临时字段，标识该条数据为新增，可以修改
                          isNewData: true,
                          showTextArea: false,
                        });
                        setTreeData([...treeData]);
                      }}
                    />
                  </Tooltip>
                  {mode !== ITypeMode.number && (
                    <Tooltip title="添加子选项">
                      <MenuUnfoldOutlined
                        onClick={() => {
                          node[childrenKey] = node[childrenKey] || [];
                          node[childrenKey].push({
                            [valueKey]: '',
                            [childrenKey]: [],
                            [idKey]: genUuid(8),
                            [parentKey]: node[idKey] || '',
                            orgId: selectedPost?.orgId,
                            postId: selectedPost?.id,
                            // 临时字段，标识该条数据为新增，可以修改
                            isNewData: true,
                            showTextArea: false,
                          });
                          setExpandKeys((pre) => {
                            if (pre && pre.includes(node[idKey])) {
                              return pre;
                            }
                            return [...(pre || []), node[idKey]];
                          });
                          setTreeData([...treeData]);
                        }}
                      />
                    </Tooltip>
                  )}
                  {isEmpty(keysConfig) && (
                    <Tooltip title={node.showTextArea ? '删除描述' : '添加描述'}>
                      <EditOutlined
                        style={{ color: node.showTextArea ? '#C9CDD4' : '#4E5969 ' }}
                        onClick={() => {
                          if (node.showTextArea) node[descKey] = '';
                          node['showTextArea'] = !node.showTextArea;
                          onReRender();
                        }}
                      />
                    </Tooltip>
                  )}
                </>
              )}
              {(((level > 0 || parent.length > 1) && (!disabled || (canAdd && node.isNewData)) && isEmpty(keysConfig)) ||
                (!isEmpty(keysConfig) && node.isNewData)) && (
                <DeleteOutlined
                  onClick={() => {
                    const list = level === 0 ? parent : parent[childrenKey];
                    list.splice(
                      list.findIndex((item) => item[idKey] === node[idKey]),
                      1,
                    );
                    onReRender();
                  }}
                />
              )}
            </div>
          </div>
        }
      >
        {Array.isArray(node[childrenKey]) && node[childrenKey].map((child, idx) => renderTreeNode(node, child, idx, level + 1))}
      </Tree.TreeNode>
    );
  };

  return (
    <div style={style}>
      <Tree
        selectable={false}
        expandedKeys={expandKeys}
        onExpand={(keys) => {
          setExpandKeys(keys);
        }}
        className={styles.tree}
      >
        {treeData.map((node, index) => renderTreeNode(treeData, node, index, 0))}
      </Tree>
    </div>
  );
});

export default TreeByList;
