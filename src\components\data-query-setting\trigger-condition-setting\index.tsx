// 阶梯计算触发条件配置弹窗
import { FieldType } from '@/types/calc-field';
import { DataUnitBaseVO } from '@/types/data-unit';
import { getBeloneDataUnitTags } from '@/utils/calc-field/func';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Form, Modal, Radio, Select, Switch, Tooltip } from '@gwy/components-web';
import { useEffect, useMemo, useState } from 'react';
import { getExtConfig } from '../../data-query-setting';
import FilterGroup from '../../filter-group';
import { DATA_QUERY_TYPE, SAME_GROUP_QUERY_TYPE } from '../../filter-group/filter-const';
import GroupFilter from '../../group-filter';
import SortTags from '../../sort-tags';
import styles from '../index.less';

interface Iprops {
  disabled?: boolean;
  fieldType?: FieldType;
  allDataUnitsWithTags?: DataUnitBaseVO[]; // 所有数据单元
  configuredDataUnits?: DataUnitBaseVO[]; // 配置的数据单元
  calcFields?: any[];
  conditionGroups?: any;
  extConfig?: any;
  onOk?: (values) => void;
  onCancel?: () => void;
}

const TriggerConditionSetting = (props: Iprops) => {
  const {
    disabled = false,
    fieldType,
    allDataUnitsWithTags = [],
    configuredDataUnits = [],
    conditionGroups,
    extConfig,
    calcFields,
    onOk,
    onCancel,
  } = props;
  const [form] = Form.useForm();
  const groupFilterNewConfigWatcher = Form.useWatch('groupFilterNewConfig', form);
  const sameGroupQueryType = Form.useWatch('sameGroupQueryType', form);
  const dataQueryType = Form.useWatch('dataQueryType', form);
  const dataUnitId = Form.useWatch('dataUnitId', form);
  const [currentConditionGroups, setCurrentConditionGroups] = useState<any[]>([]);

  const sameGroupQueryOptions = useMemo(() => {
    return [
      { label: '仅子数据', value: SAME_GROUP_QUERY_TYPE.ONLY_SUB },
      { label: '全部数据', value: SAME_GROUP_QUERY_TYPE.ALL, disabled: dataQueryType === DATA_QUERY_TYPE.SAME || disabled },
    ];
  }, [dataQueryType, disabled]);

  /**
   * 标签条件组设置里条件左侧数据单元标签选项
   * 取当前选中标签所在数据单元的标签
   */
  const tags = useMemo(() => {
    if (dataUnitId) {
      const targetDataUnit = (allDataUnitsWithTags || []).find((t) => t.dataUnitId === dataUnitId);
      return getBeloneDataUnitTags([targetDataUnit], allDataUnitsWithTags);
    }
    return [];
  }, [allDataUnitsWithTags, dataUnitId]);

  /**
   * 筛选条件右侧变量标签选项
   * 包含当前选中标签所在的数据单元，以及配置的主副数据单元的标签
   */
  const canRelateTags = useMemo(() => {
    const mainDataUnitArray = (configuredDataUnits || []).filter((t) => t.mainDataUnit === true);

    if (dataUnitId) {
      if ((mainDataUnitArray || []).some((t) => t.dataUnitId === dataUnitId)) {
        return getBeloneDataUnitTags(mainDataUnitArray, allDataUnitsWithTags);
      }
      const dataUnits = (allDataUnitsWithTags || []).filter((t) => t.dataUnitId === dataUnitId);
      return getBeloneDataUnitTags(dataUnits.concat(mainDataUnitArray), allDataUnitsWithTags);
    }
    return [];
  }, [allDataUnitsWithTags, configuredDataUnits, dataUnitId]);

  const handleGroupFilterNewConfigSwitch = (checked: boolean) => {
    if (!checked) {
      form.setFieldsValue({
        groupFilterNewConfig: null,
      });
      return;
    }

    form.setFieldsValue({
      groupFilterNewConfig: {
        groupTags: [],
        timeTag: [],
      },
    });
  };

  useEffect(() => {
    const defaultExtConfig = getExtConfig(extConfig);

    setCurrentConditionGroups(conditionGroups);
    form.setFieldsValue(defaultExtConfig);
  }, [extConfig, form, conditionGroups]);

  return (
    <Modal
      title="触发条件设置"
      open
      onCancel={onCancel}
      onOk={async () => {
        try {
          const values = await form.validateFields();
          console.log(values, 'values');
          const { sameGroupQueryType, dataQueryType, conditionGroups, sortTags, groupFilterNewConfig, dataUnitId } = values;
          onOk &&
            onOk({
              extConfig: {
                sameGroupQueryType,
                dataQueryType,
                sortTags,
                groupFilterNewConfig,
                dataUnitId,
              },
              conditionGroups,
            });
        } catch (error) {
          console.log(error);
        }
      }}
      size="large"
    >
      <Form form={form} className={styles.dataQuerySetting}>
        <Form.Item
          rules={[{ required: true, message: '请选择数据单元' }]}
          className={styles.ruleItem}
          label={<span className={styles.ruleLabel}>选择数据单元</span>}
          name="dataUnitId"
          required
        >
          <Select
            style={{ width: 520 }}
            disabled={disabled}
            options={allDataUnitsWithTags.map((item) => ({ label: item.name, value: item.dataUnitId }))}
            onChange={() => {
              setCurrentConditionGroups([]);
            }}
          />
        </Form.Item>
        <Form.Item
          className={styles.ruleItem}
          label={
            <span className={styles.ruleLabel}>
              同组数据查询&nbsp;
              <Tooltip title="可选择同组数据显示的数据内容。对数据进行拆分或使用组合标签录入数据，都将产生同组数据。同组数据包括一条主数据与多条子数据。仅子数据（默认）：数据查询时，仅能查询到子数据。全部数据：数据查询时，能同时查询到主、子数据。">
                <QuestionCircleOutlined />
              </Tooltip>
            </span>
          }
          name="sameGroupQueryType"
        >
          <Radio.Group disabled={disabled} options={sameGroupQueryOptions} />
        </Form.Item>
        <Form.Item
          className={styles.ruleItem}
          label={
            <span className={styles.ruleLabel}>
              数据查询方式&nbsp;
              <Tooltip
                title={
                  <div>
                    基于单条数据：在查询数据时，单条数据需要满足所有条件，即可被查询出来。
                    <br />
                    基于同条数据：在查询数据时，同条数据中若有某条数据满足条件组，就算满足条件，即可被查询出来。
                  </div>
                }
              >
                <QuestionCircleOutlined />
              </Tooltip>
            </span>
          }
          name="dataQueryType"
        >
          <Radio.Group disabled={disabled}>
            <Radio value={DATA_QUERY_TYPE.SINGLE}>
              基于单条数据&nbsp;
              <Tooltip title="单条数据概念：基于一个发起数据，产生的后续所有数据，有同一个原始数据id。">
                <QuestionCircleOutlined />
              </Tooltip>
            </Radio>
            <Radio value={DATA_QUERY_TYPE.SAME} disabled={sameGroupQueryType === SAME_GROUP_QUERY_TYPE.ALL || disabled}>
              基于同条数据&nbsp;
              <Tooltip
                styles={{ root: { maxWidth: 400 } }}
                title={
                  <div style={{ height: 260, overflow: 'auto' }}>
                    同条数据概念：对同一条发起数据，进行操作后产生的后续所有数据。
                    <br />
                    <br />
                    使用场景：实现会签（多人同时审批通过）、或签（多人中仅需一人审批通过）审批。由于本系统中有不删除/修改原始数据的硬性设定，多个用户的会签操作，会基于一条发起数据产生多条（同条）数据，需要条件组能对一批数据，按条件组分别进行查询，都查询到相应数据才符合会签场景（都审批过）要求，才进行下一步审批。
                    <br />
                    <br />
                    示例：以财务报销流程为例，假设财务报销流程需要报销人的直属领导与财务经理都审批后，才能进行下一步总经理审批。由于本系统中不删除/修改原始数据，因此直属领导与财务经理审批过报销人的报销发票后，会产生两条分别记录直属领导、财务经理操作的新数据。下一流程总经理审批，需要获取下级都审批通过的数据进行审批。因此需在一次查询中，分别查询到财务经理与直属领导审批通过的数据，都查询到才是满足需要总经理审批的报销。选择此选项后，即可设置两个条件组，分别查询直属领导、财务经理有无审批同意的数据，若都审批同意则满足条件，能被查询出来。
                  </div>
                }
              >
                <QuestionCircleOutlined />
              </Tooltip>
            </Radio>
          </Radio.Group>
        </Form.Item>

        {/* 数据查询条件组 */}
        <div className={styles.ruleTitle} style={{ paddingTop: 8, marginBottom: 12 }}>
          <div className={styles.ruleTitleText}>数据查询条件组</div>
        </div>
        <FilterGroup disabled={disabled} conditionGroups={currentConditionGroups} tags={tags} canRelateTags={canRelateTags} calcFields={calcFields} />

        {
          // 标签独立查询 字段里才会有这个配置
          fieldType && (
            <div className={styles.ruleTitle} style={{ paddingTop: 16 }}>
              <div className={styles.ruleTitleText}>
                标签独立查询&nbsp;
                <Tooltip title="开启后，将该标签的来源于数据独立查询的结果，不依赖表单的数据查询结果。">
                  <QuestionCircleOutlined /> :
                </Tooltip>
              </div>
              <div className={styles.ruleTitleOpt}>
                <Form.Item name="ifDependentQuery" initialValue={true} noStyle>
                  <Switch disabled={fieldType === FieldType.IndependentField || disabled} />
                </Form.Item>
              </div>
            </div>
          )
        }

        {/* 结果排序 */}
        <div className={styles.ruleTitle} style={{ paddingTop: 16, marginBottom: 12 }}>
          <div className={styles.ruleTitleText}>
            结果排序&nbsp;
            <Tooltip
              title={
                <div>
                  优先级以先后顺序为准，第一条优先级最高，第二条次之，以此类推，排序规则必须在前置的排序规则上生效。
                  <br />
                  场景示例：第一条优先级规则相同的数据，将按第二条排序规则进行排序。
                </div>
              }
            >
              <QuestionCircleOutlined /> :
            </Tooltip>
          </div>
        </div>
        <SortTags dataUnitId={dataUnitId} tags={tags} disabled={disabled} />

        {/* 分组筛选最新 */}
        <div className={styles.ruleTitle} style={{ paddingTop: 16, marginBottom: 12 }}>
          <div className={styles.ruleTitleText}>
            分组筛选最新&nbsp;
            <Tooltip title="根据分组标签的值对数据进行分组，按时间标签取最新一条数据">
              <QuestionCircleOutlined /> :
            </Tooltip>
          </div>
          <div className={styles.ruleTitleOpt}>
            <Switch
              checked={!!groupFilterNewConfigWatcher}
              disabled={disabled}
              onChange={(checked) => {
                handleGroupFilterNewConfigSwitch(checked);
              }}
            />
          </div>
        </div>
        <Form.Item name="groupFilterNewConfig" noStyle>
          {!!groupFilterNewConfigWatcher && <GroupFilter dataUnitId={dataUnitId} tags={tags} disabled={disabled} />}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TriggerConditionSetting;
