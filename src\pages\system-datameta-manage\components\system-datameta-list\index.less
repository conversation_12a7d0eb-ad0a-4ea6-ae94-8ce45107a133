.container {
  width: 100%;
  height: 100%;
  display: flex;

  .leftTabs {
    width: 220px;
    height: 100%;
    flex-shrink: 0;
    overflow-y: auto;
    border-right: 1px solid #dae0e8;

    .leftTabItem {
      border-left: 2px solid transparent;
      color: #4e5969;
      padding: 5px 0 5px 15px;
      cursor: pointer;

      &.active {
        border-left: 2px solid #4d7bf6;
        background: #fff;
        color: #4d7bf6;
      }
    }
  }

  .rightContent {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;

    .topWrap {
      padding-bottom: 10px;

      :global {
        .ant-tabs-nav {
          margin-bottom: 0 !important;
        }
      }

      .classifyTabs {
        border-bottom: 1px solid #dae0e8;
        // padding: 8px;
        padding: 0 15px;
      }

      .searchBar {
        width: 200px;
        padding: 10px;
        padding-bottom: 0;
      }
    }

    .contentDetail {
      flex-grow: 1;
      overflow: auto;
      display: grid;
      padding-bottom: 10px;
      gap: 25px 18px;
      grid-auto-rows: 180px;
      grid-template-columns: repeat(auto-fill, minmax(145px, 165px));
      padding-left: 30px;
    }
  }
}

.datasourceItem {
  position: relative;
  width: 160px;
  height: 181px;
  // width: 260px;
  // height: 142px;
  border-radius: 6px;
  // border: 1px solid #e5e6eb;
  padding: 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
  box-shadow: 2px 2px 8px 0 rgba(0, 0, 0, 8%);
  background: #fff;

  .datasourceItemTitleBg {
    position: absolute;
    top: 15px;
    left: -12px;
    right: 12px;
    height: 36px;
    border-radius: 12px 18px 18px 0 / 9px 18px 18px 0;
    background: linear-gradient(to right, #3a68e4 0%, #5e86f9 100%);

    &::before {
      content: '';
      position: absolute;
      bottom: -9px;
      left: 0;
      width: 12px;
      height: 9px;
      background: linear-gradient(to right, #3a68e4 0%, #2d6be1 100%);
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -18px;
      left: 0;
      width: 12px;
      height: 18px;
      border-radius: 12px 0 0 12px / 9px 0 0 9px;
      background-color: #0a36bb;
    }
  }

  .datasourceTop {
    z-index: 1;
    display: flex;
    align-items: center;

    .datasourceIcon {
      img {
        width: 28px;
      }

      width: 42px;
      height: 42px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      flex: 0 0 42px;
    }

    .commonMetaIcon {
      color: white;
      text-align: center;
      line-height: 42px;
      font-family: 'Source Han Sans CN';
      font-weight: bold;
      font-size: 20px;
      font-style: normal;
    }

    .datasourceTitle {
      width: 100%;
      overflow: hidden;

      .nameBox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        margin-top: 8px;
        margin-bottom: 18px;

        .textName {
          font-weight: bold;
          font-size: 14px;
          // color: #1d2129;
          color: #fff;
          line-height: 20px;
          min-height: 20px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }

        // .tagBox {
        //   display: inline-flex;
        //   align-items: center;
        //   line-height: 1;

        //   .tag {
        //     padding: 2px 1px;
        //     display: flex;
        //     align-items: center;
        //     justify-content: center;
        //     margin-right: 6px;
        //     font-size: 12px;
        //     border-radius: 2px;
        //     border: 1px solid transparent;
        //   }
        // }
      }

      .createdTime {
        font-weight: normal;
        font-size: 12px;
        color: #4e5969;
        // line-height: 14px;
        line-height: 20px;
        margin-top: 5px;
      }
    }
  }

  .descDiv {
    font-weight: 400;
    font-size: 12px;
    color: #86909c;
    line-height: 14px;

    .desc {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    .tagsDiv {
      gap: 10px;
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .left {
        display: flex;
        align-items: center;

        .tagNum {
          margin-right: 10px;
          width: 60px;
          height: 25px;
          background: #e8ffea;
          border-radius: 2px;
          font-weight: 400;
          font-size: 12px;
          color: #00b42a;
          line-height: 14px;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  .delete {
    display: none;
  }

  .viewDataIcon {
    display: none;
    position: absolute;
    top: 10px;
    right: 10px;
  }

  &:hover {
    box-shadow: 0 2px 15px 2px rgba(0, 0, 0, 10%);
    border-radius: 3px;
    border: 1px solid #4d7bf6;

    .delete {
      display: block;
    }

    .viewDataIcon {
      display: block;
    }
  }
}

.header {
  display: flex;
  align-items: center;
  // height: 68px;
  // line-height: 68px;
  padding: 10px;

  .title {
    font-weight: bold;
    margin-left: 10px;
  }
}

.postInfo {
  padding-left: 8px;
  padding-right: 10px;
  margin-bottom: 10px;
}
