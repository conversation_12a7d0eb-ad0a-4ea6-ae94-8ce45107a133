import MetaDynamicItem from '@/components/meta-dynamic-item';
import { UseIn } from '@/components/meta-dynamic-item/const';
import { Form, Radio } from '@gwy/components-web';
import { memo, useContext } from 'react';
import { DataSplitTableContext } from '../..';
import styles from '../../index.less';
export enum SplitType {
  SPLIT = 0,
  APPROVE = 1,
  REJECT = 2,
}
export enum ForceDisable {
  TRUE = 1,
  FALSE = 0,
}
export enum SUBMIT_BUTTON_TYPE {
  LAUNCH = 'LAUNCH', // 发起
  AGREE = 'AGREE', // 同意
  REFUSE = 'REFUSE', // 拒绝
  RETURN = 'RETURN', // 退回
  END = 'END', // 完结
  // AGREE_ALL = 70, // 全部同意
  // REFUSE_ALL = 80, // 全部拒绝
  // RETURN_ALL = 90, // 全部退回
}
const EditCell = memo<any>((props) => {
  const { dataIndex, record, tag, rowIndex, children, isButtonType, editable, splitType, ...otherProps } = props || {};
  console.log('editable------------', editable);
  const {
    form,
    tags,
    postId,
    orgId,
    dataId,
    isPlatform,
    orgListIndex,
    ruleRowsIndex,
    authDataRules,
    controlLists,
    setLoading,
    forceUpdate,
    reviewItemKeyMap,
    canInputItemKeyMap,
  } = useContext(DataSplitTableContext);

  if (isButtonType) {
    return (
      <td {...otherProps}>
        {splitType !== SplitType.APPROVE && <Form.Item name={[rowIndex, 'splitMainData']} initialValue={rowIndex === 0} hidden />}
        {(rowIndex !== 0 || splitType === SplitType.APPROVE) && (
          <Form.Item name={[rowIndex, 'buttonType']} rules={[{ required: true, message: '请选择' }]}>
            <Radio.Group>
              <Radio value={SUBMIT_BUTTON_TYPE.AGREE}>同意</Radio>
              <Radio value={SUBMIT_BUTTON_TYPE.REFUSE}>拒绝</Radio>
              <Radio value={SUBMIT_BUTTON_TYPE.RETURN}>退回</Radio>
            </Radio.Group>
          </Form.Item>
        )}
      </td>
    );
  }
  if (!dataIndex) {
    return <td {...otherProps}>{children}</td>;
  }
  const { tagId, dataUnitId } = tag || {};
  const name = `${dataUnitId}-${tagId}`;

  return (
    <td {...otherProps}>
      {!editable ? (
        children
      ) : (
        // children
        // 222
        <div className={styles.cellWrapper}>
          {/* <Form.Item name={[rowIndex, name, 'datasourceId']} hidden />
                        <Form.Item name={[rowIndex, name, 'datasourceDataRuleV2Id']} hidden />
                        <Form.Item name={[rowIndex, name, 'targetId']} hidden />
                        <Form.Item name={[rowIndex, name, 'dataId']} hidden /> */}
          <MetaDynamicItem useIn={UseIn.App} tag={tag} field={tag} prefix={[rowIndex]} isPreview={rowIndex === 0} hiddenLabel={true} />
        </div>
      )}
    </td>
  );
});

export default EditCell;
