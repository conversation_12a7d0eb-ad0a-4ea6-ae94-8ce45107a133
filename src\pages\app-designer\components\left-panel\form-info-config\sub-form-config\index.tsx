import DataQuerySetting from '@/components/data-query-setting';
import { AppDesignerContext } from '@/pages/app-designer/context';
import { getOrgDataUnits } from '@/services/org-data-unit';
import { EditOutlined } from '@ant-design/icons';
import { Form, Switch } from '@gwy/components-web';
import { Button, Select } from 'antd';
import { useContext, useEffect, useMemo, useState } from 'react';

const SubFormConfig = () => {
  const {
    post,
    groupFlag,
    setGroupFlag,
    aggregationMainDataUnits,
    setAggregationMainDataUnits,
    aggregationMainDataUnitId,
    setAggregationMainDataUnitId,
    setCurrSubDataUnitId,
    configuredDataUnits,
    setConfiguredDataUnits,
    allConfiguredDataUnitsWithTags,
    aggregationSubDataUnits,
    setAggregationSubDataUnits,
    setConfiguredFields,
    setCombineTagConfig,
    setTableList,
    calcFields,
  } = useContext(AppDesignerContext);

  const dataUnitsOptions = aggregationMainDataUnits.map((item) => ({ label: item.name, value: item.dataUnitId }));
  const aggregationMainDataUnit = aggregationMainDataUnits.find((item) => item.dataUnitId === aggregationMainDataUnitId);
  const configuredDataUnit = configuredDataUnits?.[0];

  const [openFilter, setOpenFilter] = useState({
    open: false,
    dataUnit: null,
  });

  // 获取组织下的数据单元
  const fetchOrgDataUnits = async () => {
    const data = await getOrgDataUnits({
      orgId: post.orgId,
      subType: 'NORMAL',
    });
    setAggregationMainDataUnits(data);
  };

  useEffect(() => {
    fetchOrgDataUnits();
  }, []);

  useEffect(() => {
    if (!aggregationMainDataUnitId) return;
    (async () => {
      const data = await getOrgDataUnits({
        orgId: post.orgId,
        parentDataUnitId: aggregationMainDataUnitId,
      });
      setAggregationSubDataUnits(data);
    })();
  }, [aggregationMainDataUnitId]);

  // 当前数据单元的标签，变量可关联的标签
  const [unitIdtoTags, canRelateTags] = useMemo(() => {
    const tags =
      allConfiguredDataUnitsWithTags
        ?.find((dataUnit) => dataUnit.dataUnitId === aggregationMainDataUnit?.dataUnitId)
        ?.tagList.map((tag) => ({
          ...tag,
          belongDataUnitId: aggregationMainDataUnit.dataUnitId,
          belongDataUnitName: aggregationMainDataUnit.name,
        })) || [];

    return [tags, tags];
  }, [openFilter.dataUnit, allConfiguredDataUnitsWithTags]);

  return (
    <div>
      <Form.Item label="数据聚合查询" wrapperCol={{ style: { height: 0 } }} style={{ marginBottom: groupFlag ? 0 : undefined }}>
        <Switch
          checked={groupFlag}
          onChange={(checked) => {
            setGroupFlag(checked);
            if (checked) {
              setConfiguredDataUnits([{}]);
            }
          }}
          style={{ position: 'absolute', top: -24, right: 0 }}
        />
      </Form.Item>
      {groupFlag && (
        <>
          <Select
            style={{ width: '100%' }}
            showSearch
            optionFilterProp="label"
            options={dataUnitsOptions}
            value={aggregationMainDataUnitId}
            onChange={(value) => {
              setAggregationMainDataUnitId(value);
              setCurrSubDataUnitId(value);
              setConfiguredDataUnits([{ dataUnitId: value, name: aggregationMainDataUnits.find((item) => item.dataUnitId === value)?.name }]);
              setConfiguredFields([]);
            }}
            placeholder="请选择"
          />
          <Form.Item layout="horizontal" style={{ marginBottom: 0 }}>
            <Button
              type="link"
              icon={<EditOutlined />}
              style={{ padding: 0 }}
              onClick={() =>
                setOpenFilter((prev) => ({
                  ...prev,
                  open: true,
                  dataUnit: configuredDataUnit,
                }))
              }
            >
              {configuredDataUnit?.conditionGroups?.length > 0 ? `${configuredDataUnit?.conditionGroups?.length}个条件` : '数据查询规则'}
            </Button>
          </Form.Item>
        </>
      )}

      {openFilter.open && (
        <DataQuerySetting
          groupFlag={true}
          aggregationSubDataUnits={aggregationSubDataUnits}
          dataUnitName={aggregationMainDataUnit?.name}
          dataUnitId={aggregationMainDataUnit?.dataUnitId}
          conditionGroups={openFilter.dataUnit?.conditionGroups}
          extConfig={openFilter.dataUnit?.extConfig}
          tags={unitIdtoTags}
          canRelateTags={canRelateTags}
          calcFields={calcFields}
          onOk={(values) => {
            console.log(values, 'values-----');
            setOpenFilter({
              open: false,
              dataUnit: null,
            });
            setConfiguredDataUnits(
              configuredDataUnits.map((item) => {
                if (item.dataUnitId === openFilter.dataUnit.dataUnitId) {
                  return {
                    ...item,
                    ...values,
                  };
                }
                return item;
              }),
            );
          }}
          onCancel={() => {
            setOpenFilter({
              open: false,
              dataUnit: null,
            });
          }}
        />
      )}
    </div>
  );
};

export default SubFormConfig;
