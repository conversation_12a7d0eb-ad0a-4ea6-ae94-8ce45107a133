import { CodeType, codeTypeOptions, CommonCode, commonCodeOptions, CustomCode, customCodeOptions } from '@/const/metadata';
import { Checkbox, Form, FormInstance, Radio } from '@gwy/components-web';
import { useContext, useMemo } from 'react';
import { MetaDataContext } from '../../context';
import CodeRules, { GROUP_LIST_KEY, RULE_LIST_KEY } from './code-rules';

export const CodeDTOKey = 'metaDataCodeDTO';

type Props = {
  form?: FormInstance;
};

const CodeConfig = ({ form }: Props) => {
  const type = Form.useWatch([CodeDTOKey, 'type'], form);
  const elementTypeList = Form.useWatch([CodeDTOKey, 'elementTypeList'], form);

  const { metaDataConfigApproved, hasApproved, disabled } = useContext(MetaDataContext);

  const _customCodeOptions = useMemo(() => {
    return customCodeOptions.map((item) => {
      return {
        ...item,
        disabled: disabled || metaDataConfigApproved?.[CodeDTOKey]?.elementTypeList?.some((c) => c === item.value),
      };
    });
  }, [customCodeOptions]);

  // 修改编码范围，需要清空对应的配置
  const handleElementTypeListChange = (checkedValue: CustomCode[]) => {
    const removed = elementTypeList.filter((item) => !checkedValue.includes(item));
    if (removed.length > 0) {
      const groupList = form.getFieldValue([CodeDTOKey, GROUP_LIST_KEY]) || [];
      for (const [groupIndex, group] of Object.entries(groupList)) {
        for (const [ruleIndex, rule] of group[RULE_LIST_KEY].entries()) {
          if (removed.includes(rule.type)) {
            form.setFieldValue([CodeDTOKey, GROUP_LIST_KEY, groupIndex, RULE_LIST_KEY, ruleIndex], {});
          }
        }
      }
    }
  };

  return (
    <div>
      <Form.Item label="编码包含" name={[CodeDTOKey, 'type']} rules={[{ required: true, message: '请选择' }]} initialValue={CodeType.custom}>
        <Radio.Group
          disabled={hasApproved || disabled}
          options={codeTypeOptions}
          onChange={(e) => {
            if (e.target.value === CodeType.custom) {
              form.setFieldValue(
                [CodeDTOKey, 'elementTypeList'],
                [CustomCode.chinese, CustomCode.number, CustomCode.english_lower, CustomCode.english_upper, CustomCode.date],
              );
            } else {
              form.setFieldValue([CodeDTOKey, 'elementTypeList'], [CommonCode.idCard, CommonCode.phone, CommonCode.tel, CommonCode.enterprise_code]);
              form.setFieldValue([CodeDTOKey, GROUP_LIST_KEY], []);
            }
          }}
        />
      </Form.Item>

      {type === CodeType.custom && (
        <>
          <Form.Item
            label="自定义编码包含"
            name={[CodeDTOKey, 'elementTypeList']}
            rules={[{ required: true, message: '请选择' }]}
            initialValue={[CustomCode.chinese, CustomCode.number, CustomCode.english_lower, CustomCode.english_upper, CustomCode.date]}
          >
            <Checkbox.Group options={_customCodeOptions} onChange={handleElementTypeListChange} />
          </Form.Item>

          <CodeRules form={form} />
        </>
      )}

      {type === CodeType.normal && (
        <Form.Item
          label="常用编码包含"
          name={[CodeDTOKey, 'elementTypeList']}
          rules={[{ required: true, message: '请选择' }]}
          initialValue={[CommonCode.idCard, CommonCode.phone, CommonCode.tel, CommonCode.enterprise_code]}
        >
          <Checkbox.Group options={commonCodeOptions} />
        </Form.Item>
      )}
    </div>
  );
};

export default CodeConfig;
