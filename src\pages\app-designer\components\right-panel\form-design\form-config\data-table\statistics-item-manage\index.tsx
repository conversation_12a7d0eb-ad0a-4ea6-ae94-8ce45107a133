import { AppDesignerContext } from '@/pages/app-designer/context';
import { FormTableConfig } from '@/types/form-table';
import { StatisticalItem } from '@/types/statistic-item';
import { CloseCircleFilled, PlusCircleOutlined } from '@ant-design/icons';
import { Button, ConfigProvider, Form, TextEllipsisTooltip } from '@gwy/components-web';
import { useContext, useState } from 'react';
import styles from './index.less';
import StatisticalModal from './statistical-modal';

export type StatisticsItemManageProps = {
  table?: FormTableConfig;
};

const StatisticsItemManage = ({ table }: StatisticsItemManageProps) => {
  const { tableList, setTableList } = useContext(AppDesignerContext);

  // 统计项弹框
  const [statisticalModal, setStatisticalModal] = useState<{
    show: boolean;
    record?: StatisticalItem;
  }>({
    show: false,
  });

  return (
    <div className={styles.container}>
      <Form.Item
        label={
          <div className={styles.header}>
            <span>统计项</span>
            <Button type="link" style={{ padding: 0 }} onClick={() => setStatisticalModal({ show: true })}>
              <PlusCircleOutlined />
              添加统计项
            </Button>
          </div>
        }
      >
        {table.statisticList?.length > 0 && (
          <div className={styles.list}>
            {table.statisticList?.map((item) => (
              <div className={styles.item} key={item.statisticId} onClick={() => setStatisticalModal({ show: true, record: item })}>
                <TextEllipsisTooltip className={styles.name} text={item.statisticName} />
                <CloseCircleFilled
                  className={styles.delete}
                  onClick={(e) => {
                    e.stopPropagation();
                    setTableList(
                      tableList.map((t) => {
                        if (t.id === table.id) {
                          return {
                            ...t,
                            statisticList: t.statisticList.filter((s) => s.statisticId !== item.statisticId),
                          };
                        }
                        return t;
                      }),
                    );
                  }}
                />
              </div>
            ))}
          </div>
        )}
      </Form.Item>

      {statisticalModal.show && (
        <ConfigProvider theme={{ token: { fontSize: 14 } }}>
          <StatisticalModal
            table={table}
            record={statisticalModal.record}
            onCancel={() => setStatisticalModal({ show: false })}
            onOk={(values) => {
              setStatisticalModal({ show: false });
              if (statisticalModal.record) {
                // 编辑
                setTableList(
                  tableList.map((t) => {
                    if (t.id === table.id) {
                      return {
                        ...t,
                        statisticList: t.statisticList.map((s) => {
                          if (s.statisticId === statisticalModal.record?.statisticId) {
                            return values;
                          }
                          return s;
                        }),
                      };
                    }
                    return t;
                  }),
                );
              } else {
                // 新增
                setTableList(
                  tableList.map((t) => {
                    if (t.id === table.id) {
                      return {
                        ...t,
                        statisticList: [...(t.statisticList || []), values],
                      };
                    }
                    return t;
                  }),
                );
              }
            }}
          />
        </ConfigProvider>
      )}
    </div>
  );
};

export default StatisticsItemManage;
