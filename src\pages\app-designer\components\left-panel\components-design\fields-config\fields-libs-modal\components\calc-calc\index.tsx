import { memo } from 'react';
import FormItemCondition from './components/form-item-condition';
import styles from './index.less';

interface IProps {
  disabled?: boolean;
}

const CalcCalc = memo<IProps>((props) => {
  return (
    <div className={styles.container}>
      <div className={styles.title}>计算规则</div>
      <div className={styles.content}>
        <FormItemCondition {...props} />
      </div>
    </div>
  );
});

export default CalcCalc;
