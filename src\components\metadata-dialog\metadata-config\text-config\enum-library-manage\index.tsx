import { deleteDict, enableDict, getDictPage } from '@/services/datasource';
import { formatDateTime } from '@/utils';
import { PlusOutlined } from '@ant-design/icons';
import { Button, DatePicker, Form, Input, message, Modal, Switch, Table, TableColumnsType } from '@gwy/components-web';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash-es';
import { useEffect, useState } from 'react';
import { OPERATE_TYPE } from '../tree-by-list';
import styles from './index.less';
import OperateModal from './operate-modal';

const { RangePicker } = DatePicker;

type Props = {
  open?: boolean;
  onClose?: () => void;
};

const BASIC_SEARCH_PARAMS = {
  pageSize: 20,
  currentPage: 1,
};

const DictManage = ({ open, onClose }: Props) => {
  const [form] = Form.useForm();
  const [searchParams, setSearchParams] = useState<{
    name?: string;
    createDateStart?: string;
    createDateEnd?: string;
    updateDateStart?: string;
    updateDateEnd?: string;
    pageSize: number;
    currentPage: number;
  }>(BASIC_SEARCH_PARAMS);

  const [dataSource, setDataSource] = useState([]);
  const [total, setTotal] = useState(0);

  const [showOperateModal, setShowOperateModal] = useState({
    open: false,
    type: OPERATE_TYPE.ADD,
    currentItem: null,
  });

  const fetchDictPage = async () => {
    const { records, total } = await getDictPage(searchParams);
    setDataSource(records);
    setTotal(total);
  };

  useEffect(() => {
    fetchDictPage();
  }, [searchParams]);

  const handleSearch = () => {
    const values = form.getFieldsValue();
    const { createDate = [], updateDate = [], name } = values;
    let createDateStart, createDateEnd, updateDateStart, updateDateEnd;
    if (!isEmpty(createDate)) {
      createDateStart = dayjs(createDate[0]).format('YYYY-MM-DD');
      createDateEnd = dayjs(createDate[1]).format('YYYY-MM-DD');
    }
    if (!isEmpty(updateDate)) {
      updateDateStart = dayjs(updateDate[0]).format('YYYY-MM-DD');
      updateDateEnd = dayjs(updateDate[1]).format('YYYY-MM-DD');
    }
    setSearchParams((n) => ({
      ...n,
      name,
      createDateStart,
      createDateEnd,
      updateDateStart,
      updateDateEnd,
      currentPage: 1,
    }));
  };

  const columns: TableColumnsType<any> = [
    {
      title: '序号',
      width: 60,
      dataIndex: 'idx',
      render: (value, record, index) => index + 1,
    },
    {
      title: '名称',
      width: 'auto',
      dataIndex: 'name',
    },
    {
      title: '枚举数量',
      width: 85,
      dataIndex: 'itemNum',
    },
    {
      title: '状态',
      width: 100,
      dataIndex: 'enable',
      render: (value, _r) => {
        return (
          <Switch
            checked={value}
            onChange={() => {
              Modal.confirm({
                title: `确定${value ? '关闭' : '开启'}该枚举库吗?`,
                centered: true,
                async onOk() {
                  await enableDict(_r.dictId, !value).catch((error) => error);
                  message.success('操作成功');
                  fetchDictPage();
                },
                onCancel() {},
              });
            }}
          />
        );
      },
    },
    {
      title: '创建时间',
      width: 'auto',
      dataIndex: 'createTime',
      sorter: (a, b) => {
        const aTime = new Date(a.createTime).getTime();
        const bTime = new Date(b.createTime).getTime();
        return aTime - bTime;
      },
      render: (value) => {
        return <div>{value ? formatDateTime(value) : '-'}</div>;
      },
    },
    {
      title: '更新时间',
      width: 'auto',
      dataIndex: 'updateTime',
      sorter: (a, b) => {
        const aTime = new Date(a.updateTime).getTime();
        const bTime = new Date(b.updateTime).getTime();
        return aTime - bTime;
      },
      render: (value) => {
        return <div>{value ? formatDateTime(value) : '-'}</div>;
      },
    },
    {
      title: '操作',
      width: 'auto',
      dataIndex: 'actions',
      render: (value, record) => (
        <div className={styles.btnWrapper}>
          <Button
            color="primary"
            size="small"
            variant="link"
            onClick={() => {
              setShowOperateModal({
                open: true,
                type: OPERATE_TYPE.VIEW,
                currentItem: record,
              });
            }}
          >
            查看
          </Button>
          <Button
            color="primary"
            size="small"
            variant="link"
            onClick={() => {
              setShowOperateModal({
                open: true,
                type: OPERATE_TYPE.EDIT,
                currentItem: record,
              });
            }}
          >
            编辑
          </Button>
          <Button
            onClick={() => {
              Modal.confirm({
                title: `确定要删除该枚举库吗?`,
                centered: true,
                async onOk() {
                  await deleteDict(record.dictId).catch((error) => error);
                  message.success('操作成功');
                  fetchDictPage();
                },
              });
            }}
            color="primary"
            size="small"
            variant="link"
          >
            删除
          </Button>
        </div>
      ),
    },
  ];
  const handleAdd = () => {
    setShowOperateModal({
      open: true,
      type: OPERATE_TYPE.ADD,
      currentItem: null,
    });
  };
  const onCloseOperateModal = () => {
    setShowOperateModal({
      open: false,
      type: undefined,
      currentItem: null,
    });
  };

  return (
    <Modal title="枚举库管理" footer={null} open={open} onCancel={onClose} width={980} bodyProps={{ styles: { height: '600px' } }}>
      <div className={styles.container}>
        <div>
          <Form form={form} layout="inline" style={{ margin: '0  0 16px 0', flexWrap: 'nowrap' }}>
            <Form.Item label="名称" name="name">
              <Input placeholder="请输入关键词" allowClear className={styles.itemInput} />
            </Form.Item>
            <Form.Item label="创建日期" name="createDate">
              <RangePicker format="YYYY-MM-DD" className={styles.itemRange} />
            </Form.Item>
            <Form.Item label="更新日期" name="updateDate">
              <RangePicker format="YYYY-MM-DD" className={styles.itemRange} />
            </Form.Item>
            <div className={styles.searchForm}>
              <Button type="primary" ghost onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleAdd} icon={<PlusOutlined />} className={styles.btnAdd} type="primary">
                新增
              </Button>
            </div>
          </Form>
        </div>
        <Table
          rowKey="dictId"
          bordered
          size="small"
          columns={columns}
          dataSource={dataSource}
          scroll={{ y: 450, x: '100%' }}
          pagination={{
            showSizeChanger: true,
            current: searchParams.currentPage,
            pageSize: searchParams.pageSize,
            total,
            onChange: (page, pageSize) => {
              setSearchParams((pre) => ({
                ...pre,
                currentPage: page,
                pageSize,
              }));
            },
          }}
        />
        {showOperateModal.open && (
          <OperateModal
            disabled={showOperateModal.type === OPERATE_TYPE.VIEW}
            modalConfig={showOperateModal.currentItem}
            tagOprateType={showOperateModal.type}
            onCancel={onCloseOperateModal}
            onOk={() => {
              onCloseOperateModal();
              fetchDictPage();
            }}
          />
        )}
      </div>
    </Modal>
  );
};

export default DictManage;
