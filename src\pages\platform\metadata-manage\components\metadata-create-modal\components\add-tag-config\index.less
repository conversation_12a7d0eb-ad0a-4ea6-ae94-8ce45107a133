.tagList {
  // padding-left: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .filterWrapper {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .right {
      .sortBar {
        display: flex;
        align-items: center;
        justify-content: space-around;
        height: 22px;

        .sortItem {
          display: flex;
          align-items: center;
          justify-content: space-around;
          padding: 0 16px;
          line-height: 1;

          &:not(:first-child) {
            border-left: 1px solid #dfdfdf;
          }
        }
      }
    }
  }

  .tableWrapper {
    flex-grow: 1;
    overflow: auto;

    :global {
      .ant-table-tbody td {
        height: 1px;
      }
    }
  }

  .tagBox {
    display: inline-flex;
    align-items: center;
    line-height: 1;

    .tag {
      padding: 2px 1px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 0;
      font-size: 12px;
      border-radius: 2px;
      border: 1px solid transparent;
    }
  }

  .uniquetagWrapper {
    display: flex;
    height: 100%;
    width: 160px;
    align-items: center;

    .uniquetagIcon {
      margin-left: 4px;
      font-size: 16px;
      color: #f6c044;
    }
  }

  /* 使用clip-path创建三角形 */
  .clipTriangleWrapper {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;

    .clipTriangle {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 26px;
      height: 26px;
      font-size: 12px;
      text-align: left;
      background: #2ed573;
      clip-path: polygon(0 0, 0 100%, 100% 0);

      .clipLabel {
        display: inline-block;
        margin-left: 2px;
        font-size: 12px;
        scale: 0.9;
        color: #fff;
      }
    }
  }

  .btnWrapper {
    button {
      padding: 0 12px 0 0;
    }
  }
}

.modal {
  :global {
    .ant-modal-content {
      padding: 0;
    }

    .gwy-ui-modal-body {
      height: 100%;
      overflow: visible !important;

      .gwy-ui-modal-content {
        max-height: unset;
      }
    }
  }
}

.detailPage {
  height: 100%;
  background-color: #dbeafb;
}

.diffIcon {
  cursor: pointer;
  margin-left: 4px;
  color: #ff7d00;
}

:global {
  :where(.css-dev-only-do-not-override-drsnig).ant-tour .ant-tour-inner {
    min-width: 230px;
    width: min-content;
  }
}
