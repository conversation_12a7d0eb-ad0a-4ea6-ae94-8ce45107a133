.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footerTips {
  display: flex;
  align-items: center;
  overflow: auto;
}

.content {
  height: 100%;
  width: 100%;
}

.popover {
  width: 340px;

  .popConTitle {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
    color: #1d2129;
  }

  .popConBody {
    .tips {
      margin-bottom: 8px;
      font-size: 12px;
      color: #4e5969;
    }
  }

  .popFooter {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 16px;
  }
}
