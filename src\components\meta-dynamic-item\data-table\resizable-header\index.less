.resizableHeader {
  :global {
    // 可拖拽表头样式 ---------start
    .react-resizable {
      position: relative;
    }

    .react-resizable-handle {
      position: absolute;
      width: 10px;
      height: 10px;

      &::after {
        position: absolute;
        bottom: 1px;
        right: 1px;
        content: '';
        z-index: 1;
        border: 4px solid transparent;
        border-right-color: #bdbdbd;
        border-bottom-color: #bdbdbd;
      }
    }

    .react-resizable-handle-sw {
      bottom: 0;
      left: 0;
      cursor: sw-resize;
      transform: rotate(90deg);
    }

    .react-resizable-handle-se {
      bottom: 0;
      right: 0;
      cursor: se-resize;
    }

    .react-resizable-handle-nw {
      top: 0;
      left: 0;
      cursor: nw-resize;
      transform: rotate(180deg);
    }

    .react-resizable-handle-ne {
      top: 0;
      right: 0;
      cursor: ne-resize;
      transform: rotate(270deg);
    }

    .react-resizable-handle-w,
    .react-resizable-handle-e {
      top: 50%;
      margin-top: -10px;
      cursor: ew-resize;
    }

    .react-resizable-handle-w {
      left: 0;
      transform: rotate(135deg);
    }

    .react-resizable-handle-e {
      right: 0;
      transform: rotate(315deg);
    }

    .react-resizable-handle-n,
    .react-resizable-handle-s {
      left: 50%;
      margin-left: -10px;
      cursor: ns-resize;
    }

    .react-resizable-handle-n {
      top: 0;
      transform: rotate(225deg);
    }

    .react-resizable-handle-s {
      bottom: 0;
      transform: rotate(45deg);
    }

    // 可拖拽表头样式 ---------end
  }
}
