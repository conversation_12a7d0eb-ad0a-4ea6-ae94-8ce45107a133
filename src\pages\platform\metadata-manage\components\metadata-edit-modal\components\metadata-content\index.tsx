// import Empty from '@/components/empty';
import AddTagConfig from '@/pages/platform/metadata-manage/components/metadata-create-modal/components/add-tag-config';
import { systemDatametaAPI } from '@/services';
import { getVeriosns } from '@/services/datasource';
import { Empty, Tabs } from '@gwy/components-web';
import { forwardRef, ForwardRefRenderFunction, useEffect, useImperativeHandle, useRef, useState } from 'react';
import AddTagConfigLight from '../../../metadata-create-modal/components/add-tag-config-light';
import EditBasicInfo from '../edit-basicinfo';
import HistoryVersion from '../history-version';
import styles from './index.less';

type Iprops = {
  operateType: 'add' | 'edit' | 'approve' | 'view';
  dataSourceType: 'NORMAL' | 'GENERAL';
  editData: any;
  compareData?: any;
  versions?: any[];
  dataUnit?: any;
  isOrgMeta?: boolean;
  incompleteTag?: any;
  onCompleteTag?: () => void;
};
type IRef = any;
const MetaDataContent: ForwardRefRenderFunction<IRef, Iprops> = (props, ref) => {
  const { operateType, dataSourceType, editData, compareData, versions, dataUnit, isOrgMeta, incompleteTag, onCompleteTag } = props;
  const [metaTabs, setMetaTabs] = useState([
    { key: 1, title: '基础信息' },
    { key: 2, title: '标签配置' },
    { key: 3, title: '高级配置' },
    { key: 4, title: '历史版本' },
  ]);
  const [activeKey, setActiveKey] = useState<string>(incompleteTag ? '2' : '1');
  const tabBasicRef = useRef(null);
  const tabTagConfRef = useRef(null);
  const tabTagConfLightRef = useRef(null);

  useImperativeHandle(ref, () => ({
    getValues: async () => {
      let tagsLightValues = {};
      let tagsValues = {};
      const basicValues = await tabBasicRef.current?.getValues();
      if (tabTagConfLightRef.current) {
        tagsLightValues = await tabTagConfLightRef.current?.getValues();
      }
      if (tabTagConfRef.current) {
        tagsValues = await tabTagConfRef.current?.getValues();
      }

      return {
        basicValues,
        tagsValues,
        tagsLightValues,
      };
    },
  }));
  const [historyVerions, setHistoryVersions] = useState([]);

  useEffect(() => {
    // 轻量数据单元，只有一个版本
    if (editData?.lightDataUnitId) {
      setHistoryVersions([editData]);
      return;
    }
    if (isOrgMeta) {
      systemDatametaAPI.getOrgHistoryVersionLists(editData?.dataUnitId).then((data) => {
        setHistoryVersions(data || []);
      });
      return;
    }

    if (editData?.dataUnitId) {
      getVeriosns(editData?.dataUnitId).then((data) => {
        setHistoryVersions(data || []);
      });
    }
  }, [editData]);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        {/* <TabsHeader tabs={metaTabs} activeKey={activeKey} onTabChange={(key) => setActiveKey(key)} /> */}
        <Tabs
          type="card"
          size="small"
          activeKey={activeKey}
          onChange={(key) => setActiveKey(key)}
          items={[
            { key: '1', label: '基础信息' },
            { key: '2', label: '标签配置' },
            { key: '3', label: '高级配置' },
            !isOrgMeta && { key: '4', label: '历史版本' },
          ].filter(Boolean)}
        />
      </div>
      <div className={styles.content}>
        <div
          style={{
            display: activeKey !== '1' ? 'none' : '',
          }}
        >
          <EditBasicInfo
            ref={tabBasicRef}
            operateType={operateType}
            dataSourceType={dataSourceType}
            editData={editData}
            compareData={compareData}
            isOrgMeta={isOrgMeta}
          />
        </div>
        <div
          style={{
            display: activeKey !== '2' ? 'none' : '',
            height: '100%',
          }}
        >
          {!dataUnit?.light && (
            <AddTagConfig
              ref={tabTagConfRef}
              initialValue={[]}
              operateType={operateType}
              currentVersionId={editData?.dataUnitVersionId}
              preVersionId={editData?.preVersionId}
              isOrgMeta={isOrgMeta}
              incompleteTag={incompleteTag}
              onCompleteTag={onCompleteTag}
            />
          )}

          {dataUnit?.light && (
            <AddTagConfigLight
              ref={tabTagConfLightRef}
              currentVersionId={editData?.dataUnitVersionId}
              dataUnit={dataUnit}
              operateType={operateType}
            />
          )}
        </div>

        <div
          style={{
            display: activeKey !== '3' ? 'none' : '',
            height: '100%',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 'calc(100% - 60px)' }}>
            <Empty
              description="暂无配置"
              style={{
                height: '200px',
              }}
            />
          </div>
        </div>
        <div
          style={{
            display: activeKey !== '4' ? 'none' : '',
          }}
        >
          {historyVerions.length > 0 ? (
            <HistoryVersion
              timeLineList={historyVerions}
              clickFun={(value) => {
                console.log(value);
              }}
            />
          ) : (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 'calc(100% - 60px)' }}>
              <Empty
                description="暂无配置"
                style={{
                  height: '200px',
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default forwardRef(MetaDataContent);
