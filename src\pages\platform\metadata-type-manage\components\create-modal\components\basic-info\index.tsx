import { Col, Form, Input, Row } from '@gwy/components-web';
import { forwardRef, ForwardRefRenderFunction, useEffect, useImperativeHandle } from 'react';

type IRef = object;
enum OPERATE_TYPE {
  EDIT = 'edit',
  ADD = 'add',
  VIEW = 'view',
  APPROVE = 'approve',
}
type Iprops = {
  initialValue: any;
  dataSourceType: 'NORMAL' | 'GENERAL';
  operateType?: 'add' | 'edit' | 'approve' | 'view';
};
const BasicInfo: ForwardRefRenderFunction<IRef, Iprops> = (props: Iprops, ref) => {
  const { initialValue, operateType } = props;
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    getValues: async () => {
      try {
        const values = await form.validateFields();
        return {
          ...(initialValue || {}),
          ...(values || {}),
        };
      } catch (e: any) {
        const formData = form.getFieldsValue();
        const { name } = formData;
        if (!name) {
          return Promise.reject('类型名称未填写，请填写后再次提交');
        }
        const { errorFields } = e || {};
        if (errorFields?.length > 0 && Array.isArray(errorFields)) {
          return Promise.reject(errorFields[0].errors[0]);
        }
        return Promise.reject(e);
      }
    },
  }));

  useEffect(() => {
    console.log(initialValue, 'initialValue');
    if (initialValue) {
      form.setFieldsValue(initialValue);
    }
  }, [initialValue]);

  return (
    <div>
      <Form form={form} labelCol={{ style: { width: '110px' } }} wrapperCol={{ style: { width: 'calc(100% - 110px)' } }}>
        <Row gutter={24}>
          <Col span={12}>
            {operateType === OPERATE_TYPE.ADD ? (
              <Form.Item name="name" label="类型名称" rules={[{ required: true, message: '请输入类型名称' }]}>
                <Input placeholder="请输入类型名称" maxLength={20} />
              </Form.Item>
            ) : (
              <Form.Item label="类型名称">
                <span className="ant-form-text">{initialValue?.name}</span>
              </Form.Item>
            )}
          </Col>

          <Col span={24}>
            {operateType === OPERATE_TYPE.ADD ? (
              <Form.Item label="类型描述" name="description">
                <Input.TextArea placeholder="请输入类型描述" rows={4} maxLength={200} />
              </Form.Item>
            ) : (
              <Form.Item label="类型描述" name="description">
                <span className="ant-form-text">{initialValue?.description}</span>
              </Form.Item>
            )}
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default forwardRef(BasicInfo);
